<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CFBundleExecutable</key>
    <string>Electron Framework</string>
    <key>CFBundleIdentifier</key>
    <string>com.github.Electron.framework</string>
    <key>CFBundleName</key>
    <string>Electron Framework</string>
    <key>CFBundlePackageType</key>
    <string>FMWK</string>
    <key>CFBundleVersion</key>
    <string>35.5.1</string>
    <key>DTCompiler</key>
    <string>com.apple.compilers.llvm.clang.1_0</string>
    <key>DTSDKBuild</key>
    <string>24B75</string>
    <key>DTSDKName</key>
    <string>macosx15.1</string>
    <key>DTXcode</key>
    <string>1610</string>
    <key>DTXcodeBuild</key>
    <string>16B40</string>
    <key>LSEnvironment</key>
    <dict>
      <key>MallocNanoZone</key>
      <string>0</string>
    </dict>
    <key>NSSupportsAutomaticGraphicsSwitching</key>
    <true/>
    <key>ElectronAsarIntegrity</key>
    <dict>
      <key>Contents/Resources/app/node_modules.asar</key>
      <dict>
        <key>algorithm</key>
        <string>SHA256</string>
        <key>hash</key>
        <string>15fa94105bd59b86deb09e2a0161ab2b86f5b7e2ada5c2a00ec3b815a5059725</string>
      </dict>
    </dict>
  </dict>
</plist>