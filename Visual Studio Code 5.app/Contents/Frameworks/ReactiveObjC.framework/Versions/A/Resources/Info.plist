<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>CFBundleExecutable</key>
    <string>ReactiveObjC</string>
    <key>CFBundleIdentifier</key>
    <string>com.electron.reactive</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>ReactiveObjC</string>
    <key>CFBundlePackageType</key>
    <string>FMWK</string>
    <key>CFBundleShortVersionString</key>
    <string>3.1.0</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleVersion</key>
    <string>0.0.0</string>
    <key>DTCompiler</key>
    <string>com.apple.compilers.llvm.clang.1_0</string>
    <key>DTSDKBuild</key>
    <string>24B75</string>
    <key>DTSDKName</key>
    <string>macosx15.1</string>
    <key>DTXcode</key>
    <string>1610</string>
    <key>DTXcodeBuild</key>
    <string>16B40</string>
    <key>NSHumanReadableCopyright</key>
    <string>Copyright © 2014 GitHub. All rights reserved.</string>
    <key>NSPrincipalClass</key>
    <string/>
    <key>ElectronAsarIntegrity</key>
    <dict>
      <key>Contents/Resources/app/node_modules.asar</key>
      <dict>
        <key>algorithm</key>
        <string>SHA256</string>
        <key>hash</key>
        <string>15fa94105bd59b86deb09e2a0161ab2b86f5b7e2ada5c2a00ec3b815a5059725</string>
      </dict>
    </dict>
  </dict>
</plist>