{"name": "font-finder", "version": "1.1.0", "description": "Quickly find system font names and metadata without native dependencies", "homepage": "https://github.com/princjef/font-finder#readme", "repository": {"type": "git", "url": "https://github.com/princjef/font-finder.git"}, "bugs": {"url": "https://github.com/princjef/font-finder/issues"}, "engines": {"node": ">8.0.0"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"commit": "commit", "commitmsg": "commitlint -e $GIT_PARAMS", "lint": "tslint --project tsconfig.json --fix", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean && npm run lint", "build": "tsc", "watch": "tsc -w", "pretest": "npm run build", "test": "nyc ava --serial dist/**/*.spec.js", "pretest-nocover": "npm run build", "test-nocover": "ava --serial dist/**/*.spec.js", "show-coverage": "opener ./coverage/index.html", "coverage": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "prerelease": "npm run build", "release": "semantic-release"}, "keywords": ["font", "system", "fast", "ttf", "otf"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@commitlint/cli": "^6.1.3", "@commitlint/config-conventional": "^6.1.3", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "@semantic-release/changelog": "^2.1.2", "@semantic-release/git": "^4.0.3", "@semantic-release/github": "^4.4.2", "@semantic-release/npm": "^3.4.1", "@types/node": "^8.10.10", "@types/sinon": "^4.3.1", "ava": "^0.25.0", "codecov": "^3.0.1", "husky": "^0.14.3", "nyc": "^11.7.1", "opener": "^1.4.3", "rimraf": "^2.6.2", "semantic-release": "^15.8.1", "sinon": "^4.5.0", "tslint": "^5.11.0", "tslint-config-standard": "^7.0.0", "typescript": "^3.0.1"}, "dependencies": {"get-system-fonts": "^2.0.0", "promise-stream-reader": "^1.0.1"}, "nyc": {"sourceMap": true, "reporter": ["text", "html", "json"], "include": ["dist/**/*.js"], "exclude": ["dist/**/*.spec.*", "dist/**/*.d.ts", "dist/**/*.map*"], "check-coverage": true, "lines": 95, "statements": 95, "functions": 95, "branches": 85, "watermarks": {"lines": [85, 95], "statements": [85, 95], "functions": [85, 95], "branches": [85, 95]}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release": {"verifyConditions": ["@semantic-release/changelog", "@semantic-release/github", "@semantic-release/npm", "@semantic-release/git"], "prepare": [{"path": "@semantic-release/changelog", "changelogFile": "CHANGELOG.md"}, "@semantic-release/npm", "@semantic-release/git"], "publish": ["@semantic-release/npm", "@semantic-release/github"], "success": ["@semantic-release/github"], "fail": ["@semantic-release/github"]}}