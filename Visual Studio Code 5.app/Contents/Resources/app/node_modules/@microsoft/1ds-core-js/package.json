{"name": "@microsoft/1ds-core-js", "version": "3.2.13", "description": "Microsoft Application Insights JavaScript SDK - 1ds-core-js extensions", "author": "Microsoft Application Insights Team", "homepage": "https://github.com/microsoft/ApplicationInsights-JS#readme", "license": "MIT", "sideEffects": false, "scripts": {"ai-min": "grunt core-min", "ai-restore": "grunt core-restore", "publishPackage": "npm publish", "docs": "typedoc --out docs docs --excludePrivate --excludeProtected --tsconfig lib/tsconfig.json --theme minimal", "sri": "node ../../tools/subResourceIntegrity/generateIntegrityFile.js", "npm-pack": "npm pack"}, "publishConfig": {"registry": "https://registry.npmjs.org"}, "repository": {"type": "git", "url": "https://github.com/microsoft/ApplicationInsights-JS"}, "main": "dist/ms.core.js", "module": "dist-esm/src/Index.js", "keywords": ["1ds", "azure", "cloud", "script errors", "microsoft", "application insights", "Js", "SDK"], "types": "dist-esm/src/Index.d.ts", "dependencies": {"@microsoft/applicationinsights-shims": "^2.0.2", "@microsoft/applicationinsights-core-js": "2.8.15", "@microsoft/dynamicproto-js": "^1.1.7"}, "devDependencies": {"grunt": "^1.4.1", "typescript": "^4.3.5"}}