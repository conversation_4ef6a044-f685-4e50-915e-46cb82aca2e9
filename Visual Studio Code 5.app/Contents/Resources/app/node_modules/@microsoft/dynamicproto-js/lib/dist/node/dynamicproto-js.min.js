/*!
 * Microsoft Dynamic Proto Utility, 1.1.9
 * Copyright (c) Microsoft and contributors. All rights reserved.
 */
var n=this,t=function(){"use strict";var n,t="undefined",r="constructor",d="prototype",h="function",v="_dynInstFuncs",_="_isDynProxy",g="_dynClass",b="_dynInstChk",w=b,m="_dfOpts",o="_unknown_",e="__proto__",i="_dyn"+e,f="__dynProto$Gbl",u="_dynInstProto",P="useBaseInst",I="setInstFuncs",s=Object,O=s.getPrototypeOf,a=s.getOwnPropertyNames,t=(n=(n=(n=(n=typeof globalThis!=t?globalThis:n)||typeof self==t?n:self)||typeof window==t?n:window)||typeof global==t?n:global)||{},T=t[f]||(t[f]={o:((n={})[I]=!0,n[P]=!0,n),n:1e3});function C(n,t){return n&&s[d].hasOwnProperty.call(n,t)}function M(n){return n&&(n===s[d]||n===Array[d])}function k(n){return M(n)||n===Function[d]}function x(n){if(n){if(O)return O(n);var t=n[e]||n[d]||(n[r]?n[r][d]:null),o=n[i]||t;C(n,i)||(delete n[u],o=n[i]=n[u]||n[i],n[u]=t)}return o}function D(n,t){var o=[];if(a)o=a(n);else for(var r in n)"string"==typeof r&&C(n,r)&&o.push(r);if(o&&0<o.length)for(var e=0;e<o.length;e++)t(o[e])}function F(n,t,o){return t!==r&&typeof n[t]===h&&(o||C(n,t))}function $(n){throw new TypeError("DynamicProto: "+n)}function j(n,t){for(var o=n.length-1;0<=o;o--)if(n[o]===t)return 1}function A(n,t){return C(n,d)?n.name||t||o:((n||{})[r]||{}).name||t||o}function B(n,r,t,o){C(n,d)||$("theClass is an invalid class definition.");var e,i,f,u,s,a,c=n[d],l=(function(n){if(!O)return 1;for(var t=[],o=x(r);o&&!k(o)&&!j(t,o);){if(o===n)return 1;t.push(o),o=x(o)}}(c)||$("["+A(n)+"] not in hierarchy of ["+A(r)+"]"),null),n=(C(c,g)?l=c[g]:(l="_dynCls$"+A(n,"_")+"$"+T.n,T.n++,c[g]=l),B[m]),y=!!n[P],p=(y&&o&&o[P]!==undefined&&(y=!!o[P]),i={},D(e=r,function(n){!i[n]&&F(e,n,!1)&&(i[n]=e[n])}),i),y=(t(r,function(n,t,o,i){function r(n,t,o){var r,e=t[o];return e[_]&&i&&!1!==(r=n[v]||{})[w]&&(e=(r[t[g]]||{})[o]||e),function(){return e.apply(n,arguments)}}for(var e={},f=(D(o,function(n){e[n]=r(t,o,n)}),x(n)),u=[];f&&!k(f)&&!j(u,f);)D(f,function(n){!e[n]&&F(f,n,!O)&&(e[n]=r(t,f,n))}),u.push(f),f=x(f);return e}(c,r,p,y)),!!O&&!!n[I]);f=c,t=l,u=r,s=p,n=0!=(y&&o?!!o[I]:y),M(f)||(c=u[v]=u[v]||{},a=c[t]=c[t]||{},!1!==c[w]&&(c[w]=!!n),D(u,function(n){var o,r,e;F(u,n,!1)&&u[n]!==s[n]&&(a[n]=u[n],delete u[n],C(f,n)&&(!f[n]||f[n][_])||(f[n]=(o=f,r=n,(e=function(){var n,t;return(function(n,t,o,r){var e=null;if(n&&C(o,g)){var i=n[v]||{};if((e=(i[o[g]]||{})[t])||$("Missing ["+t+"] "+h),!e[b]&&!1!==i[w]){for(var f=!C(n,t),u=x(n),s=[];f&&u&&!k(u)&&!j(s,u);){var a=u[t];if(a){f=a===r;break}s.push(u),u=x(u)}try{f&&(n[t]=e),e[b]=1}catch(c){i[w]=!1}}}return e}(this,r,o,e)||(typeof(t=(t=o[n=r])===e?x(o)[n]:t)!==h&&$("["+n+"] is not a "+h),t)).apply(this,arguments)})[_]=1,e)))}))}return B[m]=T.o,B};"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):((n="undefined"!=typeof globalThis?globalThis:n||self).Microsoft=n.Microsoft||{},n.Microsoft["DynamicProto-JS"]=t());//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/18e3a1ec544e6907be1e944a94c496e302073435/node_modules/@microsoft/dynamicproto-js/lib/dist/node/dynamicproto-js.min.js.map
