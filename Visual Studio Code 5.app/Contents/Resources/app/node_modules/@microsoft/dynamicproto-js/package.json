{"name": "@microsoft/dynamicproto-js", "description": "Microsoft Dynamic Proto Utility", "version": "1.1.9", "keywords": ["javascript", "dynamic prototype", "microsoft", "typescript", "inheritence", "minification", "application insights"], "main": "./lib/dist/node/dynamicproto-js.js", "module": "./lib/dist/esm/dynamicproto-js.js", "types": "./lib/types/dynamicproto-js.d.ts", "directories": {"doc": "lib/docs"}, "scripts": {"ci-install": "node common/scripts/install-run-rush.js check && node common/scripts/install-run-rush.js update", "build": "node common/scripts/install-run-rush.js rebuild && npm run docs", "test": "grunt dynamic<PERSON>rot<PERSON>t", "rollup": "grunt rollup", "rupdate": "node common/scripts/install-run-rush.js update --recheck --purge --full", "docs": "typedoc", "fullClean": "git clean -xdf && npm install && rush update --recheck --purge --full", "fullCleanBuild": "npm run fullClean && npm run build", "npm_pack": "npm pack", "npm_publish": "node ./tools/release-tools/npm_publish.js ."}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/DynamicProto-JS.git"}, "author": "Microsoft Application Insights Team", "sideEffects": false, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/DynamicProto-JS/issues"}, "homepage": "https://github.com/microsoft/DynamicProto-JS#readme", "dependencies": {}, "devDependencies": {"@microsoft/rush": "5.82.1", "@nevware21/grunt-ts-plugin": "^0.3.0", "@nevware21/grunt-eslint-ts": "^0.1.0", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "eslint": "^7.29.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.23.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-security": "^1.4.0", "grunt": "^1.5.3", "grunt-cli": "^1.4.3", "grunt-contrib-qunit": "^4.0.0", "grunt-contrib-uglify": "^5.0.1", "grunt-run": "^0.8.1", "@rollup/plugin-node-resolve": "^9.0.0", "@rollup/plugin-replace": "^2.3.3", "rollup-plugin-cleanup": "3.2.1", "rollup": "^2.32.0", "typedoc": "^0.23.25", "typescript": "^4.9.5", "copyfiles": "^2.4.1"}}