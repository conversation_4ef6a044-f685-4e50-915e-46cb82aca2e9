## API Report File for "@microsoft/applicationinsights-core-js"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

import { getGlobal } from '@microsoft/applicationinsights-shims';
import { objCreateFn as objCreate } from '@microsoft/applicationinsights-shims';
import { strShimFunction as strFunction } from '@microsoft/applicationinsights-shims';
import { strShimObject as strObject } from '@microsoft/applicationinsights-shims';
import { strShimPrototype as strPrototype } from '@microsoft/applicationinsights-shims';
import { strShimUndefined as strUndefined } from '@microsoft/applicationinsights-shims';

// @public
export function __getRegisteredEvents(target: any, eventName?: string, evtNamespace?: string | string[]): _IRegisteredEvents[];

// @public
export function addEventHandler(eventName: string, callback: any, evtNamespace?: string | string[] | null): boolean;

// @public
export function addEventListeners(events: string[], listener: any, excludeEvents?: string[], evtNamespace?: string | string[]): boolean;

// @public
export function addPageHideEventListener(listener: any, excludeEvents?: string[] | null, evtNamespace?: string | string[] | null): boolean;

// @public
export function addPageShowEventListener(listener: any, excludeEvents?: string[] | null, evtNamespace?: string | string[] | null): boolean;

// @public
export function addPageUnloadEventListener(listener: any, excludeEvents?: string[], evtNamespace?: string | string[]): boolean;

// @public (undocumented)
export class AppInsightsCore extends BaseCore implements IAppInsightsCore {
    constructor();
    // (undocumented)
    initialize(config: IConfiguration, extensions: IPlugin[], logger?: IDiagnosticLogger, notificationManager?: INotificationManager): void;
    // (undocumented)
    track(telemetryItem: ITelemetryItem): void;
}

// @public (undocumented)
export function areCookiesSupported(logger?: IDiagnosticLogger): any;

// @public
export function arrForEach<T = any>(arr: T[], callbackfn: (value: T, index?: number, array?: T[]) => undefined | void | number, thisArg?: any): void;

// @public
export function arrIndexOf<T>(arr: T[], searchElement: T, fromIndex?: number): number;

// @public
export function arrMap<T, R>(arr: T[], callbackfn: (value: T, index?: number, array?: T[]) => R, thisArg?: any): R[];

// @public
export function arrReduce<T, R>(arr: T[], callbackfn: (previousValue: T | R, currentValue?: T, currentIndex?: number, array?: T[]) => R, initialValue?: R): R;

// @public
export function attachEvent(obj: any, eventNameWithoutOn: string, handlerRef: any, useCapture?: boolean): boolean;

// @public (undocumented)
export class BaseCore implements IAppInsightsCore {
    constructor();
    addNotificationListener(listener: INotificationListener): void;
    addPlugin<T extends IPlugin = ITelemetryPlugin>(plugin: T, replaceExisting?: boolean, doAsync?: boolean, addCb?: (added?: boolean) => void): void;
    addTelemetryInitializer(telemetryInitializer: TelemetryInitializerFunction): ITelemetryInitializerHandler | void;
    addUnloadCb(handler: UnloadHandler): void;
    // (undocumented)
    config: IConfiguration;
    // (undocumented)
    static defaultConfig: IConfiguration;
    // (undocumented)
    eventCnt(): number;
    evtNamespace(): string;
    // (undocumented)
    _extensions: IPlugin[];
    flush(isAsync?: boolean, callBack?: (flushComplete?: boolean) => void, sendReason?: SendRequestReason): void;
    getCookieMgr(): ICookieMgr;
    // (undocumented)
    getNotifyMgr(): INotificationManager;
    // (undocumented)
    getPerfMgr(): IPerfManager;
    // (undocumented)
    getPlugin<T extends IPlugin = IPlugin>(pluginIdentifier: string): ILoadedPlugin<T>;
    // (undocumented)
    getProcessTelContext(): IProcessTelemetryContext;
    getTraceCtx(createNew?: boolean): IDistributedTraceContext | null;
    // (undocumented)
    getTransmissionControls(): IChannelControls[][];
    // (undocumented)
    initialize(config: IConfiguration, extensions: IPlugin[], logger?: IDiagnosticLogger, notificationManager?: INotificationManager): void;
    // (undocumented)
    isInitialized: () => boolean;
    // (undocumented)
    logger: IDiagnosticLogger;
    pollInternalLogs(eventName?: string): number;
    // (undocumented)
    protected releaseQueue(): void;
    removeNotificationListener(listener: INotificationListener): void;
    setCookieMgr(cookieMgr: ICookieMgr): void;
    // (undocumented)
    setPerfMgr(perfMgr: IPerfManager): void;
    setTraceCtx(newTracectx: IDistributedTraceContext): void;
    stopPollingInternalLogs(): void;
    // (undocumented)
    track(telemetryItem: ITelemetryItem): void;
    unload(isAsync?: boolean, unloadComplete?: (unloadState: ITelemetryUnloadState) => void, cbTimeout?: number): void;
    protected _updateHook?(updateCtx: IProcessTelemetryUpdateContext, updateState: ITelemetryUpdateState): void | boolean;
}

// @public
export abstract class BaseTelemetryPlugin implements ITelemetryPlugin {
    constructor();
    protected _addHook(hooks: IInstrumentHook | IInstrumentHook[]): void;
    protected _addUnloadCb(handler: UnloadHandler): void;
    core: IAppInsightsCore;
    diagLog: (itemCtx?: IProcessTelemetryContext) => IDiagnosticLogger;
    protected _doTeardown?: (unloadCtx?: IProcessTelemetryUnloadContext, unloadState?: ITelemetryUnloadState, asyncCallback?: () => void) => void | boolean;
    protected _doUpdate?: (updateCtx?: IProcessTelemetryUpdateContext, updateState?: ITelemetryUpdateState, asyncCallback?: () => void) => void | boolean;
    protected _getTelCtx: (currentCtx?: IProcessTelemetryContext) => IProcessTelemetryContext;
    // (undocumented)
    identifier: string;
    // (undocumented)
    initialize(config: IConfiguration, core: IAppInsightsCore, extensions: IPlugin[], pluginChain?: ITelemetryPluginChain): void;
    isInitialized: () => boolean;
    // (undocumented)
    priority: number;
    processNext: (env: ITelemetryItem, itemCtx: IProcessTelemetryContext) => void;
    // (undocumented)
    abstract processTelemetry(env: ITelemetryItem, itemCtx?: IProcessTelemetryContext): void;
    protected setInitialized: (isInitialized: boolean) => void;
    setNextPlugin: (next: ITelemetryPlugin | ITelemetryPluginChain) => void;
    teardown(unloadCtx?: IProcessTelemetryUnloadContext, unloadState?: ITelemetryUnloadState): void | boolean;
    update(updateCtx: IProcessTelemetryUpdateContext, updateState: ITelemetryUpdateState): void | boolean;
    // (undocumented)
    version?: string;
}

// @public @deprecated (undocumented)
export function canUseCookies(logger: IDiagnosticLogger): any;

// @public @deprecated
export const CoreUtils: ICoreUtils;

// @public
export function createClassFromInterface<T>(defaults?: T): new () => T;

// @public (undocumented)
export function createCookieMgr(rootConfig?: IConfiguration, logger?: IDiagnosticLogger): ICookieMgr;

// @public (undocumented)
export function createCustomDomEvent(eventName: string, details?: any): CustomEvent;

// @public
export function createEnumMap<E, I = keyof E>(values: {
    [key in keyof E]: E[keyof E];
}): EnumMap<E, I>;

// @public
export function createEnumStyle<E>(values: {
    [key in keyof E]: E[keyof E];
}): EnumValue<E>;

// @public
export function createProcessTelemetryContext(telemetryChain: ITelemetryPluginChain | null, config: IConfiguration, core: IAppInsightsCore, startAt?: IPlugin): IProcessTelemetryContext;

// @public
export function createTraceParent(traceId?: string, spanId?: string, flags?: number, version?: string): ITraceParent;

// @public (undocumented)
export function createUniqueNamespace(name: string, includeVersion?: boolean): string;

// @public (undocumented)
export function createUnloadHandlerContainer(): {
    add: (handler: UnloadHandler) => void;
    run: (unloadCtx: IProcessTelemetryUnloadContext, unloadState: ITelemetryUnloadState) => void;
};

// @public
export function createValueMap<E, V = E>(values: {
    [key in keyof E]: [E[keyof E], V[keyof V]];
}): V;

// @public
export function dateNow(): number;

// @public (undocumented)
export function deepFreeze<T>(obj: T): T;

// @public @deprecated (undocumented)
export function deleteCookie(logger: IDiagnosticLogger, name: string): boolean;

// @public
export function detachEvent(obj: any, eventNameWithoutOn: string, handlerRef: any, useCapture?: boolean): void;

// @public (undocumented)
export class DiagnosticLogger implements IDiagnosticLogger {
    constructor(config?: IConfiguration);
    consoleLoggingLevel(): number;
    enableDebugExceptions(): boolean;
    errorToConsole(message: string): void;
    // (undocumented)
    identifier: string;
    logInternalMessage(severity: LoggingSeverity, message: _InternalLogMessage): void;
    maxInternalMessageLimit(): number;
    queue: _InternalLogMessage[];
    resetInternalMessageCount(): void;
    telemetryLoggingLevel(): number;
    throwInternal(severity: LoggingSeverity, msgId: _InternalMessageId, msg: string, properties?: Object, isUserAct?: boolean): void;
    warnToConsole(message: string): void;
}

// @public @deprecated (undocumented)
export function disableCookies(): void;

// @public (undocumented)
function dispatchEvent_2(target: EventTarget, evnt: Event | CustomEvent): boolean;
export { dispatchEvent_2 as dispatchEvent }

// @public
export function doPerf<T>(mgrSource: IPerfManagerProvider | IPerfManager, getSource: () => string, func: (perfEvt?: IPerfEvent) => T, details?: () => any, isAsync?: boolean): T;

// @public
export function dumpObj(object: any): string;

// @public
export const enum eEventsDiscardedReason {
    InvalidEvent = 2,
    KillSwitch = 4,
    NonRetryableStatus = 1,
    QueueFull = 5,
    SizeLimitExceeded = 3,
    Unknown = 0
}

// @public (undocumented)
export const enum _eInternalMessageId {
    // (undocumented)
    BrowserCannotReadLocalStorage = 1,
    // (undocumented)
    BrowserCannotReadSessionStorage = 2,
    // (undocumented)
    BrowserCannotWriteLocalStorage = 3,
    // (undocumented)
    BrowserCannotWriteSessionStorage = 4,
    // (undocumented)
    BrowserDoesNotSupportLocalStorage = 0,
    // (undocumented)
    BrowserFailedRemovalFromLocalStorage = 5,
    // (undocumented)
    BrowserFailedRemovalFromSessionStorage = 6,
    // (undocumented)
    CannotAccessCookie = 68,
    // (undocumented)
    CannotParseAiBlobValue = 101,
    // (undocumented)
    CannotSendEmptyTelemetry = 7,
    // (undocumented)
    CannotSerializeObject = 48,
    // (undocumented)
    CannotSerializeObjectNonSerializable = 49,
    // (undocumented)
    CircularReferenceDetected = 50,
    // (undocumented)
    ClearAuthContextFailed = 51,
    // (undocumented)
    ClientPerformanceMathError = 8,
    // (undocumented)
    CreateEnvelopeError = 47,
    // (undocumented)
    ErrorParsingAISessionCookie = 9,
    // (undocumented)
    ErrorPVCalc = 10,
    // (undocumented)
    ExceptionTruncated = 52,
    // (undocumented)
    ExceptionWhileLoggingError = 11,
    // (undocumented)
    FailedAddingCustomDefinedRequestContext = 104,
    // (undocumented)
    FailedAddingTelemetryToBuffer = 12,
    // (undocumented)
    FailedMonitorAjaxAbort = 13,
    // (undocumented)
    FailedMonitorAjaxDur = 14,
    // (undocumented)
    FailedMonitorAjaxGetCorrelationHeader = 18,
    // (undocumented)
    FailedMonitorAjaxOpen = 15,
    // (undocumented)
    FailedMonitorAjaxRSC = 16,
    // (undocumented)
    FailedMonitorAjaxSend = 17,
    // (undocumented)
    FailedMonitorAjaxSetRequestHeader = 71,
    // (undocumented)
    FailedToAddHandlerForOnBeforeUnload = 19,
    // (undocumented)
    FailedToFixDepricatedValues = 44,
    // (undocumented)
    FailedToReportDataLoss = 21,
    // (undocumented)
    FailedToRestoreStorageBuffer = 42,
    // (undocumented)
    FailedToSendQueuedTelemetry = 20,
    // (undocumented)
    FailedToSetStorageBuffer = 41,
    // (undocumented)
    FlushFailed = 22,
    // (undocumented)
    IdTooLong = 69,
    // (undocumented)
    IllegalCharsInName = 53,
    // (undocumented)
    InMemoryStorageBufferFull = 105,
    // (undocumented)
    InstrumentationKeyDeprecation = 106,
    // (undocumented)
    InvalidBackendResponse = 43,
    // (undocumented)
    InvalidContentBlob = 102,
    // (undocumented)
    InvalidDurationValue = 45,
    // (undocumented)
    InvalidEvent = 70,
    // (undocumented)
    InvalidInstrumentationKey = 100,
    // (undocumented)
    ItemNotInArray = 54,
    // (undocumented)
    MaxAjaxPerPVExceeded = 55,
    // (undocumented)
    MessageLimitPerPVExceeded = 23,
    // (undocumented)
    MessageTruncated = 56,
    // (undocumented)
    MissingRequiredFieldSpecification = 24,
    // (undocumented)
    NameTooLong = 57,
    // (undocumented)
    NavigationTimingNotSupported = 25,
    // (undocumented)
    NotificationException = 74,
    // (undocumented)
    OnError = 26,
    // (undocumented)
    PluginException = 73,
    // (undocumented)
    SampleRateOutOfRange = 58,
    // (undocumented)
    SendBrowserInfoOnUserInit = 72,
    // (undocumented)
    SenderNotInitialized = 28,
    // (undocumented)
    SessionRenewalDateIsZero = 27,
    // (undocumented)
    SessionStorageBufferFull = 67,
    // (undocumented)
    SetAuthContextFailed = 59,
    // (undocumented)
    SetAuthContextFailedAccountName = 60,
    // (undocumented)
    SnippetScriptLoadFailure = 99,
    // (undocumented)
    StartCalledMoreThanOnce = 62,
    // (undocumented)
    StartTrackEventFailed = 29,
    // (undocumented)
    StartTrackFailed = 31,
    // (undocumented)
    StopCalledWithoutStart = 63,
    // (undocumented)
    StopTrackEventFailed = 30,
    // (undocumented)
    StopTrackFailed = 32,
    // (undocumented)
    StringValueTooLong = 61,
    // (undocumented)
    TelemetryEnvelopeInvalid = 46,
    // (undocumented)
    TelemetryInitializerFailed = 64,
    // (undocumented)
    TelemetrySampledAndNotSent = 33,
    // (undocumented)
    TrackArgumentsNotSpecified = 65,
    // (undocumented)
    TrackEventFailed = 34,
    // (undocumented)
    TrackExceptionFailed = 35,
    // (undocumented)
    TrackMetricFailed = 36,
    // (undocumented)
    TrackPageActionEventFailed = 103,
    // (undocumented)
    TrackPVFailed = 37,
    // (undocumented)
    TrackPVFailedCalc = 38,
    // (undocumented)
    TrackTraceFailed = 39,
    // (undocumented)
    TransmissionFailed = 40,
    // (undocumented)
    UrlTooLong = 66
}

// @public (undocumented)
export const enum eLoggingSeverity {
    CRITICAL = 1,
    WARNING = 2
}

// @public (undocumented)
export type EnumMap<E = any, I = E> = {
    readonly [key in keyof E extends string ? keyof E : never]: key extends string ? key : keyof E;
} & I;

// @public (undocumented)
export type EnumValue<E = any> = {
    readonly [key in keyof E]: E[key];
};

// @public (undocumented)
export const EventHelper: IEventHelper;

// @public
export function eventOff<T>(target: T, eventName: string, handlerRef: any, evtNamespace?: string | string[] | null, useCapture?: boolean): void;

// @public
export function eventOn<T>(target: T, eventName: string, handlerRef: any, evtNamespace?: string | string[] | null, useCapture?: boolean): boolean;

// @public
export const EventsDiscardedReason: EnumValue<typeof eEventsDiscardedReason>;

// @public (undocumented)
export type EventsDiscardedReason = number | eEventsDiscardedReason;

// @public
export function findMetaTag(name: string): any;

// @public
export function findNamedServerTiming(name: string): any;

// @public
export function findW3cTraceParent(): ITraceParent;

// @public
export function formatTraceParent(value: ITraceParent): string;

// @public
export function generateW3CId(): string;

// @public
export function getConsole(): Console | null;

// @public @deprecated (undocumented)
export function getCookie(logger: IDiagnosticLogger, name: string): string;

// @public
export function getCrypto(): Crypto | null;

// @public (undocumented)
export function getDebugExt(config: IConfiguration): IDbgExtension;

// @public (undocumented)
export function getDebugListener(config: IConfiguration): INotificationListener;

// @public
export function getDocument(): Document | null;

// @public
export function getExceptionName(object: any): string;

// @public (undocumented)
export const enum GetExtCfgMergeType {
    // (undocumented)
    MergeDefaultFromRootOrDefault = 2,
    // (undocumented)
    MergeDefaultOnly = 1,
    // (undocumented)
    None = 0
}

// @public
export function getGblPerfMgr(): IPerfManager;

export { getGlobal }

// @public
export function getGlobalInst<T>(name: string): T;

// @public
export function getHistory(): History | null;

// @public
export function getIEVersion(userAgentStr?: string): number;

// @public
export function getJSON(): JSON | null;

// @public
export function getLocation(checkForMock?: boolean): Location | null;

// @public
export function getMsCrypto(): Crypto | null;

// @public
export function getNavigator(): Navigator | null;

// @public
export function getPerformance(): Performance | null;

// @public
export function getSetValue<T, K extends keyof T>(target: T, field: K, defValue?: T[K]): T[K];

// @public
export function getWindow(): Window | null;

// @public
export function hasDocument(): boolean;

// @public
export function hasHistory(): boolean;

// @public
export function hasJSON(): boolean;

// @public
export function hasNavigator(): boolean;

// @public (undocumented)
export function hasOwnProperty(obj: any, prop: string): boolean;

// @public
export function hasWindow(): boolean;

// @public (undocumented)
export interface IAppInsightsCore extends IPerfManagerProvider {
    addNotificationListener?(listener: INotificationListener): void;
    addPlugin<T extends IPlugin = ITelemetryPlugin>(plugin: T, replaceExisting?: boolean, doAsync?: boolean, addCb?: (added?: boolean) => void): void;
    addTelemetryInitializer(telemetryInitializer: TelemetryInitializerFunction): ITelemetryInitializerHandler | void;
    addUnloadCb(handler: UnloadHandler): void;
    // (undocumented)
    config: IConfiguration;
    evtNamespace(): string;
    flush(isAsync?: boolean, callBack?: (flushComplete?: boolean) => void, sendReason?: SendRequestReason, cbTimeout?: number): boolean | void;
    getCookieMgr(): ICookieMgr;
    getNotifyMgr(): INotificationManager;
    getPlugin<T extends IPlugin = IPlugin>(pluginIdentifier: string): ILoadedPlugin<T>;
    getProcessTelContext(): IProcessTelemetryContext;
    getTraceCtx(createNew?: boolean): IDistributedTraceContext | null;
    // (undocumented)
    getTransmissionControls(): IChannelControls[][];
    // (undocumented)
    initialize(config: IConfiguration, extensions: IPlugin[], logger?: IDiagnosticLogger, notificationManager?: INotificationManager): void;
    isInitialized?: () => boolean;
    // (undocumented)
    logger: IDiagnosticLogger;
    // (undocumented)
    pollInternalLogs?(eventName?: string): number;
    removeNotificationListener?(listener: INotificationListener): void;
    setCookieMgr(cookieMgr: ICookieMgr): void;
    setTraceCtx(newTraceCtx: IDistributedTraceContext | null | undefined): void;
    // (undocumented)
    stopPollingInternalLogs?(): void;
    // (undocumented)
    track(telemetryItem: ITelemetryItem): void;
    unload(isAsync?: boolean, unloadComplete?: (unloadState: ITelemetryUnloadState) => void, cbTimeout?: number): void;
}

// @public (undocumented)
export interface IBaseProcessingContext {
    core: () => IAppInsightsCore;
    createNew: (plugins?: IPlugin[] | ITelemetryPluginChain, startAt?: IPlugin) => IBaseProcessingContext;
    diagLog: () => IDiagnosticLogger;
    getCfg: () => IConfiguration;
    getConfig: (identifier: string, field: string, defaultValue?: number | string | boolean | string[] | RegExp[] | Function) => number | string | boolean | string[] | RegExp[] | Function;
    getExtCfg: <T>(identifier: string, defaultValue?: T | any, mergeDefault?: GetExtCfgMergeType) => T;
    getNext: () => ITelemetryPluginChain;
    hasNext: () => boolean;
    iterate: <T extends ITelemetryPlugin = ITelemetryPlugin>(callback: (plugin: T) => void) => void;
    onComplete: (onComplete: () => void, that?: any, ...args: any[]) => void;
    setNext: (nextCtx: ITelemetryPluginChain) => void;
}

// @public
export interface IChannelControls extends ITelemetryPlugin {
    flush(async: boolean, callBack?: (flushComplete?: boolean) => void, sendReason?: SendRequestReason): boolean | void;
    pause(): void;
    resume(): void;
    teardown: (unloadCtx?: IProcessTelemetryUnloadContext, unloadState?: ITelemetryUnloadState) => void | boolean;
}

// @public
export interface IConfiguration {
    channels?: IChannelControls[][];
    connectionString?: string;
    cookieCfg?: ICookieMgrConfig;
    cookieDomain?: string;
    cookiePath?: string;
    createPerfMgr?: (core: IAppInsightsCore, notificationManager: INotificationManager) => IPerfManager;
    diagnosticLogInterval?: number;
    disableCookiesUsage?: boolean;
    disableDbgExt?: boolean;
    disableInstrumentationKeyValidation?: boolean;
    disablePageShowEvents?: string[];
    disablePageUnloadEvents?: string[];
    enableDebug?: boolean;
    enableDebugExceptions?: boolean;
    enablePerfMgr?: boolean;
    endpointUrl?: string;
    extensionConfig?: {
        [key: string]: any;
    };
    extensions?: ITelemetryPlugin[];
    idLength?: number;
    instrumentationKey?: string;
    loggingLevelConsole?: number;
    loggingLevelTelemetry?: number;
    maxMessageLimit?: number;
    perfEvtsSendAll?: boolean;
    storagePrefix?: string;
}

// @public (undocumented)
export interface ICookieMgr {
    del(name: string, path?: string): boolean;
    get(name: string): string;
    isEnabled(): boolean;
    purge(name: string, path?: string): boolean;
    set(name: string, value: string, maxAgeSec?: number, domain?: string, path?: string): boolean;
    setEnabled(value: boolean): void;
}

// @public
export interface ICookieMgrConfig {
    blockedCookies?: string[];
    delCookie?: (name: string, cookieValue: string) => void;
    domain?: string;
    enabled?: boolean;
    getCookie?: (name: string) => string;
    ignoreCookies?: string[];
    path?: string;
    setCookie?: (name: string, value: string) => void;
}

// @public @deprecated
export interface ICoreUtils {
    addEventHandler: (eventName: string, callback: any, evtNamespace?: string | string[]) => boolean;
    arrForEach: <T>(arr: T[], callbackfn: (value: T, index?: number, array?: T[]) => void | number, thisArg?: any) => void;
    arrIndexOf: <T>(arr: T[], searchElement: T, fromIndex?: number) => number;
    arrMap: <T, R>(arr: T[], callbackfn: (value: T, index?: number, array?: T[]) => R, thisArg?: any) => R[];
    arrReduce: <T, R>(arr: T[], callbackfn: (previousValue: T | R, currentValue?: T, currentIndex?: number, array?: T[]) => R, initialValue?: R) => R;
    // @deprecated
    _canUseCookies: boolean;
    dateNow: () => number;
    // @deprecated (undocumented)
    disableCookies: () => void;
    generateW3CId: () => string;
    // (undocumented)
    hasOwnProperty: (obj: any, prop: string) => boolean;
    isArray: (obj: any) => boolean;
    isBoolean: (value: any) => value is boolean;
    isDate: (obj: any) => obj is Date;
    isError: (obj: any) => obj is Error;
    isFunction: (value: any) => value is Function;
    isIE: () => boolean;
    // (undocumented)
    isNullOrUndefined: (value: any) => boolean;
    isNumber: (value: any) => value is number;
    isObject: (value: any) => boolean;
    isString: (value: any) => value is string;
    // (undocumented)
    isTypeof: (value: any, theType: string) => boolean;
    // (undocumented)
    isUndefined: (value: any) => boolean;
    mwcRandom32: (signed?: boolean) => number;
    mwcRandomSeed: (value?: number) => void;
    // (undocumented)
    newGuid: () => string;
    newId: (maxLength?: number) => string;
    objCreate: (obj: object) => any;
    objDefineAccessors: <T>(target: any, prop: string, getProp?: () => T, setProp?: (v: T) => void) => boolean;
    objKeys: (obj: {}) => string[];
    perfNow: () => number;
    random32: (signed?: boolean) => number;
    randomValue: (maxValue: number) => number;
    strTrim: (str: any) => string;
    toISOString: (date: Date) => string;
}

// @public (undocumented)
export interface ICustomProperties {
    // (undocumented)
    [key: string]: any;
}

// @public (undocumented)
export interface IDbgExtension {
    // (undocumented)
    debugMsg?: (name: string, data: any) => void;
    // (undocumented)
    diagLog?: (name: string, data: any) => void;
    // (undocumented)
    disable: () => void;
    // (undocumented)
    enable: () => void;
    // (undocumented)
    isEnabled: () => boolean;
    // (undocumented)
    listener: INotificationListener;
    // (undocumented)
    sendEvt?: (name: string, data: any) => void;
}

// @public (undocumented)
export interface IDiagnosticLogger {
    consoleLoggingLevel: () => number;
    enableDebugExceptions: () => boolean;
    errorToConsole?(message: string): void;
    logInternalMessage?(severity: LoggingSeverity, message: _InternalLogMessage): void;
    maxInternalMessageLimit: () => number;
    queue: _InternalLogMessage[];
    resetInternalMessageCount(): void;
    telemetryLoggingLevel: () => number;
    throwInternal(severity: LoggingSeverity, msgId: _InternalMessageId, msg: string, properties?: Object, isUserAct?: boolean): void;
    warnToConsole(message: string): void;
}

// @public (undocumented)
export interface IDistributedTraceContext {
    getName(): string;
    getSpanId(): string;
    getTraceFlags(): number | undefined;
    getTraceId(): string;
    setName(pageName: string): void;
    setSpanId(newValue: string): void;
    setTraceFlags(newValue?: number): void;
    setTraceId(newValue: string): void;
}

// @public (undocumented)
export interface IEventHelper {
    Attach: (obj: any, eventNameWithoutOn: string, handlerRef: any) => boolean;
    // @deprecated
    AttachEvent: (obj: any, eventNameWithoutOn: string, handlerRef: any) => boolean;
    Detach: (obj: any, eventNameWithoutOn: string, handlerRef: any) => void;
    // @deprecated
    DetachEvent: (obj: any, eventNameWithoutOn: string, handlerRef: any) => void;
}

// @public (undocumented)
export interface IInstrumentCallDetails {
    ctx: () => any;
    err?: Error;
    evt?: Event;
    // (undocumented)
    inst: any;
    // (undocumented)
    name: string;
    rslt?: any;
    set: (idx: number, value: any) => void;
}

// @public
export interface IInstrumentHook {
    cbks: IInstrumentHooksCallbacks;
    id: number;
    rm: () => void;
}

// @public (undocumented)
export interface IInstrumentHooks {
    // (undocumented)
    f: any;
    // (undocumented)
    h: IInstrumentHook[];
    // (undocumented)
    i: number;
    // (undocumented)
    n: string;
}

// @public
export interface IInstrumentHooksCallbacks {
    fnErr?: InstrumentorHooksCallback;
    hkErr?: InstrumentorHooksCallback;
    ns?: string | string[];
    req?: InstrumentorHooksCallback;
    rsp?: InstrumentorHooksCallback;
}

// @public (undocumented)
export interface ILoadedPlugin<T extends IPlugin> {
    isEnabled: () => boolean;
    // (undocumented)
    plugin: T;
    // (undocumented)
    remove: (isAsync?: boolean, removeCb?: (removed?: boolean) => void) => void;
    setEnabled: (isEnabled: boolean) => void;
}

// @public
export function initializePlugins(processContext: IProcessTelemetryContext, extensions: IPlugin[]): void;

// @public
export interface INotificationListener {
    eventsDiscarded?: (events: ITelemetryItem[], reason: number) => void;
    eventsSendRequest?: (sendReason: number, isAsync?: boolean) => void;
    eventsSent?: (events: ITelemetryItem[]) => void;
    perfEvent?: (perfEvent: IPerfEvent) => void;
}

// @public
export interface INotificationManager {
    addNotificationListener(listener: INotificationListener): void;
    eventsDiscarded(events: ITelemetryItem[], reason: number): void;
    eventsSendRequest?(sendReason: number, isAsync: boolean): void;
    eventsSent(events: ITelemetryItem[]): void;
    // (undocumented)
    listeners: INotificationListener[];
    perfEvent?(perfEvent: IPerfEvent): void;
    removeNotificationListener(listener: INotificationListener): void;
}

// @public
export function InstrumentEvent(target: any, evtName: string, callbacks: IInstrumentHooksCallbacks, checkPrototype?: boolean, checkParentProto?: boolean): IInstrumentHook;

// @public
export function InstrumentFunc(target: any, funcName: string, callbacks: IInstrumentHooksCallbacks, checkPrototype?: boolean, checkParentProto?: boolean): IInstrumentHook;

// @public
export function InstrumentFuncs(target: any, funcNames: string[], callbacks: IInstrumentHooksCallbacks, checkPrototype?: boolean, checkParentProto?: boolean): IInstrumentHook[];

// @public
export type InstrumentorHooksCallback = (funcArgs: IInstrumentCallDetails, ...orgArgs: any[]) => void;

// @public
export function InstrumentProto(target: any, funcName: string, callbacks: IInstrumentHooksCallbacks): IInstrumentHook;

// @public
export function InstrumentProtos(target: any, funcNames: string[], callbacks: IInstrumentHooksCallbacks): IInstrumentHook[];

// @public (undocumented)
export class _InternalLogMessage {
    constructor(msgId: _InternalMessageId, msg: string, isUserAct?: boolean, properties?: Object);
    // (undocumented)
    static dataType: string;
    // (undocumented)
    message: string;
    // (undocumented)
    messageId: _InternalMessageId;
}

// @public
export const _InternalMessageId: EnumValue<typeof _eInternalMessageId>;

// @public (undocumented)
export type _InternalMessageId = number | _eInternalMessageId;

// @public
export interface IPerfEvent {
    childEvts?: IPerfEvent[];
    complete: () => void;
    exTime?: number;
    getCtx?: (key: string) => any;
    isAsync: boolean;
    isChildEvt: () => boolean;
    name: string;
    parent?: IPerfEvent;
    payload: any;
    setCtx?: (key: string, value: any) => void;
    start: number;
    time?: number;
}

// @public
export interface IPerfManager {
    create(src: string, payloadDetails?: () => any, isAsync?: boolean): IPerfEvent | null | undefined;
    fire(perfEvent: IPerfEvent): void;
    getCtx(key: string): any;
    setCtx(key: string, value: any): void;
}

// @public
export interface IPerfManagerProvider {
    getPerfMgr(): IPerfManager;
    setPerfMgr(perfMgr: IPerfManager): void;
}

// @public (undocumented)
export interface IPlugin {
    core?: IAppInsightsCore;
    readonly identifier: string;
    initialize: (config: IConfiguration, core: IAppInsightsCore, extensions: IPlugin[], pluginChain?: ITelemetryPluginChain) => void;
    isInitialized?: () => boolean;
    teardown?: (unloadCtx: IProcessTelemetryUnloadContext, unloadState?: ITelemetryUnloadState) => void | boolean;
    readonly version?: string;
}

// @public
export interface IProcessTelemetryContext extends IBaseProcessingContext {
    createNew: (plugins?: IPlugin[] | ITelemetryPluginChain, startAt?: IPlugin) => IProcessTelemetryContext;
    processNext: (env: ITelemetryItem) => boolean | void;
}

// @public
export interface IProcessTelemetryUnloadContext extends IBaseProcessingContext {
    createNew: (plugins?: IPlugin[] | ITelemetryPluginChain, startAt?: IPlugin) => IProcessTelemetryUnloadContext;
    processNext: (unloadState: ITelemetryUnloadState) => boolean | void;
}

// @public
export interface IProcessTelemetryUpdateContext extends IBaseProcessingContext {
    createNew: (plugins?: IPlugin[] | ITelemetryPluginChain, startAt?: IPlugin) => IProcessTelemetryUpdateContext;
    processNext: (updateState: ITelemetryUpdateState) => boolean | void;
}

// @public (undocumented)
export interface _IRegisteredEvents {
    // (undocumented)
    handler: any;
    // (undocumented)
    name: string;
}

// @public
export let isArray: <T = any>(obj: any) => obj is Array<T>;

// @public
export function isBeaconsSupported(): boolean;

// @public
export function isBoolean(value: any): value is boolean;

// @public
export function isDate(obj: any): obj is Date;

// @public
export function isError(obj: any): obj is Error;

// @public
export function isFetchSupported(withKeepAlive?: boolean): boolean;

// @public (undocumented)
export function isFunction(value: any): value is Function;

// @public
export function isIE(): boolean;

// @public (undocumented)
export function isNotNullOrUndefined<T>(value: T): value is T;

// @public (undocumented)
export function isNotTruthy(value: any): boolean;

// @public (undocumented)
export function isNotUndefined<T>(value: T): value is T;

// @public (undocumented)
export function isNullOrUndefined(value: any): value is null | undefined;

// @public
export function isNumber(value: any): value is number;

// @public (undocumented)
export function isObject<T>(value: T): value is T;

// @public
export function isPlainObject(value: any): boolean;

// @public
export function isReactNative(): boolean;

// @public (undocumented)
export function isSafari(userAgentStr?: string): boolean;

// @public
export function isSampledFlag(value: ITraceParent): boolean;

// @public
export function isString(value: any): value is string;

// @public
export function isSymbol(value: any): boolean;

// @public (undocumented)
export function isTruthy(value: any): boolean;

// @public (undocumented)
export function isTypeof(value: any, theType: string): boolean;

// @public (undocumented)
export function isUndefined(value: any): value is undefined;

// @public
export function isValidSpanId(value: string): boolean;

// @public
export function isValidTraceId(value: string): boolean;

// @public
export function isValidTraceParent(value: ITraceParent): boolean;

// @public
export function isXhrSupported(): boolean;

// @public (undocumented)
export interface ITelemetryInitializerContainer {
    addTelemetryInitializer(telemetryInitializer: TelemetryInitializerFunction): ITelemetryInitializerHandler | void;
}

// @public (undocumented)
export interface ITelemetryInitializerHandler {
    // (undocumented)
    remove(): void;
}

// @public
export interface ITelemetryItem {
    baseData?: {
        [key: string]: any;
    };
    baseType?: string;
    data?: ICustomProperties;
    ext?: {
        [key: string]: any;
    };
    iKey?: string;
    name: string;
    tags?: Tags & Tags[];
    time?: string;
    ver?: string;
}

// Warning: (ae-forgotten-export) The symbol "ITelemetryProcessor" needs to be exported by the entry point applicationinsights-core-js.d.ts
//
// @public
export interface ITelemetryPlugin extends ITelemetryProcessor, IPlugin {
    readonly priority: number;
    setNextPlugin?: (next: ITelemetryPlugin | ITelemetryPluginChain) => void;
}

// @public
export interface ITelemetryPluginChain extends ITelemetryProcessor {
    getNext: () => ITelemetryPluginChain;
    getPlugin: () => ITelemetryPlugin;
    unload?: (unloadCtx: IProcessTelemetryUnloadContext, unloadState: ITelemetryUnloadState) => void;
}

// @public (undocumented)
export interface ITelemetryUnloadState {
    // (undocumented)
    flushComplete?: boolean;
    // (undocumented)
    isAsync: boolean;
    // (undocumented)
    reason: TelemetryUnloadReason;
}

// @public (undocumented)
export interface ITelemetryUpdateState {
    added?: IPlugin[];
    reason: TelemetryUpdateReason;
    removed?: IPlugin[];
}

// @public
export interface ITraceParent {
    spanId: string;
    traceFlags: number;
    traceId: string;
    version: string;
}

// @public (undocumented)
export interface IUnloadableComponent {
    _doUnload?: (unloadCtx?: IProcessTelemetryUnloadContext, unloadState?: ITelemetryUnloadState, asyncCallback?: () => void) => void | boolean;
}

// @public (undocumented)
export interface IUnloadHandlerContainer {
    // (undocumented)
    add: (handler: UnloadHandler) => void;
    // (undocumented)
    run: (itemCtx: IProcessTelemetryUnloadContext, unloadState: ITelemetryUnloadState) => void;
}

// @public
export function _legacyCookieMgr(config?: IConfiguration, logger?: IDiagnosticLogger): ICookieMgr;

// @public (undocumented)
export const LoggingSeverity: EnumValue<typeof eLoggingSeverity>;

// @public (undocumented)
export type LoggingSeverity = number | eLoggingSeverity;

// @public
export function _logInternalMessage(logger: IDiagnosticLogger, severity: LoggingSeverity, message: _InternalLogMessage): void;

// @public (undocumented)
export function mergeEvtNamespace(theNamespace: string, namespaces?: string | string[] | null): string | string[];

// @public (undocumented)
export const MinChannelPriorty: number;

// @public
export function mwcRandom32(signed?: boolean): number;

// @public
export function mwcRandomSeed(value?: number): void;

// @public (undocumented)
export function newGuid(): string;

// @public
export function newId(maxLength?: number): string;

// @public
export function normalizeJsName(name: string): string;

// @public
export class NotificationManager implements INotificationManager {
    constructor(config?: IConfiguration);
    addNotificationListener(listener: INotificationListener): void;
    eventsDiscarded(events: ITelemetryItem[], reason: number): void;
    eventsSendRequest?(sendReason: number, isAsync: boolean): void;
    eventsSent(events: ITelemetryItem[]): void;
    // (undocumented)
    listeners: INotificationListener[];
    perfEvent?(perfEvent: IPerfEvent): void;
    removeNotificationListener(listener: INotificationListener): void;
}

export { objCreate }

// @public
export function objDefineAccessors<T>(target: any, prop: string, getProp?: () => T, setProp?: (v: T) => void): boolean;

// @public
export function objExtend<T2, T3, T4, T5, T6>(deepExtend?: boolean, obj2?: T2, obj3?: T3, obj4?: T4, obj5?: T5, obj6?: T6): T2 & T3 & T4 & T5 & T6;

// @public (undocumented)
export function objExtend<T1, T2, T3, T4, T5, T6>(obj1?: T1, obj2?: T2, obj3?: T3, obj4?: T4, obj5?: T5, obj6?: T6): T1 & T2 & T3 & T4 & T5 & T6;

// @public
export function objForEachKey<T = any>(target: T, callbackfn: (name: string, value: T[keyof T]) => void): void;

// @public (undocumented)
export const objFreeze: <T>(value: T) => T;

// @public
export function objKeys(obj: {}): string[];

// @public (undocumented)
export const objSeal: <T>(value: T) => T;

// @public (undocumented)
export function objToString(obj: any): any;

// @public
export function optimizeObject<T>(theObject: T): T;

// @public
export function parseTraceParent(value: string): ITraceParent;

// @public (undocumented)
export class PerfEvent implements IPerfEvent {
    constructor(name: string, payloadDetails: () => any, isAsync: boolean);
    // (undocumented)
    static ChildrenContextKey: string;
    // (undocumented)
    complete: () => void;
    exTime?: number;
    // (undocumented)
    getCtx?: (key: string) => any | null | undefined;
    isAsync: boolean;
    isChildEvt: () => boolean;
    name: string;
    // (undocumented)
    static ParentContextKey: string;
    payload: any;
    // (undocumented)
    setCtx?: (key: string, value: any) => void;
    start: number;
    time?: number;
}

// @public (undocumented)
export class PerfManager implements IPerfManager {
    constructor(manager?: INotificationManager);
    create(src: string, payload?: any, isAsync?: boolean): IPerfEvent | null | undefined;
    fire(perfEvent: IPerfEvent): void;
    getCtx(key: string): any;
    setCtx(key: string, value: any): void;
}

// @public
export function perfNow(): number;

// @public @deprecated
export class ProcessTelemetryContext implements IProcessTelemetryContext {
    constructor(pluginChain: ITelemetryPluginChain, config: IConfiguration, core: IAppInsightsCore, startAt?: IPlugin);
    core: () => IAppInsightsCore;
    createNew: (plugins?: IPlugin[] | ITelemetryPluginChain, startAt?: IPlugin) => IProcessTelemetryContext;
    diagLog: () => IDiagnosticLogger;
    getCfg: () => IConfiguration;
    // (undocumented)
    getConfig: (identifier: string, field: string, defaultValue?: number | string | boolean | string[] | RegExp[] | Function) => number | string | boolean | string[] | RegExp[] | Function;
    // (undocumented)
    getExtCfg: <T>(identifier: string, defaultValue?: T | any) => T;
    getNext: () => ITelemetryPluginChain;
    hasNext: () => boolean;
    iterate: <T extends ITelemetryPlugin = ITelemetryPlugin>(callback: (plugin: T) => void) => void;
    onComplete: (onComplete: () => void) => void;
    processNext: (env: ITelemetryItem) => boolean | void;
    setNext: (nextCtx: ITelemetryPluginChain) => void;
}

// @public
export function proxyAssign<T, S>(target: T, source: S, chkSet?: (name: string, isFunc?: boolean, source?: S, target?: T) => boolean): T;

// @public
export function proxyFunctionAs<T, S>(target: T, name: string, source: S | (() => S), theFunc: (keyof S), overwriteTarget?: boolean): void;

// @public
export function proxyFunctions<T, S>(target: T, source: S | (() => S), functionsToProxy: (keyof S)[], overwriteTarget?: boolean): T;

// @public
export function random32(signed?: boolean): number;

// @public
export function randomValue(maxValue: number): number;

// @public
export function removeEventHandler(eventName: string, callback: any, evtNamespace?: string | string[] | null): void;

// @public
export function removeEventListeners(events: string[], listener: any, evtNamespace?: string | string[]): void;

// @public
export function removePageHideEventListener(listener: any, evtNamespace?: string | string[] | null): void;

// @public
export function removePageShowEventListener(listener: any, evtNamespace?: string | string[] | null): void;

// @public
export function removePageUnloadEventListener(listener: any, evtNamespace?: string | string[]): void;

// @public
export function safeGetCookieMgr(core: IAppInsightsCore, config?: IConfiguration): ICookieMgr;

// @public (undocumented)
export function safeGetLogger(core: IAppInsightsCore, config?: IConfiguration): IDiagnosticLogger;

// @public (undocumented)
export function sendCustomEvent(evtName: string, cfg?: any, customDetails?: any): boolean;

// @public
export const enum SendRequestReason {
    ManualFlush = 1,
    MaxBatchSize = 10,
    MaxQueuedEvents = 20,
    NormalSchedule = 1,
    Resumed = 4,
    Retry = 5,
    SdkUnload = 6,
    SyncEvent = 3,
    Undefined = 0,
    Unload = 2
}

// @public @deprecated (undocumented)
export function setCookie(logger: IDiagnosticLogger, name: string, value: string, domain?: string): void;

// @public
export function setEnableEnvMocks(enabled: boolean): void;

// @public
export function setGblPerfMgr(perfManager: IPerfManager): void;

// @public
export function setValue<T, K extends keyof T>(target: T, field: K, value: T[K], valChk?: ((value: T[K]) => boolean) | null, srcChk?: ((value: T[K]) => boolean) | null): T[K];

// @public (undocumented)
export function sortPlugins<T = IPlugin>(plugins: T[]): T[];

// @public
export function strContains(value: string, search: string): boolean;

// @public
export function strEndsWith(value: string, search: string): boolean;

export { strFunction }

export { strObject }

export { strPrototype }

// @public
export function strStartsWith(value: string, checkValue: string): boolean;

// @public
export function strTrim(str: any): string;

export { strUndefined }

// @public (undocumented)
export interface Tags {
    // (undocumented)
    [key: string]: any;
}

// @public (undocumented)
export type TelemetryInitializerFunction = <T extends ITelemetryItem>(item: T) => boolean | void;

// @public
export const enum TelemetryUnloadReason {
    ManualTeardown = 0,
    PluginReplace = 2,
    PluginUnload = 1,
    SdkUnload = 50
}

// @public
export const enum TelemetryUpdateReason {
    PluginAdded = 16,
    PluginRemoved = 32,
    Unknown = 0
}

// @public (undocumented)
export function throwError(message: string): never;

// @public
export function _throwInternal(logger: IDiagnosticLogger, severity: LoggingSeverity, msgId: _InternalMessageId, msg: string, properties?: Object, isUserAct?: boolean): void;

// @public
export function toISOString(date: Date): string;

// @public (undocumented)
export function uaDisallowsSameSiteNone(userAgent: string): boolean;

// @public (undocumented)
export const Undefined = "undefined";

// @public
export function unloadComponents(components: any | IUnloadableComponent[], unloadCtx?: IProcessTelemetryUnloadContext, unloadState?: ITelemetryUnloadState, asyncCallback?: () => void): void | boolean;

// @public (undocumented)
export type UnloadHandler = (itemCtx: IProcessTelemetryUnloadContext, unloadState: ITelemetryUnloadState) => void;

// @public (undocumented)
export function useXDomainRequest(): boolean | undefined;

// @public
export function _warnToConsole(logger: IDiagnosticLogger, message: string): void;

// (No @packageDocumentation comment for this package)

```
