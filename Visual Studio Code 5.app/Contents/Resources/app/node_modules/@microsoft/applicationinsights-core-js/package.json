{"name": "@microsoft/applicationinsights-core-js", "author": "Microsoft Application Insights Team", "version": "2.8.15", "description": "Microsoft Application Insights Core Javascript SDK", "homepage": "https://github.com/microsoft/ApplicationInsights-JS#readme", "keywords": ["azure", "cloud", "script errors", "microsoft", "application insights", "browser performance monitoring", "web analytics"], "main": "dist/applicationinsights-core-js.js", "module": "dist-esm/applicationinsights-core-js.js", "types": "types/applicationinsights-core-js.d.ts", "scripts": {"clean": "grunt clean", "build": "npm run build:esm && npm run build:browser && npm run sri && npm run dtsgen", "build:esm": "grunt core", "build:browser": "rollup -c rollup.config.js", "rebuild": "npm run build", "test": "grunt coreunittest", "mintest": "grunt core-mintest", "perftest": "grunt coreperftest", "lint": "tslint -p tsconfig.json", "dtsgen": "api-extractor run --local && node ../../scripts/dtsgen.js \"Microsoft Application Insights Core Javascript SDK\"", "sri": "node ../../tools/subResourceIntegrity/generateIntegrityFile.js", "ai-min": "grunt core-min", "ai-restore": "grunt core-restore", "npm-pack": "npm pack"}, "repository": {"type": "git", "url": "https://github.com/microsoft/ApplicationInsights-JS/tree/master/shared/AppInsightsCore"}, "license": "MIT", "sideEffects": false, "devDependencies": {"@microsoft/ai-test-framework": "0.0.1", "@microsoft/applicationinsights-rollup-plugin-uglify3-js": "1.0.0", "@microsoft/applicationinsights-rollup-es3": "1.1.3", "@microsoft/api-extractor": "^7.18.1", "grunt": "^1.5.3", "grunt-cli": "^1.4.3", "grunt-contrib-qunit": "^6.2.1", "@nevware21/grunt-ts-plugin": "^0.4.3", "@nevware21/grunt-eslint-ts": "^0.2.2", "globby": "^11.0.0", "magic-string": "^0.25.7", "pako": "^2.0.3", "@rollup/plugin-commonjs": "^18.0.0", "@rollup/plugin-node-resolve": "^11.2.1", "@rollup/plugin-replace": "^2.3.3", "rollup-plugin-cleanup": "^3.2.1", "rollup": "^2.32.0", "typescript": "^4.9.3", "tslib": "^2.0.0", "qunit": "^2.11.2", "sinon": "^7.3.1"}, "peerDependencies": {"tslib": "*"}, "dependencies": {"@microsoft/applicationinsights-shims": "2.0.2", "@microsoft/dynamicproto-js": "^1.1.9"}}