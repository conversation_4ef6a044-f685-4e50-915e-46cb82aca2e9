{"name": "@microsoft/applicationinsights-shims", "author": "Microsoft Application Insights Team", "version": "2.0.2", "description": "Microsoft Application Insights JavaScript SDK - Shim functions", "homepage": "https://github.com/microsoft/ApplicationInsights-JS/tree/master/tools/shims", "keywords": ["azure", "cloud", "microsoft", "application insights", "tslib", "es3"], "main": "dist/umd/applicationinsights-shims.js", "module": "dist-esm/applicationinsights-shims.js", "types": "types/applicationinsights-shims.d.ts", "scripts": {"clean": "grunt clean", "build": "npm run build:esm && npm run build:bundle", "build:esm": "grunt shims", "build:bundle": "rollup -c rollup.config.js", "rebuild": "npm run build", "test": "grunt shimstest", "lint": "tslint -p tsconfig.json"}, "repository": {"type": "git", "url": "https://github.com/microsoft/ApplicationInsights-JS/tree/master/tools/shims"}, "license": "MIT", "sideEffects": ["**/TsLibGlobals.js", "**/TsLibGlobals.ts"], "devDependencies": {"@microsoft/ai-test-framework": "0.0.1", "@microsoft/applicationinsights-rollup-plugin-uglify3-js": "1.0.0", "@microsoft/applicationinsights-rollup-es3": "1.1.3", "grunt": "^1.5.3", "grunt-cli": "^1.4.3", "grunt-contrib-qunit": "^5.0.1", "@nevware21/grunt-ts-plugin": "^0.4.3", "@nevware21/grunt-eslint-ts": "^0.2.2", "@rollup/plugin-commonjs": "^18.0.0", "@rollup/plugin-node-resolve": "^11.2.1", "@rollup/plugin-replace": "^2.3.3", "rollup-plugin-minify-es": "^1.1.1", "rollup": "^2.32.0", "typescript": "^4.3.4"}, "dependencies": {}}