/**
 * Copyright (c) 2014-2024 The xterm.js authors. All rights reserved.
 * @license MIT
 *
 * Copyright (c) 2012-2013, <PERSON> (MIT License)
 * @license MIT
 *
 * Originally forked from (with the author's permission):
 *   <PERSON><PERSON><PERSON>'s javascript vt100 for jslinux:
 *   http://bellard.org/jslinux/
 *   Copyright (c) 2011 F<PERSON><PERSON>
 */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var he=class{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout(()=>{throw e.stack?Z.isErrorNoTelemetry(e)?new Z(e.message+`

`+e.stack):new Error(e.message+`

`+e.stack):e},0)}}addListener(e){return this.listeners.push(e),()=>{this._removeListener(e)}}emit(e){this.listeners.forEach(t=>{t(e)})}_removeListener(e){this.listeners.splice(this.listeners.indexOf(e),1)}setUnexpectedErrorHandler(e){this.unexpectedErrorHandler=e}getUnexpectedErrorHandler(){return this.unexpectedErrorHandler}onUnexpectedError(e){this.unexpectedErrorHandler(e),this.emit(e)}onUnexpectedExternalError(e){this.unexpectedErrorHandler(e)}},ze=new he;function ee(r){Ve(r)||ze.onUnexpectedError(r)}var fe="Canceled";function Ve(r){return r instanceof X?!0:r instanceof Error&&r.name===fe&&r.message===fe}var X=class extends Error{constructor(){super(fe),this.name=this.message}};var Z=class r extends Error{constructor(e){super(e),this.name="CodeExpectedError"}static fromError(e){if(e instanceof r)return e;let t=new r;return t.message=e.message,t.stack=e.stack,t}static isErrorNoTelemetry(e){return e.name==="CodeExpectedError"}};function pe(r,e){let t=this,n=!1,i;return function(){if(n)return i;if(n=!0,e)try{i=r.apply(t,arguments)}finally{e()}else i=r.apply(t,arguments);return i}}function je(r,e,t=0,n=r.length){let i=t,s=n;for(;i<s;){let l=Math.floor((i+s)/2);e(r[l])?i=l+1:s=l}return i-1}var te=class te{constructor(e){this._array=e;this._findLastMonotonousLastIdx=0}findLastMonotonous(e){if(te.assertInvariants){if(this._prevFindLastPredicate){for(let n of this._array)if(this._prevFindLastPredicate(n)&&!e(n))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this._prevFindLastPredicate=e}let t=je(this._array,e,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=t+1,t===-1?void 0:this._array[t]}};te.assertInvariants=!1;var ye=te;var Ee;(u=>{function r(a){return a<0}u.isLessThan=r;function e(a){return a<=0}u.isLessThanOrEqual=e;function t(a){return a>0}u.isGreaterThan=t;function n(a){return a===0}u.isNeitherLessOrGreaterThan=n,u.greaterThan=1,u.lessThan=-1,u.neitherLessOrGreaterThan=0})(Ee||={});function De(r,e){return(t,n)=>e(r(t),r(n))}var we=(r,e)=>r-e;var F=class F{constructor(e){this.iterate=e}forEach(e){this.iterate(t=>(e(t),!0))}toArray(){let e=[];return this.iterate(t=>(e.push(t),!0)),e}filter(e){return new F(t=>this.iterate(n=>e(n)?t(n):!0))}map(e){return new F(t=>this.iterate(n=>t(e(n))))}some(e){let t=!1;return this.iterate(n=>(t=e(n),!t)),t}findFirst(e){let t;return this.iterate(n=>e(n)?(t=n,!1):!0),t}findLast(e){let t;return this.iterate(n=>(e(n)&&(t=n),!0)),t}findLastMaxBy(e){let t,n=!0;return this.iterate(i=>((n||Ee.isGreaterThan(e(i,t)))&&(n=!1,t=i),!0)),t}};F.empty=new F(e=>{});var Ie=F;function Ce(r,e){let t=Object.create(null);for(let n of r){let i=e(n),s=t[i];s||(s=t[i]=[]),s.push(n)}return t}var Le,ke,Se=class{constructor(e,t){this.toKey=t;this._map=new Map;this[Le]="SetWithKey";for(let n of e)this.add(n)}get size(){return this._map.size}add(e){let t=this.toKey(e);return this._map.set(t,e),this}delete(e){return this._map.delete(this.toKey(e))}has(e){return this._map.has(this.toKey(e))}*entries(){for(let e of this._map.values())yield[e,e]}keys(){return this.values()}*values(){for(let e of this._map.values())yield e}clear(){this._map.clear()}forEach(e,t){this._map.forEach(n=>e.call(t,n,n,this))}[(ke=Symbol.iterator,Le=Symbol.toStringTag,ke)](){return this.values()}};var ne=class{constructor(){this.map=new Map}add(e,t){let n=this.map.get(e);n||(n=new Set,this.map.set(e,n)),n.add(t)}delete(e,t){let n=this.map.get(e);n&&(n.delete(t),n.size===0&&this.map.delete(e))}forEach(e,t){let n=this.map.get(e);n&&n.forEach(t)}get(e){let t=this.map.get(e);return t||new Set}};var Te;(j=>{function r(T){return T&&typeof T=="object"&&typeof T[Symbol.iterator]=="function"}j.is=r;let e=Object.freeze([]);function t(){return e}j.empty=t;function*n(T){yield T}j.single=n;function i(T){return r(T)?T:n(T)}j.wrap=i;function s(T){return T||e}j.from=s;function*l(T){for(let g=T.length-1;g>=0;g--)yield T[g]}j.reverse=l;function u(T){return!T||T[Symbol.iterator]().next().done===!0}j.isEmpty=u;function a(T){return T[Symbol.iterator]().next().value}j.first=a;function f(T,g){let x=0;for(let C of T)if(g(C,x++))return!0;return!1}j.some=f;function v(T,g){for(let x of T)if(g(x))return x}j.find=v;function*m(T,g){for(let x of T)g(x)&&(yield x)}j.filter=m;function*I(T,g){let x=0;for(let C of T)yield g(C,x++)}j.map=I;function*E(T,g){let x=0;for(let C of T)yield*g(C,x++)}j.flatMap=E;function*k(...T){for(let g of T)yield*g}j.concat=k;function R(T,g,x){let C=x;for(let K of T)C=g(C,K);return C}j.reduce=R;function*D(T,g,x=T.length){for(g<0&&(g+=T.length),x<0?x+=T.length:x>T.length&&(x=T.length);g<x;g++)yield T[g]}j.slice=D;function w(T,g=Number.POSITIVE_INFINITY){let x=[];if(g===0)return[x,T];let C=T[Symbol.iterator]();for(let K=0;K<g;K++){let ce=C.next();if(ce.done)return[x,j.empty()];x.push(ce.value)}return[x,{[Symbol.iterator](){return C}}]}j.consume=w;async function y(T){let g=[];for await(let x of T)g.push(x);return Promise.resolve(g)}j.asyncToArray=y})(Te||={});var Ke=!1,P=null,ie=class ie{constructor(){this.livingDisposables=new Map}getDisposableData(e){let t=this.livingDisposables.get(e);return t||(t={parent:null,source:null,isSingleton:!1,value:e,idx:ie.idx++},this.livingDisposables.set(e,t)),t}trackDisposable(e){let t=this.getDisposableData(e);t.source||(t.source=new Error().stack)}setParent(e,t){let n=this.getDisposableData(e);n.parent=t}markAsDisposed(e){this.livingDisposables.delete(e)}markAsSingleton(e){this.getDisposableData(e).isSingleton=!0}getRootParent(e,t){let n=t.get(e);if(n)return n;let i=e.parent?this.getRootParent(this.getDisposableData(e.parent),t):e;return t.set(e,i),i}getTrackedDisposables(){let e=new Map;return[...this.livingDisposables.entries()].filter(([,n])=>n.source!==null&&!this.getRootParent(n,e).isSingleton).flatMap(([n])=>n)}computeLeakingDisposables(e=10,t){let n;if(t)n=t;else{let a=new Map,f=[...this.livingDisposables.values()].filter(m=>m.source!==null&&!this.getRootParent(m,a).isSingleton);if(f.length===0)return;let v=new Set(f.map(m=>m.value));if(n=f.filter(m=>!(m.parent&&v.has(m.parent))),n.length===0)throw new Error("There are cyclic diposable chains!")}if(!n)return;function i(a){function f(m,I){for(;m.length>0&&I.some(E=>typeof E=="string"?E===m[0]:m[0].match(E));)m.shift()}let v=a.source.split(`
`).map(m=>m.trim().replace("at ","")).filter(m=>m!=="");return f(v,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),v.reverse()}let s=new ne;for(let a of n){let f=i(a);for(let v=0;v<=f.length;v++)s.add(f.slice(0,v).join(`
`),a)}n.sort(De(a=>a.idx,we));let l="",u=0;for(let a of n.slice(0,e)){u++;let f=i(a),v=[];for(let m=0;m<f.length;m++){let I=f[m];I=`(shared with ${s.get(f.slice(0,m+1).join(`
`)).size}/${n.length} leaks) at ${I}`;let k=s.get(f.slice(0,m).join(`
`)),R=Ce([...k].map(D=>i(D)[m]),D=>D);delete R[f[m]];for(let[D,w]of Object.entries(R))v.unshift(`    - stacktraces of ${w.length} other leaks continue with ${D}`);v.unshift(I)}l+=`


==================== Leaking disposable ${u}/${n.length}: ${a.value.constructor.name} ====================
${v.join(`
`)}
============================================================

`}return n.length>e&&(l+=`


... and ${n.length-e} more leaking disposables

`),{leaks:n,details:l}}};ie.idx=0;var Re=ie;function Qe(r){P=r}if(Ke){let r="__is_disposable_tracked__";Qe(new class{trackDisposable(e){let t=new Error("Potentially leaked disposable").stack;setTimeout(()=>{e[r]||console.log(t)},3e3)}setParent(e,t){if(e&&e!==O.None)try{e[r]=!0}catch{}}markAsDisposed(e){if(e&&e!==O.None)try{e[r]=!0}catch{}}markAsSingleton(e){}})}function re(r){return P?.trackDisposable(r),r}function oe(r){P?.markAsDisposed(r)}function $(r,e){P?.setParent(r,e)}function $e(r,e){if(P)for(let t of r)P.setParent(t,e)}function U(r){if(Te.is(r)){let e=[];for(let t of r)if(t)try{t.dispose()}catch(n){e.push(n)}if(e.length===1)throw e[0];if(e.length>1)throw new AggregateError(e,"Encountered errors while disposing of store");return Array.isArray(r)?[]:r}else if(r)return r.dispose(),r}function ae(...r){let e=q(()=>U(r));return $e(r,e),e}function q(r){let e=re({dispose:pe(()=>{oe(e),r()})});return e}var se=class se{constructor(){this._toDispose=new Set;this._isDisposed=!1;re(this)}dispose(){this._isDisposed||(oe(this),this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(this._toDispose.size!==0)try{U(this._toDispose)}finally{this._toDispose.clear()}}add(e){if(!e)return e;if(e===this)throw new Error("Cannot register a disposable on itself!");return $(e,this),this._isDisposed?se.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(e),e}delete(e){if(e){if(e===this)throw new Error("Cannot dispose a disposable on itself!");this._toDispose.delete(e),e.dispose()}}deleteAndLeak(e){e&&this._toDispose.has(e)&&(this._toDispose.delete(e),$(e,null))}};se.DISABLE_DISPOSED_WARNING=!1;var N=se,O=class{constructor(){this._store=new N;re(this),$(this._store,this)}dispose(){oe(this),this._store.dispose()}_register(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(e)}};O.None=Object.freeze({dispose(){}});var H=class{constructor(){this._isDisposed=!1;re(this)}get value(){return this._isDisposed?void 0:this._value}set value(e){this._isDisposed||e===this._value||(this._value?.dispose(),e&&$(e,this),this._value=e)}clear(){this.value=void 0}dispose(){this._isDisposed=!0,oe(this),this._value?.dispose(),this._value=void 0}clearAndLeak(){let e=this._value;return this._value=void 0,e&&$(e,null),e}};var W=class W{constructor(e){this.element=e,this.next=W.Undefined,this.prev=W.Undefined}};W.Undefined=new W(void 0);var Oe=W;var He=globalThis.performance&&typeof globalThis.performance.now=="function",le=class r{static create(e){return new r(e)}constructor(e){this._now=He&&e===!1?Date.now:globalThis.performance.now.bind(globalThis.performance),this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}reset(){this._startTime=this._now(),this._stopTime=-1}elapsed(){return this._stopTime!==-1?this._stopTime-this._startTime:this._now()-this._startTime}};var qe=!1,Ae=!1,Be=!1,Ge;(G=>{G.None=()=>O.None;function e(h){if(Be){let{onDidAddListener:o}=h,c=B.create(),d=0;h.onDidAddListener=()=>{++d===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),c.print()),o?.()}}}function t(h,o){return I(h,()=>{},0,void 0,!0,void 0,o)}G.defer=t;function n(h){return(o,c=null,d)=>{let p=!1,b;return b=h(_=>{if(!p)return b?b.dispose():p=!0,o.call(c,_)},null,d),p&&b.dispose(),b}}G.once=n;function i(h,o,c){return v((d,p=null,b)=>h(_=>d.call(p,o(_)),null,b),c)}G.map=i;function s(h,o,c){return v((d,p=null,b)=>h(_=>{o(_),d.call(p,_)},null,b),c)}G.forEach=s;function l(h,o,c){return v((d,p=null,b)=>h(_=>o(_)&&d.call(p,_),null,b),c)}G.filter=l;function u(h){return h}G.signal=u;function a(...h){return(o,c=null,d)=>{let p=ae(...h.map(b=>b(_=>o.call(c,_))));return m(p,d)}}G.any=a;function f(h,o,c,d){let p=c;return i(h,b=>(p=o(p,b),p),d)}G.reduce=f;function v(h,o){let c,d={onWillAddFirstListener(){c=h(p.fire,p)},onDidRemoveLastListener(){c?.dispose()}};o||e(d);let p=new A(d);return o?.add(p),p.event}function m(h,o){return o instanceof Array?o.push(h):o&&o.add(h),h}function I(h,o,c=100,d=!1,p=!1,b,_){let S,L,M,Y=0,Q,xe={leakWarningThreshold:b,onWillAddFirstListener(){S=h(Ue=>{Y++,L=o(L,Ue),d&&!M&&(J.fire(L),L=void 0),Q=()=>{let We=L;L=void 0,M=void 0,(!d||Y>1)&&J.fire(We),Y=0},typeof c=="number"?(clearTimeout(M),M=setTimeout(Q,c)):M===void 0&&(M=0,queueMicrotask(Q))})},onWillRemoveListener(){p&&Y>0&&Q?.()},onDidRemoveLastListener(){Q=void 0,S.dispose()}};_||e(xe);let J=new A(xe);return _?.add(J),J.event}G.debounce=I;function E(h,o=0,c){return G.debounce(h,(d,p)=>d?(d.push(p),d):[p],o,void 0,!0,void 0,c)}G.accumulate=E;function k(h,o=(d,p)=>d===p,c){let d=!0,p;return l(h,b=>{let _=d||!o(b,p);return d=!1,p=b,_},c)}G.latch=k;function R(h,o,c){return[G.filter(h,o,c),G.filter(h,d=>!o(d),c)]}G.split=R;function D(h,o=!1,c=[],d){let p=c.slice(),b=h(L=>{p?p.push(L):S.fire(L)});d&&d.add(b);let _=()=>{p?.forEach(L=>S.fire(L)),p=null},S=new A({onWillAddFirstListener(){b||(b=h(L=>S.fire(L)),d&&d.add(b))},onDidAddFirstListener(){p&&(o?setTimeout(_):_())},onDidRemoveLastListener(){b&&b.dispose(),b=null}});return d&&d.add(S),S.event}G.buffer=D;function w(h,o){return(d,p,b)=>{let _=o(new j);return h(function(S){let L=_.evaluate(S);L!==y&&d.call(p,L)},void 0,b)}}G.chain=w;let y=Symbol("HaltChainable");class j{constructor(){this.steps=[]}map(o){return this.steps.push(o),this}forEach(o){return this.steps.push(c=>(o(c),c)),this}filter(o){return this.steps.push(c=>o(c)?c:y),this}reduce(o,c){let d=c;return this.steps.push(p=>(d=o(d,p),d)),this}latch(o=(c,d)=>c===d){let c=!0,d;return this.steps.push(p=>{let b=c||!o(p,d);return c=!1,d=p,b?p:y}),this}evaluate(o){for(let c of this.steps)if(o=c(o),o===y)break;return o}}function T(h,o,c=d=>d){let d=(...S)=>_.fire(c(...S)),p=()=>h.on(o,d),b=()=>h.removeListener(o,d),_=new A({onWillAddFirstListener:p,onDidRemoveLastListener:b});return _.event}G.fromNodeEventEmitter=T;function g(h,o,c=d=>d){let d=(...S)=>_.fire(c(...S)),p=()=>h.addEventListener(o,d),b=()=>h.removeEventListener(o,d),_=new A({onWillAddFirstListener:p,onDidRemoveLastListener:b});return _.event}G.fromDOMEventEmitter=g;function x(h){return new Promise(o=>n(h)(o))}G.toPromise=x;function C(h){let o=new A;return h.then(c=>{o.fire(c)},()=>{o.fire(void 0)}).finally(()=>{o.dispose()}),o.event}G.fromPromise=C;function K(h,o){return h(c=>o.fire(c))}G.forward=K;function ce(h,o,c){return o(c),h(d=>o(d))}G.runAndSubscribe=ce;class Ne{constructor(o,c){this._observable=o;this._counter=0;this._hasChanged=!1;let d={onWillAddFirstListener:()=>{o.addObserver(this)},onDidRemoveLastListener:()=>{o.removeObserver(this)}};c||e(d),this.emitter=new A(d),c&&c.add(this.emitter)}beginUpdate(o){this._counter++}handlePossibleChange(o){}handleChange(o,c){this._hasChanged=!0}endUpdate(o){this._counter--,this._counter===0&&(this._observable.reportChanges(),this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this._observable.get())))}}function tt(h,o){return new Ne(h,o).emitter.event}G.fromObservable=tt;function nt(h){return(o,c,d)=>{let p=0,b=!1,_={beginUpdate(){p++},endUpdate(){p--,p===0&&(h.reportChanges(),b&&(b=!1,o.call(c)))},handlePossibleChange(){},handleChange(){b=!0}};h.addObserver(_),h.reportChanges();let S={dispose(){h.removeObserver(_)}};return d instanceof N?d.add(S):Array.isArray(d)&&d.push(S),S}}G.fromObservableLight=nt})(Ge||={});var z=class z{constructor(e){this.listenerCount=0;this.invocationCount=0;this.elapsedOverall=0;this.durations=[];this.name=`${e}_${z._idPool++}`,z.all.add(this)}start(e){this._stopWatch=new le,this.listenerCount=e}stop(){if(this._stopWatch){let e=this._stopWatch.elapsed();this.durations.push(e),this.elapsedOverall+=e,this.invocationCount+=1,this._stopWatch=void 0}}};z.all=new Set,z._idPool=0;var me=z,Me=-1;var de=class de{constructor(e,t,n=(de._idPool++).toString(16).padStart(3,"0")){this._errorHandler=e;this.threshold=t;this.name=n;this._warnCountdown=0}dispose(){this._stacks?.clear()}check(e,t){let n=this.threshold;if(n<=0||t<n)return;this._stacks||(this._stacks=new Map);let i=this._stacks.get(e.value)||0;if(this._stacks.set(e.value,i+1),this._warnCountdown-=1,this._warnCountdown<=0){this._warnCountdown=n*.5;let[s,l]=this.getMostFrequentStack(),u=`[${this.name}] potential listener LEAK detected, having ${t} listeners already. MOST frequent listener (${l}):`;console.warn(u),console.warn(s);let a=new be(u,s);this._errorHandler(a)}return()=>{let s=this._stacks.get(e.value)||0;this._stacks.set(e.value,s-1)}}getMostFrequentStack(){if(!this._stacks)return;let e,t=0;for(let[n,i]of this._stacks)(!e||t<i)&&(e=[n,i],t=i);return e}};de._idPool=1;var ve=de,B=class r{constructor(e){this.value=e}static create(){let e=new Error;return new r(e.stack??"")}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},be=class extends Error{constructor(e,t){super(e),this.name="ListenerLeakError",this.stack=t}},ge=class extends Error{constructor(e,t){super(e),this.name="ListenerRefusalError",this.stack=t}},Ye=0,V=class{constructor(e){this.value=e;this.id=Ye++}},Je=2,Xe=(r,e)=>{if(r instanceof V)e(r);else for(let t=0;t<r.length;t++){let n=r[t];n&&e(n)}},ue;if(qe){let r=[];setInterval(()=>{r.length!==0&&(console.warn("[LEAKING LISTENERS] GC'ed these listeners that were NOT yet disposed:"),console.warn(r.join(`
`)),r.length=0)},3e3),ue=new FinalizationRegistry(e=>{typeof e=="string"&&r.push(e)})}var A=class{constructor(e){this._size=0;this._options=e,this._leakageMon=Me>0||this._options?.leakWarningThreshold?new ve(e?.onListenerError??ee,this._options?.leakWarningThreshold??Me):void 0,this._perfMon=this._options?._profName?new me(this._options._profName):void 0,this._deliveryQueue=this._options?.deliveryQueue}dispose(){if(!this._disposed){if(this._disposed=!0,this._deliveryQueue?.current===this&&this._deliveryQueue.reset(),this._listeners){if(Ae){let e=this._listeners;queueMicrotask(()=>{Xe(e,t=>t.stack?.print())})}this._listeners=void 0,this._size=0}this._options?.onDidRemoveLastListener?.(),this._leakageMon?.dispose()}}get event(){return this._event??=(e,t,n)=>{if(this._leakageMon&&this._size>this._leakageMon.threshold**2){let a=`[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this._size} vs ${this._leakageMon.threshold})`;console.warn(a);let f=this._leakageMon.getMostFrequentStack()??["UNKNOWN stack",-1],v=new ge(`${a}. HINT: Stack shows most frequent listener (${f[1]}-times)`,f[0]);return(this._options?.onListenerError||ee)(v),O.None}if(this._disposed)return O.None;t&&(e=e.bind(t));let i=new V(e),s,l;this._leakageMon&&this._size>=Math.ceil(this._leakageMon.threshold*.2)&&(i.stack=B.create(),s=this._leakageMon.check(i.stack,this._size+1)),Ae&&(i.stack=l??B.create()),this._listeners?this._listeners instanceof V?(this._deliveryQueue??=new _e,this._listeners=[this._listeners,i]):this._listeners.push(i):(this._options?.onWillAddFirstListener?.(this),this._listeners=i,this._options?.onDidAddFirstListener?.(this)),this._size++;let u=q(()=>{ue?.unregister(u),s?.(),this._removeListener(i)});if(n instanceof N?n.add(u):Array.isArray(n)&&n.push(u),ue){let a=new Error().stack.split(`
`).slice(2,3).join(`
`).trim(),f=/(file:|vscode-file:\/\/vscode-app)?(\/[^:]*:\d+:\d+)/.exec(a);ue.register(u,f?.[2]??a,u)}return u},this._event}_removeListener(e){if(this._options?.onWillRemoveListener?.(this),!this._listeners)return;if(this._size===1){this._listeners=void 0,this._options?.onDidRemoveLastListener?.(this),this._size=0;return}let t=this._listeners,n=t.indexOf(e);if(n===-1)throw console.log("disposed?",this._disposed),console.log("size?",this._size),console.log("arr?",JSON.stringify(this._listeners)),new Error("Attempted to dispose unknown listener");this._size--,t[n]=void 0;let i=this._deliveryQueue.current===this;if(this._size*Je<=t.length){let s=0;for(let l=0;l<t.length;l++)t[l]?t[s++]=t[l]:i&&(this._deliveryQueue.end--,s<this._deliveryQueue.i&&this._deliveryQueue.i--);t.length=s}}_deliver(e,t){if(!e)return;let n=this._options?.onListenerError||ee;if(!n){e.value(t);return}try{e.value(t)}catch(i){n(i)}}_deliverQueue(e){let t=e.current._listeners;for(;e.i<e.end;)this._deliver(t[e.i++],e.value);e.reset()}fire(e){if(this._deliveryQueue?.current&&(this._deliverQueue(this._deliveryQueue),this._perfMon?.stop()),this._perfMon?.start(this._size),this._listeners)if(this._listeners instanceof V)this._deliver(this._listeners,e);else{let t=this._deliveryQueue;t.enqueue(this,e,this._listeners.length),this._deliverQueue(t)}this._perfMon?.stop()}hasListeners(){return this._size>0}};var _e=class{constructor(){this.i=-1;this.end=0}enqueue(e,t,n){this.i=0,this.end=n,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}};var Fe=" ~!@#$%^&*()+`-=[]{}|\\;:\"',./<>?",Ze=15*1e3,et=1e3,Pe=class extends O{constructor(t){super();this._highlightedLines=new Set;this._highlightDecorations=[];this._selectedDecoration=this._register(new H);this._linesCacheTimeoutId=0;this._linesCacheDisposables=new H;this._onDidChangeResults=this._register(new A);this.onDidChangeResults=this._onDidChangeResults.event;this._highlightLimit=t?.highlightLimit??et}activate(t){this._terminal=t,this._register(this._terminal.onWriteParsed(()=>this._updateMatches())),this._register(this._terminal.onResize(()=>this._updateMatches())),this._register(q(()=>this.clearDecorations()))}_updateMatches(){this._highlightTimeout&&window.clearTimeout(this._highlightTimeout),this._cachedSearchTerm&&this._lastSearchOptions?.decorations&&(this._highlightTimeout=setTimeout(()=>{let t=this._cachedSearchTerm;this._cachedSearchTerm=void 0,this.findPrevious(t,{...this._lastSearchOptions,incremental:!0,noScroll:!0})},200))}clearDecorations(t){this._selectedDecoration.clear(),U(this._highlightDecorations),this._highlightDecorations=[],this._highlightedLines.clear(),t||(this._cachedSearchTerm=void 0)}clearActiveDecoration(){this._selectedDecoration.clear()}findNext(t,n){if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");let i=this._lastSearchOptions?this._didOptionsChange(this._lastSearchOptions,n):!0;this._lastSearchOptions=n,n?.decorations&&(this._cachedSearchTerm===void 0||t!==this._cachedSearchTerm||i)&&this._highlightAllMatches(t,n);let s=this._findNextAndSelect(t,n);return this._fireResults(n),this._cachedSearchTerm=t,s}_highlightAllMatches(t,n){if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");if(!t||t.length===0){this.clearDecorations();return}n=n||{},this.clearDecorations(!0);let i=[],s,l=this._find(t,0,0,n);for(;l&&(s?.row!==l.row||s?.col!==l.col)&&!(i.length>=this._highlightLimit);)s=l,i.push(s),l=this._find(t,s.col+s.term.length>=this._terminal.cols?s.row+1:s.row,s.col+s.term.length>=this._terminal.cols?0:s.col+1,n);for(let u of i){let a=this._createResultDecorations(u,n.decorations,!1);if(a)for(let f of a)this._storeDecoration(f,u)}}_storeDecoration(t,n){this._highlightedLines.add(t.marker.line),this._highlightDecorations.push({decoration:t,match:n,dispose(){t.dispose()}})}_find(t,n,i,s){if(!this._terminal||!t||t.length===0){this._terminal?.clearSelection(),this.clearDecorations();return}if(i>this._terminal.cols)throw new Error(`Invalid col: ${i} to search in terminal of ${this._terminal.cols} cols`);let l;this._initLinesCache();let u={startRow:n,startCol:i};if(l=this._findInLine(t,u,s),!l)for(let a=n+1;a<this._terminal.buffer.active.baseY+this._terminal.rows&&(u.startRow=a,u.startCol=0,l=this._findInLine(t,u,s),!l);a++);return l}_findNextAndSelect(t,n){if(!this._terminal||!t||t.length===0)return this._terminal?.clearSelection(),this.clearDecorations(),!1;let i=this._terminal.getSelectionPosition();this._terminal.clearSelection();let s=0,l=0;i&&(this._cachedSearchTerm===t?(s=i.end.x,l=i.end.y):(s=i.start.x,l=i.start.y)),this._initLinesCache();let u={startRow:l,startCol:s},a=this._findInLine(t,u,n);if(!a)for(let f=l+1;f<this._terminal.buffer.active.baseY+this._terminal.rows&&(u.startRow=f,u.startCol=0,a=this._findInLine(t,u,n),!a);f++);if(!a&&l!==0)for(let f=0;f<l&&(u.startRow=f,u.startCol=0,a=this._findInLine(t,u,n),!a);f++);return!a&&i&&(u.startRow=i.start.y,u.startCol=0,a=this._findInLine(t,u,n)),this._selectResult(a,n?.decorations,n?.noScroll)}findPrevious(t,n){if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");let i=this._lastSearchOptions?this._didOptionsChange(this._lastSearchOptions,n):!0;this._lastSearchOptions=n,n?.decorations&&(this._cachedSearchTerm===void 0||t!==this._cachedSearchTerm||i)&&this._highlightAllMatches(t,n);let s=this._findPreviousAndSelect(t,n);return this._fireResults(n),this._cachedSearchTerm=t,s}_didOptionsChange(t,n){return n?t.caseSensitive!==n.caseSensitive||t.regex!==n.regex||t.wholeWord!==n.wholeWord:!1}_fireResults(t){if(t?.decorations){let n=-1;if(this._selectedDecoration.value){let i=this._selectedDecoration.value.match;for(let s=0;s<this._highlightDecorations.length;s++){let l=this._highlightDecorations[s].match;if(l.row===i.row&&l.col===i.col&&l.size===i.size){n=s;break}}}this._onDidChangeResults.fire({resultIndex:n,resultCount:this._highlightDecorations.length})}}_findPreviousAndSelect(t,n){if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");if(!this._terminal||!t||t.length===0)return this._terminal?.clearSelection(),this.clearDecorations(),!1;let i=this._terminal.getSelectionPosition();this._terminal.clearSelection();let s=this._terminal.buffer.active.baseY+this._terminal.rows-1,l=this._terminal.cols,u=!0;this._initLinesCache();let a={startRow:s,startCol:l},f;if(i&&(a.startRow=s=i.start.y,a.startCol=l=i.start.x,this._cachedSearchTerm!==t&&(f=this._findInLine(t,a,n,!1),f||(a.startRow=s=i.end.y,a.startCol=l=i.end.x))),f||(f=this._findInLine(t,a,n,u)),!f){a.startCol=Math.max(a.startCol,this._terminal.cols);for(let v=s-1;v>=0&&(a.startRow=v,f=this._findInLine(t,a,n,u),!f);v--);}if(!f&&s!==this._terminal.buffer.active.baseY+this._terminal.rows-1)for(let v=this._terminal.buffer.active.baseY+this._terminal.rows-1;v>=s&&(a.startRow=v,f=this._findInLine(t,a,n,u),!f);v--);return this._selectResult(f,n?.decorations,n?.noScroll)}_initLinesCache(){let t=this._terminal;this._linesCache||(this._linesCache=new Array(t.buffer.active.length),this._linesCacheDisposables.value=ae(t.onLineFeed(()=>this._destroyLinesCache()),t.onCursorMove(()=>this._destroyLinesCache()),t.onResize(()=>this._destroyLinesCache()))),window.clearTimeout(this._linesCacheTimeoutId),this._linesCacheTimeoutId=window.setTimeout(()=>this._destroyLinesCache(),Ze)}_destroyLinesCache(){this._linesCache=void 0,this._linesCacheDisposables.clear(),this._linesCacheTimeoutId&&(window.clearTimeout(this._linesCacheTimeoutId),this._linesCacheTimeoutId=0)}_isWholeWord(t,n,i){return(t===0||Fe.includes(n[t-1]))&&(t+i.length===n.length||Fe.includes(n[t+i.length]))}_findInLine(t,n,i={},s=!1){let l=this._terminal,u=n.startRow,a=n.startCol;if(l.buffer.active.getLine(u)?.isWrapped){if(s){n.startCol+=l.cols;return}return n.startRow--,n.startCol+=l.cols,this._findInLine(t,n,i)}let v=this._linesCache?.[u];v||(v=this._translateBufferLineToStringWithWrap(u,!0),this._linesCache&&(this._linesCache[u]=v));let[m,I]=v,E=this._bufferColsToStringOffset(u,a),k=t,R=m;i.regex||(k=i.caseSensitive?t:t.toLowerCase(),R=i.caseSensitive?m:m.toLowerCase());let D=-1;if(i.regex){let w=RegExp(k,i.caseSensitive?"g":"gi"),y;if(s)for(;y=w.exec(R.slice(0,E));)D=w.lastIndex-y[0].length,t=y[0],w.lastIndex-=t.length-1;else y=w.exec(R.slice(E)),y&&y[0].length>0&&(D=E+(w.lastIndex-y[0].length),t=y[0])}else s?E-k.length>=0&&(D=R.lastIndexOf(k,E-k.length)):D=R.indexOf(k,E);if(D>=0){if(i.wholeWord&&!this._isWholeWord(D,R,t))return;let w=0;for(;w<I.length-1&&D>=I[w+1];)w++;let y=w;for(;y<I.length-1&&D+t.length>=I[y+1];)y++;let j=D-I[w],T=D+t.length-I[y],g=this._stringLengthToBufferSize(u+w,j),C=this._stringLengthToBufferSize(u+y,T)-g+l.cols*(y-w);return{term:t,col:g,row:u+w,size:C}}}_stringLengthToBufferSize(t,n){let i=this._terminal.buffer.active.getLine(t);if(!i)return 0;for(let s=0;s<n;s++){let l=i.getCell(s);if(!l)break;let u=l.getChars();u.length>1&&(n-=u.length-1);let a=i.getCell(s+1);a&&a.getWidth()===0&&n++}return n}_bufferColsToStringOffset(t,n){let i=this._terminal,s=t,l=0,u=i.buffer.active.getLine(s);for(;n>0&&u;){for(let a=0;a<n&&a<i.cols;a++){let f=u.getCell(a);if(!f)break;f.getWidth()&&(l+=f.getCode()===0?1:f.getChars().length)}if(s++,u=i.buffer.active.getLine(s),u&&!u.isWrapped)break;n-=i.cols}return l}_translateBufferLineToStringWithWrap(t,n){let i=this._terminal,s=[],l=[0],u=i.buffer.active.getLine(t);for(;u;){let a=i.buffer.active.getLine(t+1),f=a?a.isWrapped:!1,v=u.translateToString(!f&&n);if(f&&a){let m=u.getCell(u.length-1);m&&m.getCode()===0&&m.getWidth()===1&&a.getCell(0)?.getWidth()===2&&(v=v.slice(0,-1))}if(s.push(v),f)l.push(l[l.length-1]+v.length);else break;t++,u=a}return[s.join(""),l]}_selectResult(t,n,i){let s=this._terminal;if(this._selectedDecoration.clear(),!t)return s.clearSelection(),!1;if(s.select(t.col,t.row,t.size),n){let l=this._createResultDecorations(t,n,!0);l&&(this._selectedDecoration.value={decorations:l,match:t,dispose(){U(l)}})}if(!i&&(t.row>=s.buffer.active.viewportY+s.rows||t.row<s.buffer.active.viewportY)){let l=t.row-s.buffer.active.viewportY;l-=Math.floor(s.rows/2),s.scrollLines(l)}return!0}_applyStyles(t,n,i){t.classList.contains("xterm-find-result-decoration")||(t.classList.add("xterm-find-result-decoration"),n&&(t.style.outline=`1px solid ${n}`)),i&&t.classList.add("xterm-find-active-result-decoration")}_createResultDecorations(t,n,i){let s=this._terminal,l=[],u=t.col,a=t.size,f=-s.buffer.active.baseY-s.buffer.active.cursorY+t.row;for(;a>0;){let m=Math.min(s.cols-u,a);l.push([f,u,m]),u=0,a-=m,f++}let v=[];for(let m of l){let I=s.registerMarker(m[0]),E=s.registerDecoration({marker:I,x:m[1],width:m[2],backgroundColor:i?n.activeMatchBackground:n.matchBackground,overviewRulerOptions:this._highlightedLines.has(I.line)?void 0:{color:i?n.activeMatchColorOverviewRuler:n.matchOverviewRuler,position:"center"}});if(E){let k=[];k.push(I),k.push(E.onRender(R=>this._applyStyles(R,i?n.activeMatchBorder:n.matchBorder,!1))),k.push(E.onDispose(()=>U(k))),v.push(E)}}return v.length===0?void 0:v}};export{Pe as SearchAddon};
//# sourceMappingURL=addon-search.mjs.map
