"use strict";
/**
 * Copyright (c) 2023 The xterm.js authors. All rights reserved.
 * @license MIT
 */
Object.defineProperty(exports, "__esModule", { value: true });
const chai_1 = require("chai");
const IIPMetrics_1 = require("./IIPMetrics");
const fs = require('fs');
const TEST_IMAGES = [
    ['w3c_home_256.gif', { mime: 'image/gif', width: 72, height: 48 }],
    ['w3c_home_256.jpg', { mime: 'image/jpeg', width: 72, height: 48 }],
    ['w3c_home_256.png', { mime: 'image/png', width: 72, height: 48 }],
    ['w3c_home_2.gif', { mime: 'image/gif', width: 72, height: 48 }],
    ['w3c_home_2.jpg', { mime: 'image/jpeg', width: 72, height: 48 }],
    ['w3c_home_2.png', { mime: 'image/png', width: 72, height: 48 }],
    ['w3c_home_animation.gif', { mime: 'image/gif', width: 72, height: 48 }],
    ['w3c_home.gif', { mime: 'image/gif', width: 72, height: 48 }],
    ['w3c_home_gray.gif', { mime: 'image/gif', width: 72, height: 48 }],
    ['w3c_home_gray.jpg', { mime: 'image/jpeg', width: 72, height: 48 }],
    ['w3c_home_gray.png', { mime: 'image/png', width: 72, height: 48 }],
    ['w3c_home.jpg', { mime: 'image/jpeg', width: 72, height: 48 }],
    ['w3c_home.png', { mime: 'image/png', width: 72, height: 48 }],
    ['w3c_home_noexif.jpg', { mime: 'image/jpeg', width: 72, height: 48 }],
    ['spinfox.png', { mime: 'image/png', width: 148, height: 148 }],
    ['iphone_hdr_YES.jpg', { mime: 'image/jpeg', width: 3264, height: 2448 }],
    ['nikon-e950.jpg', { mime: 'image/jpeg', width: 800, height: 600 }],
    ['agfa-makernotes.jpg', { mime: 'image/jpeg', width: 8, height: 8 }],
    ['sony-alpha-6000.jpg', { mime: 'image/jpeg', width: 6000, height: 4000 }]
];
describe('IIPMetrics', () => {
    it('bunch of testimages', () => {
        for (let i = 0; i < TEST_IMAGES.length; ++i) {
            const imageData = fs.readFileSync('./addons/addon-image/fixture/testimages/' + TEST_IMAGES[i][0]);
            chai_1.assert.deepStrictEqual((0, IIPMetrics_1.imageType)(imageData), TEST_IMAGES[i][1]);
        }
    });
});//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/18e3a1ec544e6907be1e944a94c496e302073435/node_modules/@xterm/addon-image/out/IIPMetrics.test.js.map