/*! For license information please see addon-image.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.ImageAddon=t():e.ImageAddon=t()}(globalThis,(()=>(()=>{"use strict";var e={342:(e,t)=>{function A(e){if("undefined"!=typeof Buffer)return Buffer.from(e,"base64");const t=atob(e),A=new Uint8Array(t.length);for(let e=0;e<A.length;++e)A[e]=t.charCodeAt(e);return A}Object.defineProperty(t,"__esModule",{value:!0}),t.InWasm=void 0,t.InWasm=function(e){if(e.d){const{t,s:i,d:s}=e;let r,n;const o=WebAssembly;return 2===t?i?()=>r||(r=A(s)):()=>Promise.resolve(r||(r=A(s))):1===t?i?()=>n||(n=new o.Module(r||(r=A(s)))):()=>n?Promise.resolve(n):o.compile(r||(r=A(s))).then((e=>n=e)):i?e=>new o.Instance(n||(n=new o.Module(r||(r=A(s)))),e):e=>n?o.instantiate(n,e):o.instantiate(r||(r=A(s)),e).then((e=>(n=e.module)&&e.instance))}if("undefined"==typeof _wasmCtx)throw new Error('must run "inwasm"');_wasmCtx.add(e)}},448:(e,t)=>{function A(e){return 255&e}function i(e){return e>>>8&255}function s(e){return e>>>16&255}function r(e,t,A,i=255){return((255&i)<<24|(255&A)<<16|(255&t)<<8|255&e)>>>0}function n(e,t,A){return Math.max(e,Math.min(A,t))}function o(e,t,A){return A<0&&(A+=1),A>1&&(A-=1),6*A<1?t+6*(e-t)*A:2*A<1?e:3*A<2?t+(e-t)*(4-6*A):t}function a(e,t,A){return(4278190080|Math.round(A/100*255)<<16|Math.round(t/100*255)<<8|Math.round(e/100*255))>>>0}Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_FOREGROUND=t.DEFAULT_BACKGROUND=t.PALETTE_ANSI_256=t.PALETTE_VT340_GREY=t.PALETTE_VT340_COLOR=t.normalizeHLS=t.normalizeRGB=t.nearestColorIndex=t.fromRGBA8888=t.toRGBA8888=t.alpha=t.blue=t.green=t.red=t.BIG_ENDIAN=void 0,t.BIG_ENDIAN=255===new Uint8Array(new Uint32Array([4278190080]).buffer)[0],t.BIG_ENDIAN&&console.warn("BE platform detected. This version of node-sixel works only on LE properly."),t.red=A,t.green=i,t.blue=s,t.alpha=function(e){return e>>>24&255},t.toRGBA8888=r,t.fromRGBA8888=function(e){return[255&e,e>>8&255,e>>16&255,e>>>24]},t.nearestColorIndex=function(e,t){const r=A(e),n=i(e),o=s(e);let a=Number.MAX_SAFE_INTEGER,h=-1;for(let e=0;e<t.length;++e){const A=r-t[e][0],i=n-t[e][1],s=o-t[e][2],g=A*A+i*i+s*s;if(!g)return e;g<a&&(a=g,h=e)}return h},t.normalizeRGB=a,t.normalizeHLS=function(e,t,A){return function(e,t,A){if(!A){const e=Math.round(255*t);return r(e,e,e)}const i=t<.5?t*(1+A):t+A-t*A,s=2*t-i;return r(n(0,255,Math.round(255*o(i,s,e+1/3))),n(0,255,Math.round(255*o(i,s,e))),n(0,255,Math.round(255*o(i,s,e-1/3))))}((e+240)/360,t/100,A/100)},t.PALETTE_VT340_COLOR=new Uint32Array([a(0,0,0),a(20,20,80),a(80,13,13),a(20,80,20),a(80,20,80),a(20,80,80),a(80,80,20),a(53,53,53),a(26,26,26),a(33,33,60),a(60,26,26),a(33,60,33),a(60,33,60),a(33,60,60),a(60,60,33),a(80,80,80)]),t.PALETTE_VT340_GREY=new Uint32Array([a(0,0,0),a(13,13,13),a(26,26,26),a(40,40,40),a(6,6,6),a(20,20,20),a(33,33,33),a(46,46,46),a(0,0,0),a(13,13,13),a(26,26,26),a(40,40,40),a(6,6,6),a(20,20,20),a(33,33,33),a(46,46,46)]),t.PALETTE_ANSI_256=(()=>{const e=[r(0,0,0),r(205,0,0),r(0,205,0),r(205,205,0),r(0,0,238),r(205,0,205),r(0,250,205),r(229,229,229),r(127,127,127),r(255,0,0),r(0,255,0),r(255,255,0),r(92,92,255),r(255,0,255),r(0,255,255),r(255,255,255)],t=[0,95,135,175,215,255];for(let A=0;A<6;++A)for(let i=0;i<6;++i)for(let s=0;s<6;++s)e.push(r(t[A],t[i],t[s]));for(let t=8;t<=238;t+=10)e.push(r(t,t,t));return new Uint32Array(e)})(),t.DEFAULT_BACKGROUND=r(0,0,0,255),t.DEFAULT_FOREGROUND=r(255,255,255,255)},838:(e,t,A)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.decodeAsync=t.decode=t.Decoder=t.DecoderAsync=void 0;const i=A(448),s=A(64),r=function(e){if("undefined"!=typeof Buffer)return Buffer.from(e,"base64");const t=atob(e),A=new Uint8Array(t.length);for(let e=0;e<A.length;++e)A[e]=t.charCodeAt(e);return A}(s.LIMITS.BYTES);let n;const o=new Uint32Array;class a{constructor(){this.bandHandler=e=>1,this.modeHandler=e=>1}handle_band(e){return this.bandHandler(e)}mode_parsed(e){return this.modeHandler(e)}}const h={memoryLimit:134217728,sixelColor:i.DEFAULT_FOREGROUND,fillColor:i.DEFAULT_BACKGROUND,palette:i.PALETTE_VT340_COLOR,paletteLimit:s.LIMITS.PALETTE_SIZE,truncate:!0};function g(e){const t=new a,A={env:{handle_band:t.handle_band.bind(t),mode_parsed:t.mode_parsed.bind(t)}};return WebAssembly.instantiate(n||r,A).then((A=>(n=n||A.module,new l(e,A.instance||A,t))))}t.DecoderAsync=g;class l{constructor(e,t,A){if(this._PIXEL_OFFSET=s.LIMITS.MAX_WIDTH+4,this._canvas=o,this._bandWidths=[],this._maxWidth=0,this._minWidth=s.LIMITS.MAX_WIDTH,this._lastOffset=0,this._currentHeight=0,this._opts=Object.assign({},h,e),this._opts.paletteLimit>s.LIMITS.PALETTE_SIZE)throw new Error(`DecoderOptions.paletteLimit must not exceed ${s.LIMITS.PALETTE_SIZE}`);if(t)A.bandHandler=this._handle_band.bind(this),A.modeHandler=this._initCanvas.bind(this);else{const e=n||(n=new WebAssembly.Module(r));t=new WebAssembly.Instance(e,{env:{handle_band:this._handle_band.bind(this),mode_parsed:this._initCanvas.bind(this)}})}this._instance=t,this._wasm=this._instance.exports,this._chunk=new Uint8Array(this._wasm.memory.buffer,this._wasm.get_chunk_address(),s.LIMITS.CHUNK_SIZE),this._states=new Uint32Array(this._wasm.memory.buffer,this._wasm.get_state_address(),12),this._palette=new Uint32Array(this._wasm.memory.buffer,this._wasm.get_palette_address(),s.LIMITS.PALETTE_SIZE),this._palette.set(this._opts.palette),this._pSrc=new Uint32Array(this._wasm.memory.buffer,this._wasm.get_p0_address()),this._wasm.init(i.DEFAULT_FOREGROUND,0,this._opts.paletteLimit,0)}get _fillColor(){return this._states[0]}get _truncate(){return this._states[8]}get _rasterWidth(){return this._states[6]}get _rasterHeight(){return this._states[7]}get _width(){return this._states[2]?this._states[2]-4:0}get _height(){return this._states[3]}get _level(){return this._states[9]}get _mode(){return this._states[10]}get _paletteLimit(){return this._states[11]}_initCanvas(e){if(2===e){const e=this.width*this.height;if(e>this._canvas.length){if(this._opts.memoryLimit&&4*e>this._opts.memoryLimit)throw this.release(),new Error("image exceeds memory limit");this._canvas=new Uint32Array(e)}this._maxWidth=this._width}else if(1===e)if(2===this._level){const e=Math.min(this._rasterWidth,s.LIMITS.MAX_WIDTH)*this._rasterHeight;if(e>this._canvas.length){if(this._opts.memoryLimit&&4*e>this._opts.memoryLimit)throw this.release(),new Error("image exceeds memory limit");this._canvas=new Uint32Array(e)}}else this._canvas.length<65536&&(this._canvas=new Uint32Array(65536));return 0}_realloc(e,t){const A=e+t;if(A>this._canvas.length){if(this._opts.memoryLimit&&4*A>this._opts.memoryLimit)throw this.release(),new Error("image exceeds memory limit");const e=new Uint32Array(65536*Math.ceil(A/65536));e.set(this._canvas),this._canvas=e}}_handle_band(e){const t=this._PIXEL_OFFSET;let A=this._lastOffset;if(2===this._mode){let i=this.height-this._currentHeight,s=0;for(;s<6&&i>0;)this._canvas.set(this._pSrc.subarray(t*s,t*s+e),A+e*s),s++,i--;this._lastOffset+=e*s,this._currentHeight+=s}else if(1===this._mode){this._realloc(A,6*e),this._maxWidth=Math.max(this._maxWidth,e),this._minWidth=Math.min(this._minWidth,e);for(let i=0;i<6;++i)this._canvas.set(this._pSrc.subarray(t*i,t*i+e),A+e*i);this._bandWidths.push(e),this._lastOffset+=6*e,this._currentHeight+=6}return 0}get width(){return 1!==this._mode?this._width:Math.max(this._maxWidth,this._wasm.current_width())}get height(){return 1!==this._mode?this._height:this._wasm.current_width()?6*this._bandWidths.length+this._wasm.current_height():6*this._bandWidths.length}get palette(){return this._palette.subarray(0,this._paletteLimit)}get memoryUsage(){return this._canvas.byteLength+this._wasm.memory.buffer.byteLength+8*this._bandWidths.length}get properties(){return{width:this.width,height:this.height,mode:this._mode,level:this._level,truncate:!!this._truncate,paletteLimit:this._paletteLimit,fillColor:this._fillColor,memUsage:this.memoryUsage,rasterAttributes:{numerator:this._states[4],denominator:this._states[5],width:this._rasterWidth,height:this._rasterHeight}}}init(e=this._opts.fillColor,t=this._opts.palette,A=this._opts.paletteLimit,i=this._opts.truncate){this._wasm.init(this._opts.sixelColor,e,A,i?1:0),t&&this._palette.set(t.subarray(0,s.LIMITS.PALETTE_SIZE)),this._bandWidths.length=0,this._maxWidth=0,this._minWidth=s.LIMITS.MAX_WIDTH,this._lastOffset=0,this._currentHeight=0}decode(e,t=0,A=e.length){let i=t;for(;i<A;){const t=Math.min(A-i,s.LIMITS.CHUNK_SIZE);this._chunk.set(e.subarray(i,i+=t)),this._wasm.decode(0,t)}}decodeString(e,t=0,A=e.length){let i=t;for(;i<A;){const t=Math.min(A-i,s.LIMITS.CHUNK_SIZE);for(let A=0,s=i;A<t;++A,++s)this._chunk[A]=e.charCodeAt(s);i+=t,this._wasm.decode(0,t)}}get data32(){if(0===this._mode||!this.width||!this.height)return o;const e=this._wasm.current_width();if(2===this._mode){let t=this.height-this._currentHeight;if(t>0){const A=this._PIXEL_OFFSET;let i=this._lastOffset,s=0;for(;s<6&&t>0;)this._canvas.set(this._pSrc.subarray(A*s,A*s+e),i+e*s),s++,t--;t&&this._canvas.fill(this._fillColor,i+e*s)}return this._canvas.subarray(0,this.width*this.height)}if(1===this._mode){if(this._minWidth===this._maxWidth){let t=!1;if(e)if(e!==this._minWidth)t=!0;else{const t=this._PIXEL_OFFSET;let A=this._lastOffset;this._realloc(A,6*e);for(let i=0;i<6;++i)this._canvas.set(this._pSrc.subarray(t*i,t*i+e),A+e*i)}if(!t)return this._canvas.subarray(0,this.width*this.height)}const t=new Uint32Array(this.width*this.height);t.fill(this._fillColor);let A=0,i=0;for(let e=0;e<this._bandWidths.length;++e){const s=this._bandWidths[e];for(let e=0;e<6;++e)t.set(this._canvas.subarray(i,i+=s),A),A+=this.width}if(e){const i=this._PIXEL_OFFSET,s=this._wasm.current_height();for(let r=0;r<s;++r)t.set(this._pSrc.subarray(i*r,i*r+e),A+this.width*r)}return t}return o}get data8(){return new Uint8ClampedArray(this.data32.buffer,0,this.width*this.height*4)}release(){this._canvas=o,this._bandWidths.length=0,this._maxWidth=0,this._minWidth=s.LIMITS.MAX_WIDTH,this._wasm.init(i.DEFAULT_FOREGROUND,0,this._opts.paletteLimit,0)}}t.Decoder=l,t.decode=function(e,t){const A=new l(t);return A.init(),"string"==typeof e?A.decodeString(e):A.decode(e),{width:A.width,height:A.height,data32:A.data32,data8:A.data8}},t.decodeAsync=async function(e,t){const A=await g(t);return A.init(),"string"==typeof e?A.decodeString(e):A.decode(e),{width:A.width,height:A.height,data32:A.data32,data8:A.data8}}},64:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.LIMITS=void 0,t.LIMITS={CHUNK_SIZE:16384,PALETTE_SIZE:4096,MAX_WIDTH:16384,BYTES:"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"}},466:(e,t,A)=>{Object.defineProperty(t,"__esModule",{value:!0});const i=(0,A(342).InWasm)({s:1,t:0,d:"AGFzbQEAAAABBQFgAAF/Ag8BA2VudgZtZW1vcnkCAAEDAwIAAAcNAgNkZWMAAANlbmQAAQqxAwKuAQEFf0GIKCgCAEGgKGohAUGEKCgCACIAQYAoKAIAQQFrQXxxIgJIBEAgAkGgKGohAyAAQaAoaiEAA0AgAC0AA0ECdCgCgCAgAC0AAkECdCgCgBggAC0AAUECdCgCgBAgAC0AAEECdCgCgAhycnIiBEH///8HSwRAQQEPCyABIAQ2AgAgAUEDaiEBIABBBGoiACADSQ0ACwtBhCggAjYCAEGIKCABQaAoazYCAEEAC/4BAQZ/AkBBgCgoAgAiAUGEKCgCACIAa0EFTgRAQQEhAxAADQFBgCgoAgAhAUGEKCgCACEAC0EBIQMgASAAayIEQQJIDQAgAEGhKGotAABBAnQoAoAQIABBoChqLQAAQQJ0KAKACHIhAQJAIARBAkYEQEEBIQIMAQtBASECIAAtAKIoIgVBPUcEQEECIQIgBUECdCgCgBggAXIhAQsgBEEERw0AIAAtAKMoIgBBPUYNACACQQFqIQIgAEECdCgCgCAgAXIhAQsgAUH///8HSw0AQYgoKAIAQaAoaiABNgIAQYgoQYgoKAIAIAJqIgA2AgAgAEGQKCgCAEchAwsgAwsAdglwcm9kdWNlcnMBDHByb2Nlc3NlZC1ieQEFY2xhbmdWMTguMC4wIChodHRwczovL2dpdGh1Yi5jb20vbGx2bS9sbHZtLXByb2plY3QgZDFlNjg1ZGY0NWRjNTk0NGI0M2QyNTQ3ZDAxMzhjZDRhM2VlNGVmZSkALA90YXJnZXRfZmVhdHVyZXMCKw9tdXRhYmxlLWdsb2JhbHMrCHNpZ24tZXh0"}),s=new Uint8Array("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("").map((e=>e.charCodeAt(0)))),r=new Uint32Array(1024);r.fill(4278190080);for(let e=0;e<s.length;++e)r[s[e]]=e<<2;for(let e=0;e<s.length;++e)r[256+s[e]]=e>>4|(e<<4&255)<<8;for(let e=0;e<s.length;++e)r[512+s[e]]=e>>2<<8|(e<<6&255)<<16;for(let e=0;e<s.length;++e)r[768+s[e]]=e<<16;const n=new Uint8Array(0);t.default=class{constructor(e){this.keepSize=e}get data8(){return this._inst?this._d.subarray(0,this._m32[1282]):n}release(){this._inst&&(this._mem.buffer.byteLength>this.keepSize?this._inst=this._m32=this._d=this._mem=null:(this._m32[1280]=0,this._m32[1281]=0,this._m32[1282]=0))}init(e){let t=this._m32;const A=4*(Math.ceil(e/3)+1288);this._inst?this._mem.buffer.byteLength<A&&(this._mem.grow(Math.ceil((A-this._mem.buffer.byteLength)/65536)),t=new Uint32Array(this._mem.buffer,0),this._d=new Uint8Array(this._mem.buffer,5152)):(this._mem=new WebAssembly.Memory({initial:Math.ceil(A/65536)}),this._inst=i({env:{memory:this._mem}}),t=new Uint32Array(this._mem.buffer,0),t.set(r,256),this._d=new Uint8Array(this._mem.buffer,5152)),t[1284]=e,t[1283]=4*Math.ceil(e/3),t[1280]=0,t[1281]=0,t[1282]=0,this._m32=t}put(e,t,A){if(!this._inst)return 1;const i=this._m32;return A-t+i[1280]>i[1283]?1:(this._d.set(e.subarray(t,A),i[1280]),i[1280]+=A-t,i[1280]-i[1281]>=131072?this._inst.exports.dec():0)}end(){return this._inst?this._inst.exports.end():1}}},795:(e,t,A)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.IIPHandler=void 0;const i=A(463),s=A(699),r=A(466),n=A(389),o=A(462),a={name:"Unnamed file",size:0,width:"auto",height:"auto",preserveAspectRatio:1,inline:0};t.IIPHandler=class{constructor(e,t,A,i){this._opts=e,this._renderer=t,this._storage=A,this._coreTerminal=i,this._aborted=!1,this._hp=new n.HeaderParser,this._header=a,this._dec=new r.default(4194304),this._metrics=o.UNSUPPORTED_TYPE}reset(){}start(){this._aborted=!1,this._header=a,this._metrics=o.UNSUPPORTED_TYPE,this._hp.reset()}put(e,t,A){if(!this._aborted)if(4===this._hp.state)this._dec.put(e,t,A)&&(this._dec.release(),this._aborted=!0);else{const i=this._hp.parse(e,t,A);if(-1===i)return void(this._aborted=!0);if(i>0){if(this._header=Object.assign({},a,this._hp.fields),!this._header.inline||!this._header.size||this._header.size>this._opts.iipSizeLimit)return void(this._aborted=!0);this._dec.init(this._header.size),this._dec.put(e,i,A)&&(this._dec.release(),this._aborted=!0)}}}end(e){if(this._aborted)return!0;let t=0,A=0,s=!0;if((s=e)&&(s=!this._dec.end())&&(this._metrics=(0,o.imageType)(this._dec.data8),(s="unsupported"!==this._metrics.mime)&&(t=this._metrics.width,A=this._metrics.height,(s=t&&A&&t*A<this._opts.pixelLimit)&&([t,A]=this._resize(t,A).map(Math.floor),s=t&&A&&t*A<this._opts.pixelLimit))),!s)return this._dec.release(),!0;const r=new Blob([this._dec.data8],{type:this._metrics.mime});if(this._dec.release(),!window.createImageBitmap){const e=URL.createObjectURL(r),s=new Image;return new Promise((r=>{s.addEventListener("load",(()=>{var n;URL.revokeObjectURL(e);const o=i.ImageRenderer.createCanvas(window.document,t,A);null===(n=o.getContext("2d"))||void 0===n||n.drawImage(s,0,0,t,A),this._storage.addImage(o),r(!0)})),s.src=e,setTimeout((()=>r(!0)),1e3)}))}return createImageBitmap(r,{resizeWidth:t,resizeHeight:A}).then((e=>(this._storage.addImage(e),!0)))}_resize(e,t){var A,i,r,n;const o=(null===(A=this._renderer.dimensions)||void 0===A?void 0:A.css.cell.width)||s.CELL_SIZE_DEFAULT.width,a=(null===(i=this._renderer.dimensions)||void 0===i?void 0:i.css.cell.height)||s.CELL_SIZE_DEFAULT.height,h=(null===(r=this._renderer.dimensions)||void 0===r?void 0:r.css.canvas.width)||o*this._coreTerminal.cols,g=(null===(n=this._renderer.dimensions)||void 0===n?void 0:n.css.canvas.height)||a*this._coreTerminal.rows,l=this._dim(this._header.width,h,o),c=this._dim(this._header.height,g,a);if(!l&&!c){const A=h/e,i=(g-a)/t,s=Math.min(A,i);return s<1?[e*s,t*s]:[e,t]}return l?!this._header.preserveAspectRatio&&l&&c?[l,c]:[l,t*l/e]:[e*c/t,c]}_dim(e,t,A){return"auto"===e?0:e.endsWith("%")?parseInt(e.slice(0,-1))*t/100:e.endsWith("px")?parseInt(e.slice(0,-2)):parseInt(e)*A}}},389:(e,t)=>{function A(e){let t="";for(let A=0;A<e.length;++A)t+=String.fromCharCode(e[A]);return t}function i(e){let t=0;for(let A=0;A<e.length;++A){if(e[A]<48||e[A]>57)throw new Error("illegal char");t=10*t+e[A]-48}return t}function s(e){const t=A(e);if(!t.match(/^((auto)|(\d+?((px)|(%)){0,1}))$/))throw new Error("illegal size");return t}Object.defineProperty(t,"__esModule",{value:!0}),t.HeaderParser=void 0;const r={inline:i,size:i,name:function(e){if("undefined"!=typeof Buffer)return Buffer.from(A(e),"base64").toString();const t=atob(A(e)),i=new Uint8Array(t.length);for(let e=0;e<i.length;++e)i[e]=t.charCodeAt(e);return(new TextDecoder).decode(i)},width:s,height:s,preserveAspectRatio:i},n=[70,105,108,101],o=1024;t.HeaderParser=class{constructor(){this.state=0,this._buffer=new Uint32Array(o),this._position=0,this._key="",this.fields={}}reset(){this._buffer.fill(0),this.state=0,this._position=0,this.fields={},this._key=""}parse(e,t,A){let i=this.state,s=this._position;const r=this._buffer;if(1===i||4===i)return-1;if(0===i&&s>6)return-1;for(let a=t;a<A;++a){const t=e[a];switch(t){case 59:if(!this._storeValue(s))return this._a();i=2,s=0;break;case 61:if(0===i){for(let e=0;e<n.length;++e)if(r[e]!==n[e])return this._a();i=2,s=0}else if(2===i){if(!this._storeKey(s))return this._a();i=3,s=0}else if(3===i){if(s>=o)return this._a();r[s++]=t}break;case 58:return 3!==i||this._storeValue(s)?(this.state=4,a+1):this._a();default:if(s>=o)return this._a();r[s++]=t}}return this.state=i,this._position=s,-2}_a(){return this.state=1,-1}_storeKey(e){const t=A(this._buffer.subarray(0,e));return!!t&&(this._key=t,this.fields[t]=null,!0)}_storeValue(e){if(this._key){try{const t=this._buffer.slice(0,e);this.fields[this._key]=r[this._key]?r[this._key](t):t}catch(e){return!1}return!0}return!1}}},462:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UNSUPPORTED_TYPE=void 0,t.imageType=function(e){if(e.length<24)return t.UNSUPPORTED_TYPE;const A=new Uint32Array(e.buffer,e.byteOffset,6);if(1196314761===A[0]&&169478669===A[1]&&1380206665===A[3])return{mime:"image/png",width:e[16]<<24|e[17]<<16|e[18]<<8|e[19],height:e[20]<<24|e[21]<<16|e[22]<<8|e[23]};if(255===e[0]&&216===e[1]&&255===e[2]){const[t,A]=function(e){const t=e.length;let A=4,i=e[A]<<8|e[A+1];for(;;){if(A+=i,A>=t)return[0,0];if(255!==e[A])return[0,0];if(192===e[A+1]||194===e[A+1])return A+8<t?[e[A+7]<<8|e[A+8],e[A+5]<<8|e[A+6]]:[0,0];A+=2,i=e[A]<<8|e[A+1]}}(e);return{mime:"image/jpeg",width:t,height:A}}return 944130375!==A[0]||55!==e[4]&&57!==e[4]||97!==e[5]?t.UNSUPPORTED_TYPE:{mime:"image/gif",width:e[7]<<8|e[6],height:e[9]<<8|e[8]}},t.UNSUPPORTED_TYPE={mime:"unsupported",width:0,height:0}},463:(e,t,A)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ImageRenderer=void 0;const i=A(448),s=A(540);class r extends s.Disposable{static createCanvas(e,t,A){const i=(e||document).createElement("canvas");return i.width=0|t,i.height=0|A,i}static createImageData(e,t,A,i){if("function"!=typeof ImageData){const s=e.createImageData(t,A);return i&&s.data.set(new Uint8ClampedArray(i,0,t*A*4)),s}return i?new ImageData(new Uint8ClampedArray(i,0,t*A*4),t,A):new ImageData(t,A)}static createImageBitmap(e){return"function"!=typeof createImageBitmap?Promise.resolve(void 0):createImageBitmap(e)}constructor(e){super(),this._terminal=e,this._optionsRefresh=this._register(new s.MutableDisposable),this._oldOpen=this._terminal._core.open,this._terminal._core.open=e=>{var t;null===(t=this._oldOpen)||void 0===t||t.call(this._terminal._core,e),this._open()},this._terminal._core.screenElement&&this._open(),this._optionsRefresh.value=this._terminal._core.optionsService.onOptionChange((e=>{var t;"fontSize"===e&&(this.rescaleCanvas(),null===(t=this._renderService)||void 0===t||t.refreshRows(0,this._terminal.rows))})),this._register((0,s.toDisposable)((()=>{var e;this.removeLayerFromDom(),this._terminal._core&&this._oldOpen&&(this._terminal._core.open=this._oldOpen,this._oldOpen=void 0),this._renderService&&this._oldSetRenderer&&(this._renderService.setRenderer=this._oldSetRenderer,this._oldSetRenderer=void 0),this._renderService=void 0,this.canvas=void 0,this._ctx=void 0,null===(e=this._placeholderBitmap)||void 0===e||e.close(),this._placeholderBitmap=void 0,this._placeholder=void 0})))}showPlaceholder(e){var t,A;e?this._placeholder||-1===this.cellSize.height||this._createPlaceHolder(Math.max(this.cellSize.height+1,24)):(null===(t=this._placeholderBitmap)||void 0===t||t.close(),this._placeholderBitmap=void 0,this._placeholder=void 0),null===(A=this._renderService)||void 0===A||A.refreshRows(0,this._terminal.rows)}get dimensions(){var e;return null===(e=this._renderService)||void 0===e?void 0:e.dimensions}get cellSize(){var e,t;return{width:(null===(e=this.dimensions)||void 0===e?void 0:e.css.cell.width)||-1,height:(null===(t=this.dimensions)||void 0===t?void 0:t.css.cell.height)||-1}}clearLines(e,t){var A,i,s,r;null===(A=this._ctx)||void 0===A||A.clearRect(0,e*((null===(i=this.dimensions)||void 0===i?void 0:i.css.cell.height)||0),(null===(s=this.dimensions)||void 0===s?void 0:s.css.canvas.width)||0,(++t-e)*((null===(r=this.dimensions)||void 0===r?void 0:r.css.cell.height)||0))}clearAll(){var e,t,A;null===(e=this._ctx)||void 0===e||e.clearRect(0,0,(null===(t=this.canvas)||void 0===t?void 0:t.width)||0,(null===(A=this.canvas)||void 0===A?void 0:A.height)||0)}draw(e,t,A,i,s=1){if(!this._ctx)return;const{width:r,height:n}=this.cellSize;if(-1===r||-1===n)return;this._rescaleImage(e,r,n);const o=e.actual,a=Math.ceil(o.width/r),h=t%a*r,g=Math.floor(t/a)*n,l=A*r,c=i*n,d=s*r+h>o.width?o.width-h:s*r,I=g+n>o.height?o.height-g:n;this._ctx.drawImage(o,Math.floor(h),Math.floor(g),Math.ceil(d),Math.ceil(I),Math.floor(l),Math.floor(c),Math.ceil(d),Math.ceil(I))}extractTile(e,t){const{width:A,height:i}=this.cellSize;if(-1===A||-1===i)return;this._rescaleImage(e,A,i);const s=e.actual,n=Math.ceil(s.width/A),o=t%n*A,a=Math.floor(t/n)*i,h=A+o>s.width?s.width-o:A,g=a+i>s.height?s.height-a:i,l=r.createCanvas(this.document,h,g),c=l.getContext("2d");return c?(c.drawImage(s,Math.floor(o),Math.floor(a),Math.floor(h),Math.floor(g),0,0,Math.floor(h),Math.floor(g)),l):void 0}drawPlaceholder(e,t,A=1){if(this._ctx){const{width:i,height:s}=this.cellSize;if(-1===i||-1===s)return;if(this._placeholder?s>=this._placeholder.height&&this._createPlaceHolder(s+1):this._createPlaceHolder(Math.max(s+1,24)),!this._placeholder)return;this._ctx.drawImage(this._placeholderBitmap||this._placeholder,e*i,t*s%2?0:1,i*A,s,e*i,t*s,i*A,s)}}rescaleCanvas(){this.canvas&&(this.canvas.width===this.dimensions.css.canvas.width&&this.canvas.height===this.dimensions.css.canvas.height||(this.canvas.width=this.dimensions.css.canvas.width||0,this.canvas.height=this.dimensions.css.canvas.height||0))}_rescaleImage(e,t,A){if(t===e.actualCellSize.width&&A===e.actualCellSize.height)return;const{width:i,height:s}=e.origCellSize;if(t===i&&A===s)return e.actual=e.orig,e.actualCellSize.width=i,void(e.actualCellSize.height=s);const n=r.createCanvas(this.document,Math.ceil(e.orig.width*t/i),Math.ceil(e.orig.height*A/s)),o=n.getContext("2d");o&&(o.drawImage(e.orig,0,0,n.width,n.height),e.actual=n,e.actualCellSize.width=t,e.actualCellSize.height=A)}_open(){this._renderService=this._terminal._core._renderService,this._oldSetRenderer=this._renderService.setRenderer.bind(this._renderService),this._renderService.setRenderer=e=>{var t;this.removeLayerFromDom(),null===(t=this._oldSetRenderer)||void 0===t||t.call(this._renderService,e)}}insertLayerToDom(){var e,t;this.document&&this._terminal._core.screenElement?this.canvas||(this.canvas=r.createCanvas(this.document,(null===(e=this.dimensions)||void 0===e?void 0:e.css.canvas.width)||0,(null===(t=this.dimensions)||void 0===t?void 0:t.css.canvas.height)||0),this.canvas.classList.add("xterm-image-layer"),this._terminal._core.screenElement.appendChild(this.canvas),this._ctx=this.canvas.getContext("2d",{alpha:!0,desynchronized:!0}),this.clearAll()):console.warn("image addon: cannot insert output canvas to DOM, missing document or screenElement")}removeLayerFromDom(){this.canvas&&(this._ctx=void 0,this.canvas.remove(),this.canvas=void 0)}_createPlaceHolder(e=24){var t;null===(t=this._placeholderBitmap)||void 0===t||t.close(),this._placeholderBitmap=void 0;const A=32,s=r.createCanvas(this.document,A,e),n=s.getContext("2d",{alpha:!1});if(!n)return;const o=r.createImageData(n,A,e),a=new Uint32Array(o.data.buffer),h=(0,i.toRGBA8888)(0,0,0),g=(0,i.toRGBA8888)(255,255,255);a.fill(h);for(let t=0;t<e;++t){const e=t%2,i=t*A;for(let t=0;t<A;t+=2)a[i+t+e]=g}n.putImageData(o,0,0);const l=screen.width+A-1&-32||4096;this._placeholder=r.createCanvas(this.document,l,e);const c=this._placeholder.getContext("2d",{alpha:!1});if(c){for(let e=0;e<l;e+=A)c.drawImage(s,e,0);r.createImageBitmap(this._placeholder).then((e=>this._placeholderBitmap=e))}else this._placeholder=void 0}get document(){var e;return null===(e=this._terminal._core._coreBrowserService)||void 0===e?void 0:e.window.document}}t.ImageRenderer=r},699:(e,t,A)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ImageStorage=t.CELL_SIZE_DEFAULT=void 0;const i=A(463);t.CELL_SIZE_DEFAULT={width:7,height:14};class s{get ext(){return this._urlId?-469762049&this._ext|this.underlineStyle<<26:this._ext}set ext(e){this._ext=e}get underlineStyle(){return this._urlId?5:(469762048&this._ext)>>26}set underlineStyle(e){this._ext&=-469762049,this._ext|=e<<26&469762048}get underlineColor(){return 67108863&this._ext}set underlineColor(e){this._ext&=-67108864,this._ext|=67108863&e}get underlineVariantOffset(){const e=(3758096384&this._ext)>>29;return e<0?4294967288^e:e}set underlineVariantOffset(e){this._ext&=536870911,this._ext|=e<<29&3758096384}get urlId(){return this._urlId}set urlId(e){this._urlId=e}constructor(e=0,t=0,A=-1,i=-1){this.imageId=A,this.tileId=i,this._ext=0,this._urlId=0,this._ext=e,this._urlId=t}clone(){return new s(this._ext,this._urlId,this.imageId,this.tileId)}isEmpty(){return 0===this.underlineStyle&&0===this._urlId&&-1===this.imageId}}const r=new s;t.ImageStorage=class{constructor(e,t,A){this._terminal=e,this._renderer=t,this._opts=A,this._images=new Map,this._lastId=0,this._lowestId=0,this._fullyCleared=!1,this._needsFullClear=!1,this._pixelLimit=25e5;try{this.setLimit(this._opts.storageLimit)}catch(e){console.error(e.message),console.warn(`storageLimit is set to ${this.getLimit()} MB`)}this._viewportMetrics={cols:this._terminal.cols,rows:this._terminal.rows}}dispose(){this.reset()}reset(){var e;for(const t of this._images.values())null===(e=t.marker)||void 0===e||e.dispose();this._images.clear(),this._renderer.clearAll()}getLimit(){return 4*this._pixelLimit/1e6}setLimit(e){if(e<.5||e>1e3)throw RangeError("invalid storageLimit, should be at least 0.5 MB and not exceed 1G");this._pixelLimit=e/4*1e6>>>0,this._evictOldest(0)}getUsage(){return 4*this._getStoredPixels()/1e6}_getStoredPixels(){let e=0;for(const t of this._images.values())t.orig&&(e+=t.orig.width*t.orig.height,t.actual&&t.actual!==t.orig&&(e+=t.actual.width*t.actual.height));return e}_delImg(e){const t=this._images.get(e);this._images.delete(e),t&&window.ImageBitmap&&t.orig instanceof ImageBitmap&&t.orig.close()}wipeAlternate(){var e;const t=[];for(const[A,i]of this._images.entries())"alternate"===i.bufferType&&(null===(e=i.marker)||void 0===e||e.dispose(),t.push(A));for(const e of t)this._delImg(e);this._needsFullClear=!0,this._fullyCleared=!1}advanceCursor(e){if(this._opts.sixelScrolling){let A=this._renderer.cellSize;-1!==A.width&&-1!==A.height||(A=t.CELL_SIZE_DEFAULT);const i=Math.ceil(e/A.height);for(let e=1;e<i;++e)this._terminal._core._inputHandler.lineFeed()}}addImage(e){var A;this._evictOldest(e.width*e.height);let i=this._renderer.cellSize;-1!==i.width&&-1!==i.height||(i=t.CELL_SIZE_DEFAULT);const s=Math.ceil(e.width/i.width),r=Math.ceil(e.height/i.height),n=++this._lastId,o=this._terminal._core.buffer,a=this._terminal.cols,h=this._terminal.rows,g=o.x,l=o.y;let c=g,d=0;this._opts.sixelScrolling||(o.x=0,o.y=0,c=0),this._terminal._core._inputHandler._dirtyRowTracker.markDirty(o.y);for(let e=0;e<r;++e){const t=o.lines.get(o.y+o.ybase);for(let A=0;A<s&&!(c+A>=a);++A)this._writeToCell(t,c+A,n,e*s+A),d++;if(this._opts.sixelScrolling)e<r-1&&this._terminal._core._inputHandler.lineFeed();else if(++o.y>=h)break;o.x=c}this._terminal._core._inputHandler._dirtyRowTracker.markDirty(o.y),this._opts.sixelScrolling?o.x=c:(o.x=g,o.y=l);const I=[];for(const[e,t]of this._images.entries())t.tileCount<1&&(null===(A=t.marker)||void 0===A||A.dispose(),I.push(e));for(const e of I)this._delImg(e);const C=this._terminal.registerMarker(0);null==C||C.onDispose((()=>{this._images.get(n)&&this._delImg(n)})),"alternate"===this._terminal.buffer.active.type&&this._evictOnAlternate();const u={orig:e,origCellSize:i,actual:e,actualCellSize:Object.assign({},i),marker:C||void 0,tileCount:d,bufferType:this._terminal.buffer.active.type};this._images.set(n,u)}render(e){if(!this._renderer.canvas&&this._images.size&&(this._renderer.insertLayerToDom(),!this._renderer.canvas))return;if(this._renderer.rescaleCanvas(),!this._images.size)return this._fullyCleared||(this._renderer.clearAll(),this._fullyCleared=!0,this._needsFullClear=!1),void(this._renderer.canvas&&this._renderer.removeLayerFromDom());this._needsFullClear&&(this._renderer.clearAll(),this._fullyCleared=!0,this._needsFullClear=!1);const{start:t,end:A}=e,i=this._terminal._core.buffer,s=this._terminal._core.cols;this._renderer.clearLines(t,A);for(let e=t;e<=A;++e){const t=i.lines.get(e+i.ydisp);if(!t)return;for(let A=0;A<s;++A)if(268435456&t.getBg(A)){let i=t._extendedAttrs[A]||r;const n=i.imageId;if(void 0===n||-1===n)continue;const o=this._images.get(n);if(-1!==i.tileId){const a=i.tileId,h=A;let g=1;for(;++A<s&&268435456&t.getBg(A)&&(i=t._extendedAttrs[A]||r)&&i.imageId===n&&i.tileId===a+g;)g++;A--,o?o.actual&&this._renderer.draw(o,a,h,e,g):this._opts.showPlaceholder&&this._renderer.drawPlaceholder(h,e,g),this._fullyCleared=!1}}}}viewportResize(e){var t;if(!this._images.size)return void(this._viewportMetrics=e);if(this._viewportMetrics.cols>=e.cols)return void(this._viewportMetrics=e);const A=this._terminal._core.buffer,i=A.lines.length,s=this._viewportMetrics.cols-1;for(let n=0;n<i;++n){const i=A.lines.get(n);if(268435456&i.getBg(s)){const A=i._extendedAttrs[s]||r,n=A.imageId;if(void 0===n||-1===n)continue;const o=this._images.get(n);if(!o)continue;const a=Math.ceil(((null===(t=o.actual)||void 0===t?void 0:t.width)||0)/o.actualCellSize.width);if(A.tileId%a+1>=a)continue;let h=!1;for(let t=s+1;t>e.cols;++t)if(4194303&i._data[3*t+0]){h=!0;break}if(h)continue;const g=Math.min(e.cols,a-A.tileId%a+s);let l=A.tileId;for(let e=s+1;e<g;++e)this._writeToCell(i,e,n,++l),o.tileCount++}}this._viewportMetrics=e}getImageAtBufferCell(e,t){var A,s;const n=this._terminal._core.buffer.lines.get(t);if(n&&268435456&n.getBg(e)){const t=n._extendedAttrs[e]||r;if(t.imageId&&-1!==t.imageId){const e=null===(A=this._images.get(t.imageId))||void 0===A?void 0:A.orig;if(window.ImageBitmap&&e instanceof ImageBitmap){const t=i.ImageRenderer.createCanvas(window.document,e.width,e.height);return null===(s=t.getContext("2d"))||void 0===s||s.drawImage(e,0,0,e.width,e.height),t}return e}}}extractTileAtBufferCell(e,t){const A=this._terminal._core.buffer.lines.get(t);if(A&&268435456&A.getBg(e)){const t=A._extendedAttrs[e]||r;if(t.imageId&&-1!==t.imageId&&-1!==t.tileId){const e=this._images.get(t.imageId);if(e)return this._renderer.extractTile(e,t.tileId)}}}_evictOldest(e){var t;const A=this._getStoredPixels();let i=A;for(;this._pixelLimit<i+e&&this._images.size;){const e=this._images.get(++this._lowestId);e&&e.orig&&(i-=e.orig.width*e.orig.height,e.actual&&e.orig!==e.actual&&(i-=e.actual.width*e.actual.height),null===(t=e.marker)||void 0===t||t.dispose(),this._delImg(this._lowestId))}return A-i}_writeToCell(e,t,A,i){if(268435456&e._data[3*t+2]){const r=e._extendedAttrs[t];if(r){if(void 0!==r.imageId){const e=this._images.get(r.imageId);return e&&e.tileCount--,r.imageId=A,void(r.tileId=i)}return void(e._extendedAttrs[t]=new s(r.ext,r.urlId,A,i))}}e._data[3*t+2]|=268435456,e._extendedAttrs[t]=new s(0,0,A,i)}_evictOnAlternate(){var e,t;for(const e of this._images.values())"alternate"===e.bufferType&&(e.tileCount=0);const A=this._terminal._core.buffer;for(let t=0;t<this._terminal.rows;++t){const i=A.lines.get(t);if(i)for(let t=0;t<this._terminal.cols;++t)if(268435456&i._data[3*t+2]){const A=null===(e=i._extendedAttrs[t])||void 0===e?void 0:e.imageId;if(A){const e=this._images.get(A);e&&e.tileCount++}}}const i=[];for(const[e,A]of this._images.entries())"alternate"!==A.bufferType||A.tileCount||(null===(t=A.marker)||void 0===t||t.dispose(),i.push(e));for(const e of i)this._delImg(e)}}},566:(e,t,A)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SixelHandler=void 0;const i=A(448),s=A(463),r=A(838),n=i.PALETTE_ANSI_256;function o(e){return i.BIG_ENDIAN?e:(255&e)<<24|(e>>>8&255)<<16|(e>>>16&255)<<8|e>>>24&255}n.set(i.PALETTE_VT340_COLOR),t.SixelHandler=class{constructor(e,t,A){this._opts=e,this._storage=t,this._coreTerminal=A,this._size=0,this._aborted=!1,(0,r.DecoderAsync)({memoryLimit:4*this._opts.pixelLimit,palette:n,paletteLimit:this._opts.sixelPaletteLimit}).then((e=>this._dec=e))}reset(){this._dec&&(this._dec.release(),this._dec._palette.fill(0),this._dec.init(0,n,this._opts.sixelPaletteLimit))}hook(e){var t;if(this._size=0,this._aborted=!1,this._dec){const A=1===e.params[1]?0:function(e,t){let A=0;if(!t)return A;if(e.isInverse())if(e.isFgDefault())A=o(t.foreground.rgba);else if(e.isFgRGB()){const t=e.constructor.toColorRGB(e.getFgColor());A=(0,i.toRGBA8888)(...t)}else A=o(t.ansi[e.getFgColor()].rgba);else if(e.isBgDefault())A=o(t.background.rgba);else if(e.isBgRGB()){const t=e.constructor.toColorRGB(e.getBgColor());A=(0,i.toRGBA8888)(...t)}else A=o(t.ansi[e.getBgColor()].rgba);return A}(this._coreTerminal._core._inputHandler._curAttrData,null===(t=this._coreTerminal._core._themeService)||void 0===t?void 0:t.colors);this._dec.init(A,null,this._opts.sixelPaletteLimit)}}put(e,t,A){if(!this._aborted&&this._dec){if(this._size+=A-t,this._size>this._opts.sixelSizeLimit)return console.warn("SIXEL: too much data, aborting"),this._aborted=!0,void this._dec.release();try{this._dec.decode(e,t,A)}catch(e){console.warn(`SIXEL: error while decoding image - ${e}`),this._aborted=!0,this._dec.release()}}}unhook(e){var t;if(this._aborted||!e||!this._dec)return!0;const A=this._dec.width,i=this._dec.height;if(!A||!i)return i&&this._storage.advanceCursor(i),!0;const r=s.ImageRenderer.createCanvas(void 0,A,i);return null===(t=r.getContext("2d"))||void 0===t||t.putImageData(new ImageData(this._dec.data8,A,i),0,0),this._dec.memoryUsage>4194304&&this._dec.release(),this._storage.addImage(r),!0}}},732:(e,t,A)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Permutation=t.CallbackIterable=t.ArrayQueue=t.booleanComparator=t.numberComparator=t.CompareResult=void 0,t.tail=function(e,t=0){return e[e.length-(1+t)]},t.tail2=function(e){if(0===e.length)throw new Error("Invalid tail call");return[e.slice(0,e.length-1),e[e.length-1]]},t.equals=function(e,t,A=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;if(e.length!==t.length)return!1;for(let i=0,s=e.length;i<s;i++)if(!A(e[i],t[i]))return!1;return!0},t.removeFastWithoutKeepingOrder=function(e,t){const A=e.length-1;t<A&&(e[t]=e[A]),e.pop()},t.binarySearch=function(e,t,A){return r(e.length,(i=>A(e[i],t)))},t.binarySearch2=r,t.quickSelect=function e(t,A,i){if((t|=0)>=A.length)throw new TypeError("invalid index");const s=A[Math.floor(A.length*Math.random())],r=[],n=[],o=[];for(const e of A){const t=i(e,s);t<0?r.push(e):t>0?n.push(e):o.push(e)}return t<r.length?e(t,r,i):t<r.length+o.length?o[0]:e(t-(r.length+o.length),n,i)},t.groupBy=function(e,t){const A=[];let i;for(const s of e.slice(0).sort(t))i&&0===t(i[0],s)?i.push(s):(i=[s],A.push(i));return A},t.groupAdjacentBy=function*(e,t){let A,i;for(const s of e)void 0!==i&&t(i,s)?A.push(s):(A&&(yield A),A=[s]),i=s;A&&(yield A)},t.forEachAdjacent=function(e,t){for(let A=0;A<=e.length;A++)t(0===A?void 0:e[A-1],A===e.length?void 0:e[A])},t.forEachWithNeighbors=function(e,t){for(let A=0;A<e.length;A++)t(0===A?void 0:e[A-1],e[A],A+1===e.length?void 0:e[A+1])},t.sortedDiff=n,t.delta=function(e,t,A){const i=n(e,t,A),s=[],r=[];for(const t of i)s.push(...e.slice(t.start,t.start+t.deleteCount)),r.push(...t.toInsert);return{removed:s,added:r}},t.top=function(e,t,A){if(0===A)return[];const i=e.slice(0,A).sort(t);return o(e,t,i,A,e.length),i},t.topAsync=function(e,t,A,s,r){return 0===A?Promise.resolve([]):new Promise(((n,a)=>{(async()=>{const n=e.length,a=e.slice(0,A).sort(t);for(let h=A,g=Math.min(A+s,n);h<n;h=g,g=Math.min(g+s,n)){if(h>A&&await new Promise((e=>setTimeout(e))),r&&r.isCancellationRequested)throw new i.CancellationError;o(e,t,a,h,g)}return a})().then(n,a)}))},t.coalesce=function(e){return e.filter((e=>!!e))},t.coalesceInPlace=function(e){let t=0;for(let A=0;A<e.length;A++)e[A]&&(e[t]=e[A],t+=1);e.length=t},t.move=function(e,t,A){e.splice(A,0,e.splice(t,1)[0])},t.isFalsyOrEmpty=function(e){return!Array.isArray(e)||0===e.length},t.isNonEmptyArray=function(e){return Array.isArray(e)&&e.length>0},t.distinct=function(e,t=e=>e){const A=new Set;return e.filter((e=>{const i=t(e);return!A.has(i)&&(A.add(i),!0)}))},t.uniqueFilter=function(e){const t=new Set;return A=>{const i=e(A);return!t.has(i)&&(t.add(i),!0)}},t.firstOrDefault=function(e,t){return e.length>0?e[0]:t},t.lastOrDefault=function(e,t){return e.length>0?e[e.length-1]:t},t.commonPrefixLength=function(e,t,A=(e,t)=>e===t){let i=0;for(let s=0,r=Math.min(e.length,t.length);s<r&&A(e[s],t[s]);s++)i++;return i},t.range=function(e,t){let A="number"==typeof t?e:0;"number"==typeof t?A=e:(A=0,t=e);const i=[];if(A<=t)for(let e=A;e<t;e++)i.push(e);else for(let e=A;e>t;e--)i.push(e);return i},t.index=function(e,t,A){return e.reduce(((e,i)=>(e[t(i)]=A?A(i):i,e)),Object.create(null))},t.insert=function(e,t){return e.push(t),()=>a(e,t)},t.remove=a,t.arrayInsert=function(e,t,A){const i=e.slice(0,t),s=e.slice(t);return i.concat(A,s)},t.shuffle=function(e,t){let A;if("number"==typeof t){let e=t;A=()=>{const t=179426549*Math.sin(e++);return t-Math.floor(t)}}else A=Math.random;for(let t=e.length-1;t>0;t-=1){const i=Math.floor(A()*(t+1)),s=e[t];e[t]=e[i],e[i]=s}},t.pushToStart=function(e,t){const A=e.indexOf(t);A>-1&&(e.splice(A,1),e.unshift(t))},t.pushToEnd=function(e,t){const A=e.indexOf(t);A>-1&&(e.splice(A,1),e.push(t))},t.pushMany=function(e,t){for(const A of t)e.push(A)},t.mapArrayOrNot=function(e,t){return Array.isArray(e)?e.map(t):t(e)},t.asArray=function(e){return Array.isArray(e)?e:[e]},t.getRandomElement=function(e){return e[Math.floor(Math.random()*e.length)]},t.insertInto=h,t.splice=function(e,t,A,i){const s=g(e,t);let r=e.splice(s,A);return void 0===r&&(r=[]),h(e,s,i),r},t.compareBy=function(e,t){return(A,i)=>t(e(A),e(i))},t.tieBreakComparators=function(...e){return(t,A)=>{for(const i of e){const e=i(t,A);if(!l.isNeitherLessOrGreaterThan(e))return e}return l.neitherLessOrGreaterThan}},t.reverseOrder=function(e){return(t,A)=>-e(t,A)};const i=A(577),s=A(411);function r(e,t){let A=0,i=e-1;for(;A<=i;){const e=(A+i)/2|0,s=t(e);if(s<0)A=e+1;else{if(!(s>0))return e;i=e-1}}return-(A+1)}function n(e,t,A){const i=[];function s(e,t,A){if(0===t&&0===A.length)return;const s=i[i.length-1];s&&s.start+s.deleteCount===e?(s.deleteCount+=t,s.toInsert.push(...A)):i.push({start:e,deleteCount:t,toInsert:A})}let r=0,n=0;for(;;){if(r===e.length){s(r,0,t.slice(n));break}if(n===t.length){s(r,e.length-r,[]);break}const i=e[r],o=t[n],a=A(i,o);0===a?(r+=1,n+=1):a<0?(s(r,1,[]),r+=1):a>0&&(s(r,0,[o]),n+=1)}return i}function o(e,t,A,i,r){for(const n=A.length;i<r;i++){const r=e[i];if(t(r,A[n-1])<0){A.pop();const e=(0,s.findFirstIdxMonotonousOrArrLen)(A,(e=>t(r,e)<0));A.splice(e,0,r)}}}function a(e,t){const A=e.indexOf(t);if(A>-1)return e.splice(A,1),t}function h(e,t,A){const i=g(e,t),s=e.length,r=A.length;e.length=s+r;for(let t=s-1;t>=i;t--)e[t+r]=e[t];for(let t=0;t<r;t++)e[t+i]=A[t]}function g(e,t){return t<0?Math.max(t+e.length,0):Math.min(t,e.length)}var l;!function(e){e.isLessThan=function(e){return e<0},e.isLessThanOrEqual=function(e){return e<=0},e.isGreaterThan=function(e){return e>0},e.isNeitherLessOrGreaterThan=function(e){return 0===e},e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0}(l||(t.CompareResult=l={})),t.numberComparator=(e,t)=>e-t,t.booleanComparator=(e,A)=>(0,t.numberComparator)(e?1:0,A?1:0),t.ArrayQueue=class{constructor(e){this.items=e,this.firstIdx=0,this.lastIdx=this.items.length-1}get length(){return this.lastIdx-this.firstIdx+1}takeWhile(e){let t=this.firstIdx;for(;t<this.items.length&&e(this.items[t]);)t++;const A=t===this.firstIdx?null:this.items.slice(this.firstIdx,t);return this.firstIdx=t,A}takeFromEndWhile(e){let t=this.lastIdx;for(;t>=0&&e(this.items[t]);)t--;const A=t===this.lastIdx?null:this.items.slice(t+1,this.lastIdx+1);return this.lastIdx=t,A}peek(){if(0!==this.length)return this.items[this.firstIdx]}peekLast(){if(0!==this.length)return this.items[this.lastIdx]}dequeue(){const e=this.items[this.firstIdx];return this.firstIdx++,e}removeLast(){const e=this.items[this.lastIdx];return this.lastIdx--,e}takeCount(e){const t=this.items.slice(this.firstIdx,this.firstIdx+e);return this.firstIdx+=e,t}};class c{static{this.empty=new c((e=>{}))}constructor(e){this.iterate=e}forEach(e){this.iterate((t=>(e(t),!0)))}toArray(){const e=[];return this.iterate((t=>(e.push(t),!0))),e}filter(e){return new c((t=>this.iterate((A=>!e(A)||t(A)))))}map(e){return new c((t=>this.iterate((A=>t(e(A))))))}some(e){let t=!1;return this.iterate((A=>(t=e(A),!t))),t}findFirst(e){let t;return this.iterate((A=>!e(A)||(t=A,!1))),t}findLast(e){let t;return this.iterate((A=>(e(A)&&(t=A),!0))),t}findLastMaxBy(e){let t,A=!0;return this.iterate((i=>((A||l.isGreaterThan(e(i,t)))&&(A=!1,t=i),!0))),t}}t.CallbackIterable=c;class d{constructor(e){this._indexMap=e}static createSortPermutation(e,t){const A=Array.from(e.keys()).sort(((A,i)=>t(e[A],e[i])));return new d(A)}apply(e){return e.map(((t,A)=>e[this._indexMap[A]]))}inverse(){const e=this._indexMap.slice();for(let t=0;t<this._indexMap.length;t++)e[this._indexMap[t]]=t;return new d(e)}}t.Permutation=d},411:(e,t)=>{function A(e,t,A=e.length-1){for(let i=A;i>=0;i--)if(t(e[i]))return i;return-1}function i(e,t,A=0,i=e.length){let s=A,r=i;for(;s<r;){const A=Math.floor((s+r)/2);t(e[A])?s=A+1:r=A}return s-1}function s(e,t,A=0,i=e.length){let s=A,r=i;for(;s<r;){const A=Math.floor((s+r)/2);t(e[A])?r=A:s=A+1}return s}Object.defineProperty(t,"__esModule",{value:!0}),t.MonotonousArray=void 0,t.findLast=function(e,t){const i=A(e,t);if(-1!==i)return e[i]},t.findLastIdx=A,t.findLastMonotonous=function(e,t){const A=i(e,t);return-1===A?void 0:e[A]},t.findLastIdxMonotonous=i,t.findFirstMonotonous=function(e,t){const A=s(e,t);return A===e.length?void 0:e[A]},t.findFirstIdxMonotonousOrArrLen=s,t.findFirstIdxMonotonous=function(e,t,A=0,i=e.length){const r=s(e,t,A,i);return r===e.length?-1:r},t.findFirstMax=n,t.findLastMax=function(e,t){if(0===e.length)return;let A=e[0];for(let i=1;i<e.length;i++){const s=e[i];t(s,A)>=0&&(A=s)}return A},t.findFirstMin=function(e,t){return n(e,((e,A)=>-t(e,A)))},t.findMaxIdx=function(e,t){if(0===e.length)return-1;let A=0;for(let i=1;i<e.length;i++)t(e[i],e[A])>0&&(A=i);return A},t.mapFindFirst=function(e,t){for(const A of e){const e=t(A);if(void 0!==e)return e}};class r{static{this.assertInvariants=!1}constructor(e){this._array=e,this._findLastMonotonousLastIdx=0}findLastMonotonous(e){if(r.assertInvariants){if(this._prevFindLastPredicate)for(const t of this._array)if(this._prevFindLastPredicate(t)&&!e(t))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.");this._prevFindLastPredicate=e}const t=i(this._array,e,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=t+1,-1===t?void 0:this._array[t]}}function n(e,t){if(0===e.length)return;let A=e[0];for(let i=1;i<e.length;i++){const s=e[i];t(s,A)>0&&(A=s)}return A}t.MonotonousArray=r},33:(e,t)=>{var A;Object.defineProperty(t,"__esModule",{value:!0}),t.SetWithKey=void 0,t.groupBy=function(e,t){const A=Object.create(null);for(const i of e){const e=t(i);let s=A[e];s||(s=A[e]=[]),s.push(i)}return A},t.diffSets=function(e,t){const A=[],i=[];for(const i of e)t.has(i)||A.push(i);for(const A of t)e.has(A)||i.push(A);return{removed:A,added:i}},t.diffMaps=function(e,t){const A=[],i=[];for(const[i,s]of e)t.has(i)||A.push(s);for(const[A,s]of t)e.has(A)||i.push(s);return{removed:A,added:i}},t.intersection=function(e,t){const A=new Set;for(const i of t)e.has(i)&&A.add(i);return A};class i{static{A=Symbol.toStringTag}constructor(e,t){this.toKey=t,this._map=new Map,this[A]="SetWithKey";for(const t of e)this.add(t)}get size(){return this._map.size}add(e){const t=this.toKey(e);return this._map.set(t,e),this}delete(e){return this._map.delete(this.toKey(e))}has(e){return this._map.has(this.toKey(e))}*entries(){for(const e of this._map.values())yield[e,e]}keys(){return this.values()}*values(){for(const e of this._map.values())yield e}clear(){this._map.clear()}forEach(e,t){this._map.forEach((A=>e.call(t,A,A,this)))}[Symbol.iterator](){return this.values()}}t.SetWithKey=i},577:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BugIndicatingError=t.ErrorNoTelemetry=t.ExpectedError=t.NotSupportedError=t.NotImplementedError=t.ReadonlyError=t.CancellationError=t.errorHandler=t.ErrorHandler=void 0,t.setUnexpectedErrorHandler=function(e){t.errorHandler.setUnexpectedErrorHandler(e)},t.isSigPipeError=function(e){if(!e||"object"!=typeof e)return!1;const t=e;return"EPIPE"===t.code&&"WRITE"===t.syscall?.toUpperCase()},t.onUnexpectedError=function(e){s(e)||t.errorHandler.onUnexpectedError(e)},t.onUnexpectedExternalError=function(e){s(e)||t.errorHandler.onUnexpectedExternalError(e)},t.transformErrorForSerialization=function(e){if(e instanceof Error){const{name:t,message:A}=e;return{$isError:!0,name:t,message:A,stack:e.stacktrace||e.stack,noTelemetry:g.isErrorNoTelemetry(e)}}return e},t.transformErrorFromSerialization=function(e){let t;return e.noTelemetry?t=new g:(t=new Error,t.name=e.name),t.message=e.message,t.stack=e.stack,t},t.isCancellationError=s,t.canceled=function(){const e=new Error(i);return e.name=e.message,e},t.illegalArgument=function(e){return e?new Error(`Illegal argument: ${e}`):new Error("Illegal argument")},t.illegalState=function(e){return e?new Error(`Illegal state: ${e}`):new Error("Illegal state")},t.getErrorMessage=function(e){return e?e.message?e.message:e.stack?e.stack.split("\n")[0]:String(e):"Error"};class A{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout((()=>{if(e.stack){if(g.isErrorNoTelemetry(e))throw new g(e.message+"\n\n"+e.stack);throw new Error(e.message+"\n\n"+e.stack)}throw e}),0)}}addListener(e){return this.listeners.push(e),()=>{this._removeListener(e)}}emit(e){this.listeners.forEach((t=>{t(e)}))}_removeListener(e){this.listeners.splice(this.listeners.indexOf(e),1)}setUnexpectedErrorHandler(e){this.unexpectedErrorHandler=e}getUnexpectedErrorHandler(){return this.unexpectedErrorHandler}onUnexpectedError(e){this.unexpectedErrorHandler(e),this.emit(e)}onUnexpectedExternalError(e){this.unexpectedErrorHandler(e)}}t.ErrorHandler=A,t.errorHandler=new A;const i="Canceled";function s(e){return e instanceof r||e instanceof Error&&e.name===i&&e.message===i}class r extends Error{constructor(){super(i),this.name=this.message}}t.CancellationError=r;class n extends TypeError{constructor(e){super(e?`${e} is read-only and cannot be changed`:"Cannot change read-only property")}}t.ReadonlyError=n;class o extends Error{constructor(e){super("NotImplemented"),e&&(this.message=e)}}t.NotImplementedError=o;class a extends Error{constructor(e){super("NotSupported"),e&&(this.message=e)}}t.NotSupportedError=a;class h extends Error{constructor(){super(...arguments),this.isExpected=!0}}t.ExpectedError=h;class g extends Error{constructor(e){super(e),this.name="CodeExpectedError"}static fromError(e){if(e instanceof g)return e;const t=new g;return t.message=e.message,t.stack=e.stack,t}static isErrorNoTelemetry(e){return"CodeExpectedError"===e.name}}t.ErrorNoTelemetry=g;class l extends Error{constructor(e){super(e||"An unexpected bug occurred."),Object.setPrototypeOf(this,l.prototype)}}t.BugIndicatingError=l},355:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createSingleCallFunction=function(e,t){const A=this;let i,s=!1;return function(){if(s)return i;if(s=!0,t)try{i=e.apply(A,arguments)}finally{t()}else i=e.apply(A,arguments);return i}}},956:(e,t)=>{var A;Object.defineProperty(t,"__esModule",{value:!0}),t.Iterable=void 0,function(e){function t(e){return e&&"object"==typeof e&&"function"==typeof e[Symbol.iterator]}e.is=t;const A=Object.freeze([]);function*i(e){yield e}e.empty=function(){return A},e.single=i,e.wrap=function(e){return t(e)?e:i(e)},e.from=function(e){return e||A},e.reverse=function*(e){for(let t=e.length-1;t>=0;t--)yield e[t]},e.isEmpty=function(e){return!e||!0===e[Symbol.iterator]().next().done},e.first=function(e){return e[Symbol.iterator]().next().value},e.some=function(e,t){let A=0;for(const i of e)if(t(i,A++))return!0;return!1},e.find=function(e,t){for(const A of e)if(t(A))return A},e.filter=function*(e,t){for(const A of e)t(A)&&(yield A)},e.map=function*(e,t){let A=0;for(const i of e)yield t(i,A++)},e.flatMap=function*(e,t){let A=0;for(const i of e)yield*t(i,A++)},e.concat=function*(...e){for(const t of e)yield*t},e.reduce=function(e,t,A){let i=A;for(const A of e)i=t(i,A);return i},e.slice=function*(e,t,A=e.length){for(t<0&&(t+=e.length),A<0?A+=e.length:A>e.length&&(A=e.length);t<A;t++)yield e[t]},e.consume=function(t,A=Number.POSITIVE_INFINITY){const i=[];if(0===A)return[i,t];const s=t[Symbol.iterator]();for(let t=0;t<A;t++){const t=s.next();if(t.done)return[i,e.empty()];i.push(t.value)}return[i,{[Symbol.iterator]:()=>s}]},e.asyncToArray=async function(e){const t=[];for await(const A of e)t.push(A);return Promise.resolve(t)}}(A||(t.Iterable=A={}))},540:(e,t,A)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DisposableMap=t.ImmortalReference=t.AsyncReferenceCollection=t.ReferenceCollection=t.SafeDisposable=t.RefCountedDisposable=t.MandatoryMutableDisposable=t.MutableDisposable=t.Disposable=t.DisposableStore=t.DisposableTracker=void 0,t.setDisposableTracker=function(e){a=e},t.trackDisposable=g,t.markAsDisposed=l,t.markAsSingleton=function(e){return a?.markAsSingleton(e),e},t.isDisposable=d,t.dispose=I,t.disposeIfDisposable=function(e){for(const t of e)d(t)&&t.dispose();return[]},t.combinedDisposable=function(...e){const t=C((()=>I(e)));return function(e,t){if(a)for(const A of e)a.setParent(A,t)}(e,t),t},t.toDisposable=C,t.disposeOnReturn=function(e){const t=new u;try{e(t)}finally{t.dispose()}};const i=A(732),s=A(33),r=A(714),n=A(355),o=A(956);let a=null;class h{constructor(){this.livingDisposables=new Map}static{this.idx=0}getDisposableData(e){let t=this.livingDisposables.get(e);return t||(t={parent:null,source:null,isSingleton:!1,value:e,idx:h.idx++},this.livingDisposables.set(e,t)),t}trackDisposable(e){const t=this.getDisposableData(e);t.source||(t.source=(new Error).stack)}setParent(e,t){this.getDisposableData(e).parent=t}markAsDisposed(e){this.livingDisposables.delete(e)}markAsSingleton(e){this.getDisposableData(e).isSingleton=!0}getRootParent(e,t){const A=t.get(e);if(A)return A;const i=e.parent?this.getRootParent(this.getDisposableData(e.parent),t):e;return t.set(e,i),i}getTrackedDisposables(){const e=new Map;return[...this.livingDisposables.entries()].filter((([,t])=>null!==t.source&&!this.getRootParent(t,e).isSingleton)).flatMap((([e])=>e))}computeLeakingDisposables(e=10,t){let A;if(t)A=t;else{const e=new Map,t=[...this.livingDisposables.values()].filter((t=>null!==t.source&&!this.getRootParent(t,e).isSingleton));if(0===t.length)return;const i=new Set(t.map((e=>e.value)));if(A=t.filter((e=>!(e.parent&&i.has(e.parent)))),0===A.length)throw new Error("There are cyclic diposable chains!")}if(!A)return;function n(e){const t=e.source.split("\n").map((e=>e.trim().replace("at ",""))).filter((e=>""!==e));return function(e,t){for(;e.length>0&&t.some((t=>"string"==typeof t?t===e[0]:e[0].match(t)));)e.shift()}(t,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),t.reverse()}const o=new r.SetMap;for(const e of A){const t=n(e);for(let A=0;A<=t.length;A++)o.add(t.slice(0,A).join("\n"),e)}A.sort((0,i.compareBy)((e=>e.idx),i.numberComparator));let a="",h=0;for(const t of A.slice(0,e)){h++;const e=n(t),i=[];for(let t=0;t<e.length;t++){let r=e[t];r=`(shared with ${o.get(e.slice(0,t+1).join("\n")).size}/${A.length} leaks) at ${r}`;const a=o.get(e.slice(0,t).join("\n")),h=(0,s.groupBy)([...a].map((e=>n(e)[t])),(e=>e));delete h[e[t]];for(const[e,t]of Object.entries(h))i.unshift(`    - stacktraces of ${t.length} other leaks continue with ${e}`);i.unshift(r)}a+=`\n\n\n==================== Leaking disposable ${h}/${A.length}: ${t.value.constructor.name} ====================\n${i.join("\n")}\n============================================================\n\n`}return A.length>e&&(a+=`\n\n\n... and ${A.length-e} more leaking disposables\n\n`),{leaks:A,details:a}}}function g(e){return a?.trackDisposable(e),e}function l(e){a?.markAsDisposed(e)}function c(e,t){a?.setParent(e,t)}function d(e){return"object"==typeof e&&null!==e&&"function"==typeof e.dispose&&0===e.dispose.length}function I(e){if(o.Iterable.is(e)){const t=[];for(const A of e)if(A)try{A.dispose()}catch(e){t.push(e)}if(1===t.length)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}if(e)return e.dispose(),e}function C(e){const t=g({dispose:(0,n.createSingleCallFunction)((()=>{l(t),e()}))});return t}t.DisposableTracker=h;class u{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this._toDispose=new Set,this._isDisposed=!1,g(this)}dispose(){this._isDisposed||(l(this),this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(0!==this._toDispose.size)try{I(this._toDispose)}finally{this._toDispose.clear()}}add(e){if(!e)return e;if(e===this)throw new Error("Cannot register a disposable on itself!");return c(e,this),this._isDisposed?u.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(e),e}delete(e){if(e){if(e===this)throw new Error("Cannot dispose a disposable on itself!");this._toDispose.delete(e),e.dispose()}}deleteAndLeak(e){e&&this._toDispose.has(e)&&(this._toDispose.delete(e),c(e,null))}}t.DisposableStore=u;class B{static{this.None=Object.freeze({dispose(){}})}constructor(){this._store=new u,g(this),c(this._store,this)}dispose(){l(this),this._store.dispose()}_register(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(e)}}t.Disposable=B;class Q{constructor(){this._isDisposed=!1,g(this)}get value(){return this._isDisposed?void 0:this._value}set value(e){this._isDisposed||e===this._value||(this._value?.dispose(),e&&c(e,this),this._value=e)}clear(){this.value=void 0}dispose(){this._isDisposed=!0,l(this),this._value?.dispose(),this._value=void 0}clearAndLeak(){const e=this._value;return this._value=void 0,e&&c(e,null),e}}t.MutableDisposable=Q,t.MandatoryMutableDisposable=class{constructor(e){this._disposable=new Q,this._isDisposed=!1,this._disposable.value=e}get value(){return this._disposable.value}set value(e){this._isDisposed||e===this._disposable.value||(this._disposable.value=e)}dispose(){this._isDisposed=!0,this._disposable.dispose()}},t.RefCountedDisposable=class{constructor(e){this._disposable=e,this._counter=1}acquire(){return this._counter++,this}release(){return 0==--this._counter&&this._disposable.dispose(),this}},t.SafeDisposable=class{constructor(){this.dispose=()=>{},this.unset=()=>{},this.isset=()=>!1,g(this)}set(e){let t=e;return this.unset=()=>t=void 0,this.isset=()=>void 0!==t,this.dispose=()=>{t&&(t(),t=void 0,l(this))},this}},t.ReferenceCollection=class{constructor(){this.references=new Map}acquire(e,...t){let A=this.references.get(e);A||(A={counter:0,object:this.createReferencedObject(e,...t)},this.references.set(e,A));const{object:i}=A,s=(0,n.createSingleCallFunction)((()=>{0==--A.counter&&(this.destroyReferencedObject(e,A.object),this.references.delete(e))}));return A.counter++,{object:i,dispose:s}}},t.AsyncReferenceCollection=class{constructor(e){this.referenceCollection=e}async acquire(e,...t){const A=this.referenceCollection.acquire(e,...t);try{return{object:await A.object,dispose:()=>A.dispose()}}catch(e){throw A.dispose(),e}}},t.ImmortalReference=class{constructor(e){this.object=e}dispose(){}};class _{constructor(){this._store=new Map,this._isDisposed=!1,g(this)}dispose(){l(this),this._isDisposed=!0,this.clearAndDisposeAll()}clearAndDisposeAll(){if(this._store.size)try{I(this._store.values())}finally{this._store.clear()}}has(e){return this._store.has(e)}get size(){return this._store.size}get(e){return this._store.get(e)}set(e,t,A=!1){this._isDisposed&&console.warn(new Error("Trying to add a disposable to a DisposableMap that has already been disposed of. The added object will be leaked!").stack),A||this._store.get(e)?.dispose(),this._store.set(e,t)}deleteAndDispose(e){this._store.get(e)?.dispose(),this._store.delete(e)}deleteAndLeak(e){const t=this._store.get(e);return this._store.delete(e),t}keys(){return this._store.keys()}values(){return this._store.values()}[Symbol.iterator](){return this._store[Symbol.iterator]()}}t.DisposableMap=_},714:(e,t)=>{var A;Object.defineProperty(t,"__esModule",{value:!0}),t.SetMap=t.BidirectionalMap=t.CounterSet=t.Touch=void 0,t.getOrSet=function(e,t,A){let i=e.get(t);return void 0===i&&(i=A,e.set(t,i)),i},t.mapToString=function(e){const t=[];return e.forEach(((e,A)=>{t.push(`${A} => ${e}`)})),`Map(${e.size}) {${t.join(", ")}}`},t.setToString=function(e){const t=[];return e.forEach((e=>{t.push(e)})),`Set(${e.size}) {${t.join(", ")}}`},t.mapsStrictEqualIgnoreOrder=function(e,t){if(e===t)return!0;if(e.size!==t.size)return!1;for(const[A,i]of e)if(!t.has(A)||t.get(A)!==i)return!1;for(const[A]of t)if(!e.has(A))return!1;return!0},function(e){e[e.None=0]="None",e[e.AsOld=1]="AsOld",e[e.AsNew=2]="AsNew"}(A||(t.Touch=A={})),t.CounterSet=class{constructor(){this.map=new Map}add(e){return this.map.set(e,(this.map.get(e)||0)+1),this}delete(e){let t=this.map.get(e)||0;return 0!==t&&(t--,0===t?this.map.delete(e):this.map.set(e,t),!0)}has(e){return this.map.has(e)}},t.BidirectionalMap=class{constructor(e){if(this._m1=new Map,this._m2=new Map,e)for(const[t,A]of e)this.set(t,A)}clear(){this._m1.clear(),this._m2.clear()}set(e,t){this._m1.set(e,t),this._m2.set(t,e)}get(e){return this._m1.get(e)}getKey(e){return this._m2.get(e)}delete(e){const t=this._m1.get(e);return void 0!==t&&(this._m1.delete(e),this._m2.delete(t),!0)}forEach(e,t){this._m1.forEach(((A,i)=>{e.call(t,A,i,this)}))}keys(){return this._m1.keys()}values(){return this._m1.values()}},t.SetMap=class{constructor(){this.map=new Map}add(e,t){let A=this.map.get(e);A||(A=new Set,this.map.set(e,A)),A.add(t)}delete(e,t){const A=this.map.get(e);A&&(A.delete(t),0===A.size&&this.map.delete(e))}forEach(e,t){const A=this.map.get(e);A&&A.forEach(t)}get(e){return this.map.get(e)||new Set}}}},t={};function A(i){var s=t[i];if(void 0!==s)return s.exports;var r=t[i]={exports:{}};return e[i](r,r.exports,A),r.exports}var i={};return(()=>{var e=i;Object.defineProperty(e,"__esModule",{value:!0}),e.ImageAddon=void 0;const t=A(795),s=A(463),r=A(699),n=A(566),o={enableSizeReports:!0,pixelLimit:16777216,sixelSupport:!0,sixelScrolling:!0,sixelPaletteLimit:256,sixelSizeLimit:25e6,storageLimit:128,showPlaceholder:!0,iipSupport:!0,iipSizeLimit:2e7};e.ImageAddon=class{constructor(e){this._disposables=[],this._handlers=new Map,this._opts=Object.assign({},o,e),this._defaultOpts=Object.assign({},o,e)}dispose(){for(const e of this._disposables)e.dispose();this._disposables.length=0,this._handlers.clear()}_disposeLater(...e){for(const t of e)this._disposables.push(t)}activate(e){if(this._terminal=e,this._renderer=new s.ImageRenderer(e),this._storage=new r.ImageStorage(e,this._renderer,this._opts),this._opts.enableSizeReports){const t=e.options.windowOptions||{};t.getWinSizePixels=!0,t.getCellSizePixels=!0,t.getWinSizeChars=!0,e.options.windowOptions=t}if(this._disposeLater(this._renderer,this._storage,e.parser.registerCsiHandler({prefix:"?",final:"h"},(e=>this._decset(e))),e.parser.registerCsiHandler({prefix:"?",final:"l"},(e=>this._decrst(e))),e.parser.registerCsiHandler({final:"c"},(e=>this._da1(e))),e.parser.registerCsiHandler({prefix:"?",final:"S"},(e=>this._xtermGraphicsAttributes(e))),e.onRender((e=>{var t;return null===(t=this._storage)||void 0===t?void 0:t.render(e)})),e.parser.registerCsiHandler({intermediates:"!",final:"p"},(()=>this.reset())),e.parser.registerEscHandler({final:"c"},(()=>this.reset())),e._core._inputHandler.onRequestReset((()=>this.reset())),e.buffer.onBufferChange((()=>{var e;return null===(e=this._storage)||void 0===e?void 0:e.wipeAlternate()})),e.onResize((e=>{var t;return null===(t=this._storage)||void 0===t?void 0:t.viewportResize(e)}))),this._opts.sixelSupport){const t=new n.SixelHandler(this._opts,this._storage,e);this._handlers.set("sixel",t),this._disposeLater(e._core._inputHandler._parser.registerDcsHandler({final:"q"},t))}if(this._opts.iipSupport){const A=new t.IIPHandler(this._opts,this._renderer,this._storage,e);this._handlers.set("iip",A),this._disposeLater(e._core._inputHandler._parser.registerOscHandler(1337,A))}}reset(){var e;this._opts.sixelScrolling=this._defaultOpts.sixelScrolling,this._opts.sixelPaletteLimit=this._defaultOpts.sixelPaletteLimit,null===(e=this._storage)||void 0===e||e.reset();for(const e of this._handlers.values())e.reset();return!1}get storageLimit(){var e;return(null===(e=this._storage)||void 0===e?void 0:e.getLimit())||-1}set storageLimit(e){var t;null===(t=this._storage)||void 0===t||t.setLimit(e),this._opts.storageLimit=e}get storageUsage(){return this._storage?this._storage.getUsage():-1}get showPlaceholder(){return this._opts.showPlaceholder}set showPlaceholder(e){var t;this._opts.showPlaceholder=e,null===(t=this._renderer)||void 0===t||t.showPlaceholder(e)}getImageAtBufferCell(e,t){var A;return null===(A=this._storage)||void 0===A?void 0:A.getImageAtBufferCell(e,t)}extractTileAtBufferCell(e,t){var A;return null===(A=this._storage)||void 0===A?void 0:A.extractTileAtBufferCell(e,t)}_report(e){var t;null===(t=this._terminal)||void 0===t||t._core.coreService.triggerDataEvent(e)}_decset(e){for(let t=0;t<e.length;++t)80===e[t]&&(this._opts.sixelScrolling=!1);return!1}_decrst(e){for(let t=0;t<e.length;++t)80===e[t]&&(this._opts.sixelScrolling=!0);return!1}_da1(e){return!!e[0]||!!this._opts.sixelSupport&&(this._report("[?62;4;9;22c"),!0)}_xtermGraphicsAttributes(e){var t,A,i,s,n,o;if(e.length<2)return!0;if(1===e[0])switch(e[1]){case 1:return this._report(`[?${e[0]};0;${this._opts.sixelPaletteLimit}S`),!0;case 2:this._opts.sixelPaletteLimit=this._defaultOpts.sixelPaletteLimit,this._report(`[?${e[0]};0;${this._opts.sixelPaletteLimit}S`);for(const e of this._handlers.values())e.reset();return!0;case 3:return e.length>2&&!(e[2]instanceof Array)&&e[2]<=4096?(this._opts.sixelPaletteLimit=e[2],this._report(`[?${e[0]};0;${this._opts.sixelPaletteLimit}S`)):this._report(`[?${e[0]};2S`),!0;case 4:return this._report(`[?${e[0]};0;4096S`),!0;default:return this._report(`[?${e[0]};2S`),!0}if(2===e[0])switch(e[1]){case 1:let a=null===(A=null===(t=this._renderer)||void 0===t?void 0:t.dimensions)||void 0===A?void 0:A.css.canvas.width,h=null===(s=null===(i=this._renderer)||void 0===i?void 0:i.dimensions)||void 0===s?void 0:s.css.canvas.height;if(!a||!h){const e=r.CELL_SIZE_DEFAULT;a=((null===(n=this._terminal)||void 0===n?void 0:n.cols)||80)*e.width,h=((null===(o=this._terminal)||void 0===o?void 0:o.rows)||24)*e.height}if(a*h<this._opts.pixelLimit)this._report(`[?${e[0]};0;${a.toFixed(0)};${h.toFixed(0)}S`);else{const t=Math.floor(Math.sqrt(this._opts.pixelLimit));this._report(`[?${e[0]};0;${t};${t}S`)}return!0;case 4:const g=Math.floor(Math.sqrt(this._opts.pixelLimit));return this._report(`[?${e[0]};0;${g};${g}S`),!0;default:return this._report(`[?${e[0]};2S`),!0}return this._report(`[?${e[0]};1S`),!0}}})(),i})()));//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/18e3a1ec544e6907be1e944a94c496e302073435/node_modules/@xterm/addon-image/lib/addon-image.js.map