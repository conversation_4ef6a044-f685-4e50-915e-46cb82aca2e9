{"version": 3, "sources": ["../src/ProgressAddon.ts"], "sourcesContent": ["/**\n * Copyright (c) 2024 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport type { Terminal, ITerminalAddon, IDisposable } from '@xterm/xterm';\nimport type { ProgressAddon as IProgressApi, IProgressState } from '@xterm/addon-progress';\nimport type { Emitter, Event } from 'vs/base/common/event';\n\n\nconst enum ProgressType {\n  REMOVE = 0,\n  SET = 1,\n  ERROR = 2,\n  INDETERMINATE = 3,\n  PAUSE = 4\n}\n\n\n/**\n * Strict integer parsing, only decimal digits allowed.\n */\nfunction toInt(s: string): number {\n  let v = 0;\n  for (let i = 0; i < s.length; ++i) {\n    const c = s.charCodeAt(i);\n    if (c < 0x30 || 0x39 < c) {\n      return -1;\n    }\n    v = v * 10 + c - 48;\n  }\n  return v;\n}\n\n\nexport class ProgressAddon implements ITerminalAddon, IProgressApi {\n  private _seqHandler: IDisposable | undefined;\n  private _st: ProgressType = ProgressType.REMOVE;\n  private _pr = 0;\n  // HACK: This uses ! to align with the API, this should be fixed when 5283 is resolved\n  private _onChange!: Emitter<IProgressState>;\n  public onChange!: Event<IProgressState>;\n\n  public dispose(): void {\n    this._seqHandler?.dispose();\n    this._onChange?.dispose();\n  }\n\n  public activate(terminal: Terminal): void {\n    this._seqHandler = terminal.parser.registerOscHandler(9, data => {\n      if (!data.startsWith('4;')) {\n        return false;\n      }\n      const parts = data.split(';');\n\n      if (parts.length > 3) {\n        return true;  // faulty sequence, just exit\n      }\n      if (parts.length === 2) {\n        parts.push('');\n      }\n      const st = toInt(parts[1]);\n      const pr = toInt(parts[2]);\n\n      switch (st) {\n        case ProgressType.REMOVE:\n          this.progress = { state: st, value: 0 };\n          break;\n        case ProgressType.SET:\n          if (pr < 0) return true;  // faulty sequence, just exit\n          this.progress = { state: st, value: pr };\n          break;\n        case ProgressType.ERROR:\n        case ProgressType.PAUSE:\n          if (pr < 0) return true;  // faulty sequence, just exit\n          this.progress = { state: st, value: pr || this._pr };\n          break;\n        case ProgressType.INDETERMINATE:\n          this.progress = { state: st, value: this._pr };\n          break;\n      }\n      return true;\n    });\n    // FIXME: borrow emitter ctor from xterm, to be changed once #5283 is resolved\n    this._onChange = new (terminal as any)._core._onData.constructor();\n    this.onChange = this._onChange!.event;\n  }\n\n  public get progress(): IProgressState {\n    return { state: this._st, value: this._pr };\n  }\n\n  public set progress(progress: IProgressState) {\n    if (progress.state < 0 || progress.state > 4) {\n      console.warn(`progress state out of bounds, not applied`);\n      return;\n    }\n    this._st = progress.state;\n    this._pr = Math.min(Math.max(progress.value, 0), 100);\n    this._onChange?.fire({ state: this._st, value: this._pr });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAsBA,SAASA,EAAMC,EAAmB,CAChC,IAAIC,EAAI,EACR,QAASC,EAAI,EAAGA,EAAIF,EAAE,OAAQ,EAAEE,EAAG,CACjC,IAAMC,EAAIH,EAAE,WAAWE,CAAC,EACxB,GAAIC,EAAI,IAAQ,GAAOA,EACrB,MAAO,GAETF,EAAIA,EAAI,GAAKE,EAAI,EACnB,CACA,OAAOF,CACT,CAGO,IAAMG,EAAN,KAA4D,CAA5D,cAEL,KAAQ,IAAoB,EAC5B,KAAQ,IAAM,EAKP,SAAgB,CACrB,KAAK,aAAa,QAAQ,EAC1B,KAAK,WAAW,QAAQ,CAC1B,CAEO,SAASC,EAA0B,CACxC,KAAK,YAAcA,EAAS,OAAO,mBAAmB,EAAGC,GAAQ,CAC/D,GAAI,CAACA,EAAK,WAAW,IAAI,EACvB,MAAO,GAET,IAAMC,EAAQD,EAAK,MAAM,GAAG,EAE5B,GAAIC,EAAM,OAAS,EACjB,MAAO,GAELA,EAAM,SAAW,GACnBA,EAAM,KAAK,EAAE,EAEf,IAAMC,EAAKT,EAAMQ,EAAM,CAAC,CAAC,EACnBE,EAAKV,EAAMQ,EAAM,CAAC,CAAC,EAEzB,OAAQC,EAAI,CACV,IAAK,GACH,KAAK,SAAW,CAAE,MAAOA,EAAI,MAAO,CAAE,EACtC,MACF,IAAK,GACH,GAAIC,EAAK,EAAG,MAAO,GACnB,KAAK,SAAW,CAAE,MAAOD,EAAI,MAAOC,CAAG,EACvC,MACF,IAAK,GACL,IAAK,GACH,GAAIA,EAAK,EAAG,MAAO,GACnB,KAAK,SAAW,CAAE,MAAOD,EAAI,MAAOC,GAAM,KAAK,GAAI,EACnD,MACF,IAAK,GACH,KAAK,SAAW,CAAE,MAAOD,EAAI,MAAO,KAAK,GAAI,EAC7C,KACJ,CACA,MAAO,EACT,CAAC,EAED,KAAK,UAAY,IAAKH,EAAiB,MAAM,QAAQ,YACrD,KAAK,SAAW,KAAK,UAAW,KAClC,CAEA,IAAW,UAA2B,CACpC,MAAO,CAAE,MAAO,KAAK,IAAK,MAAO,KAAK,GAAI,CAC5C,CAEA,IAAW,SAASK,EAA0B,CAC5C,GAAIA,EAAS,MAAQ,GAAKA,EAAS,MAAQ,EAAG,CAC5C,QAAQ,KAAK,2CAA2C,EACxD,MACF,CACA,KAAK,IAAMA,EAAS,MACpB,KAAK,IAAM,KAAK,IAAI,KAAK,IAAIA,EAAS,MAAO,CAAC,EAAG,GAAG,EACpD,KAAK,WAAW,KAAK,CAAE,MAAO,KAAK,IAAK,MAAO,KAAK,GAAI,CAAC,CAC3D,CACF", "names": ["toInt", "s", "v", "i", "c", "ProgressAddon", "terminal", "data", "parts", "st", "pr", "progress"]}