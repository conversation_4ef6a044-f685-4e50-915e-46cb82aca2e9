{"name": "@xterm/addon-progress", "version": "0.2.0-beta.13", "author": {"name": "The xterm.js authors", "url": "https://xtermjs.org/"}, "main": "lib/addon-progress.js", "module": "lib/addon-progress.mjs", "types": "typings/addon-progress.d.ts", "repository": "https://github.com/xtermjs/xterm.js/tree/master/addons/addon-progress", "license": "MIT", "keywords": ["terminal", "xterm", "xterm.js"], "scripts": {"build": "../../node_modules/.bin/tsc -p .", "prepackage": "npm run build", "package": "../../node_modules/.bin/webpack", "prepublishOnly": "npm run package", "start": "node ../../demo/start"}, "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.107"}, "commit": "e9c547c1c6b67e9f09c24ccc007e19305f536e60"}