{"name": "@xterm/addon-webgl", "version": "0.19.0-beta.107", "author": {"name": "The xterm.js authors", "url": "https://xtermjs.org/"}, "main": "lib/addon-webgl.js", "module": "lib/addon-webgl.mjs", "types": "typings/addon-webgl.d.ts", "repository": "https://github.com/xtermjs/xterm.js/tree/master/addons/addon-webgl", "license": "MIT", "keywords": ["terminal", "webgl", "xterm", "xterm.js"], "scripts": {"build": "../../node_modules/.bin/tsc -p .", "prepackage": "npm run build", "package": "../../node_modules/.bin/webpack", "prepublishOnly": "npm run package", "start": "node ../../demo/start"}, "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.107"}, "commit": "e9c547c1c6b67e9f09c24ccc007e19305f536e60"}