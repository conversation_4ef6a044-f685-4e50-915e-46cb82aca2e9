/**
 * Copyright (c) 2014-2024 The xterm.js authors. All rights reserved.
 * @license MIT
 *
 * Copyright (c) 2012-2013, <PERSON> (MIT License)
 * @license MIT
 *
 * Originally forked from (with the author's permission):
 *   <PERSON><PERSON><PERSON>'s javascript vt100 for jslinux:
 *   http://bellard.org/jslinux/
 *   Copyright (c) 2011 F<PERSON>rice <PERSON>
 */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var Lr=Object.defineProperty;var wr=Object.getOwnPropertyDescriptor;var Yi=(i,e,t,n)=>{for(var s=n>1?void 0:n?wr(e,t):e,o=i.length-1,r;o>=0;o--)(r=i[o])&&(s=(n?r(e,t,s):r(s))||s);return n&&s&&Lr(e,t,s),s},Qi=(i,e)=>(t,n)=>e(t,n,i);var pi=class{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout(()=>{throw e.stack?bt.isErrorNoTelemetry(e)?new bt(e.message+`

`+e.stack):new Error(e.message+`

`+e.stack):e},0)}}addListener(e){return this.listeners.push(e),()=>{this._removeListener(e)}}emit(e){this.listeners.forEach(t=>{t(e)})}_removeListener(e){this.listeners.splice(this.listeners.indexOf(e),1)}setUnexpectedErrorHandler(e){this.unexpectedErrorHandler=e}getUnexpectedErrorHandler(){return this.unexpectedErrorHandler}onUnexpectedError(e){this.unexpectedErrorHandler(e),this.emit(e)}onUnexpectedExternalError(e){this.unexpectedErrorHandler(e)}},Rr=new pi;function Pe(i){Dr(i)||Rr.onUnexpectedError(i)}var fi="Canceled";function Dr(i){return i instanceof Ye?!0:i instanceof Error&&i.name===fi&&i.message===fi}var Ye=class extends Error{constructor(){super(fi),this.name=this.message}};var bt=class i extends Error{constructor(e){super(e),this.name="CodeExpectedError"}static fromError(e){if(e instanceof i)return e;let t=new i;return t.message=e.message,t.stack=e.stack,t}static isErrorNoTelemetry(e){return e.name==="CodeExpectedError"}};function Mr(i,e,t=0,n=i.length){let s=t,o=n;for(;s<o;){let r=Math.floor((s+o)/2);e(i[r])?s=r+1:o=r}return s-1}var Tt=class Tt{constructor(e){this._array=e;this._findLastMonotonousLastIdx=0}findLastMonotonous(e){if(Tt.assertInvariants){if(this._prevFindLastPredicate){for(let n of this._array)if(this._prevFindLastPredicate(n)&&!e(n))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this._prevFindLastPredicate=e}let t=Mr(this._array,e,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=t+1,t===-1?void 0:this._array[t]}};Tt.assertInvariants=!1;var Zi=Tt;var en;(a=>{function i(l){return l<0}a.isLessThan=i;function e(l){return l<=0}a.isLessThanOrEqual=e;function t(l){return l>0}a.isGreaterThan=t;function n(l){return l===0}a.isNeitherLessOrGreaterThan=n,a.greaterThan=1,a.lessThan=-1,a.neitherLessOrGreaterThan=0})(en||={});function tn(i,e){return(t,n)=>e(i(t),i(n))}var nn=(i,e)=>i-e;var Be=class Be{constructor(e){this.iterate=e}forEach(e){this.iterate(t=>(e(t),!0))}toArray(){let e=[];return this.iterate(t=>(e.push(t),!0)),e}filter(e){return new Be(t=>this.iterate(n=>e(n)?t(n):!0))}map(e){return new Be(t=>this.iterate(n=>t(e(n))))}some(e){let t=!1;return this.iterate(n=>(t=e(n),!t)),t}findFirst(e){let t;return this.iterate(n=>e(n)?(t=n,!1):!0),t}findLast(e){let t;return this.iterate(n=>(e(n)&&(t=n),!0)),t}findLastMaxBy(e){let t,n=!0;return this.iterate(s=>((n||en.isGreaterThan(e(s,t)))&&(n=!1,t=s),!0)),t}};Be.empty=new Be(e=>{});var Ji=Be;function an(i,e){let t=Object.create(null);for(let n of i){let s=e(n),o=t[s];o||(o=t[s]=[]),o.push(n)}return t}var sn,on,rn=class{constructor(e,t){this.toKey=t;this._map=new Map;this[sn]="SetWithKey";for(let n of e)this.add(n)}get size(){return this._map.size}add(e){let t=this.toKey(e);return this._map.set(t,e),this}delete(e){return this._map.delete(this.toKey(e))}has(e){return this._map.has(this.toKey(e))}*entries(){for(let e of this._map.values())yield[e,e]}keys(){return this.values()}*values(){for(let e of this._map.values())yield e}clear(){this._map.clear()}forEach(e,t){this._map.forEach(n=>e.call(t,n,n,this))}[(on=Symbol.iterator,sn=Symbol.toStringTag,on)](){return this.values()}};var vt=class{constructor(){this.map=new Map}add(e,t){let n=this.map.get(e);n||(n=new Set,this.map.set(e,n)),n.add(t)}delete(e,t){let n=this.map.get(e);n&&(n.delete(t),n.size===0&&this.map.delete(e))}forEach(e,t){let n=this.map.get(e);n&&n.forEach(t)}get(e){let t=this.map.get(e);return t||new Set}};function mi(i,e){let t=this,n=!1,s;return function(){if(n)return s;if(n=!0,e)try{s=i.apply(t,arguments)}finally{e()}else s=i.apply(t,arguments);return s}}var _i;(W=>{function i(E){return E&&typeof E=="object"&&typeof E[Symbol.iterator]=="function"}W.is=i;let e=Object.freeze([]);function t(){return e}W.empty=t;function*n(E){yield E}W.single=n;function s(E){return i(E)?E:n(E)}W.wrap=s;function o(E){return E||e}W.from=o;function*r(E){for(let y=E.length-1;y>=0;y--)yield E[y]}W.reverse=r;function a(E){return!E||E[Symbol.iterator]().next().done===!0}W.isEmpty=a;function l(E){return E[Symbol.iterator]().next().value}W.first=l;function u(E,y){let w=0;for(let G of E)if(y(G,w++))return!0;return!1}W.some=u;function c(E,y){for(let w of E)if(y(w))return w}W.find=c;function*d(E,y){for(let w of E)y(w)&&(yield w)}W.filter=d;function*h(E,y){let w=0;for(let G of E)yield y(G,w++)}W.map=h;function*f(E,y){let w=0;for(let G of E)yield*y(G,w++)}W.flatMap=f;function*I(...E){for(let y of E)yield*y}W.concat=I;function L(E,y,w){let G=w;for(let ue of E)G=y(G,ue);return G}W.reduce=L;function*M(E,y,w=E.length){for(y<0&&(y+=E.length),w<0?w+=E.length:w>E.length&&(w=E.length);y<w;y++)yield E[y]}W.slice=M;function q(E,y=Number.POSITIVE_INFINITY){let w=[];if(y===0)return[w,E];let G=E[Symbol.iterator]();for(let ue=0;ue<y;ue++){let Se=G.next();if(Se.done)return[w,W.empty()];w.push(Se.value)}return[w,{[Symbol.iterator](){return G}}]}W.consume=q;async function S(E){let y=[];for await(let w of E)y.push(w);return Promise.resolve(y)}W.asyncToArray=S})(_i||={});var Ar=!1,Ne=null,gt=class gt{constructor(){this.livingDisposables=new Map}getDisposableData(e){let t=this.livingDisposables.get(e);return t||(t={parent:null,source:null,isSingleton:!1,value:e,idx:gt.idx++},this.livingDisposables.set(e,t)),t}trackDisposable(e){let t=this.getDisposableData(e);t.source||(t.source=new Error().stack)}setParent(e,t){let n=this.getDisposableData(e);n.parent=t}markAsDisposed(e){this.livingDisposables.delete(e)}markAsSingleton(e){this.getDisposableData(e).isSingleton=!0}getRootParent(e,t){let n=t.get(e);if(n)return n;let s=e.parent?this.getRootParent(this.getDisposableData(e.parent),t):e;return t.set(e,s),s}getTrackedDisposables(){let e=new Map;return[...this.livingDisposables.entries()].filter(([,n])=>n.source!==null&&!this.getRootParent(n,e).isSingleton).flatMap(([n])=>n)}computeLeakingDisposables(e=10,t){let n;if(t)n=t;else{let l=new Map,u=[...this.livingDisposables.values()].filter(d=>d.source!==null&&!this.getRootParent(d,l).isSingleton);if(u.length===0)return;let c=new Set(u.map(d=>d.value));if(n=u.filter(d=>!(d.parent&&c.has(d.parent))),n.length===0)throw new Error("There are cyclic diposable chains!")}if(!n)return;function s(l){function u(d,h){for(;d.length>0&&h.some(f=>typeof f=="string"?f===d[0]:d[0].match(f));)d.shift()}let c=l.source.split(`
`).map(d=>d.trim().replace("at ","")).filter(d=>d!=="");return u(c,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),c.reverse()}let o=new vt;for(let l of n){let u=s(l);for(let c=0;c<=u.length;c++)o.add(u.slice(0,c).join(`
`),l)}n.sort(tn(l=>l.idx,nn));let r="",a=0;for(let l of n.slice(0,e)){a++;let u=s(l),c=[];for(let d=0;d<u.length;d++){let h=u[d];h=`(shared with ${o.get(u.slice(0,d+1).join(`
`)).size}/${n.length} leaks) at ${h}`;let I=o.get(u.slice(0,d).join(`
`)),L=an([...I].map(M=>s(M)[d]),M=>M);delete L[u[d]];for(let[M,q]of Object.entries(L))c.unshift(`    - stacktraces of ${q.length} other leaks continue with ${M}`);c.unshift(h)}r+=`


==================== Leaking disposable ${a}/${n.length}: ${l.value.constructor.name} ====================
${c.join(`
`)}
============================================================

`}return n.length>e&&(r+=`


... and ${n.length-e} more leaking disposables

`),{leaks:n,details:r}}};gt.idx=0;var ln=gt;function Sr(i){Ne=i}if(Ar){let i="__is_disposable_tracked__";Sr(new class{trackDisposable(e){let t=new Error("Potentially leaked disposable").stack;setTimeout(()=>{e[i]||console.log(t)},3e3)}setParent(e,t){if(e&&e!==B.None)try{e[i]=!0}catch{}}markAsDisposed(e){if(e&&e!==B.None)try{e[i]=!0}catch{}}markAsSingleton(e){}})}function Et(i){return Ne?.trackDisposable(i),i}function yt(i){Ne?.markAsDisposed(i)}function Qe(i,e){Ne?.setParent(i,e)}function Or(i,e){if(Ne)for(let t of i)Ne.setParent(t,e)}function un(i){if(_i.is(i)){let e=[];for(let t of i)if(t)try{t.dispose()}catch(n){e.push(n)}if(e.length===1)throw e[0];if(e.length>1)throw new AggregateError(e,"Encountered errors while disposing of store");return Array.isArray(i)?[]:i}else if(i)return i.dispose(),i}function It(...i){let e=O(()=>un(i));return Or(i,e),e}function O(i){let e=Et({dispose:mi(()=>{yt(e),i()})});return e}var xt=class xt{constructor(){this._toDispose=new Set;this._isDisposed=!1;Et(this)}dispose(){this._isDisposed||(yt(this),this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(this._toDispose.size!==0)try{un(this._toDispose)}finally{this._toDispose.clear()}}add(e){if(!e)return e;if(e===this)throw new Error("Cannot register a disposable on itself!");return Qe(e,this),this._isDisposed?xt.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(e),e}delete(e){if(e){if(e===this)throw new Error("Cannot dispose a disposable on itself!");this._toDispose.delete(e),e.dispose()}}deleteAndLeak(e){e&&this._toDispose.has(e)&&(this._toDispose.delete(e),Qe(e,null))}};xt.DISABLE_DISPOSED_WARNING=!1;var fe=xt,B=class{constructor(){this._store=new fe;Et(this),Qe(this._store,this)}dispose(){yt(this),this._store.dispose()}_register(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(e)}};B.None=Object.freeze({dispose(){}});var be=class{constructor(){this._isDisposed=!1;Et(this)}get value(){return this._isDisposed?void 0:this._value}set value(e){this._isDisposed||e===this._value||(this._value?.dispose(),e&&Qe(e,this),this._value=e)}clear(){this.value=void 0}dispose(){this._isDisposed=!0,yt(this),this._value?.dispose(),this._value=void 0}clearAndLeak(){let e=this._value;return this._value=void 0,e&&Qe(e,null),e}};var Lt=typeof process<"u"&&"title"in process,Ze=Lt?"node":navigator.userAgent,bi=Lt?"node":navigator.platform,cn=Ze.includes("Firefox"),dn=Ze.includes("Edge"),Ti=/^((?!chrome|android).)*safari/i.test(Ze);function hn(){if(!Ti)return 0;let i=Ze.match(/Version\/(\d+)/);return i===null||i.length<2?0:parseInt(i[1])}var oo=["Macintosh","MacIntel","MacPPC","Mac68K"].includes(bi);var ao=["Windows","Win16","Win32","WinCE"].includes(bi),lo=bi.indexOf("Linux")>=0,uo=/\bCrOS\b/.test(Ze);var pn="";var K=0,V=0,C=0,U=0,Z={css:"#00000000",rgba:0},X;(n=>{function i(s,o,r,a){return a!==void 0?`#${Oe(s)}${Oe(o)}${Oe(r)}${Oe(a)}`:`#${Oe(s)}${Oe(o)}${Oe(r)}`}n.toCss=i;function e(s,o,r,a=255){return(s<<24|o<<16|r<<8|a)>>>0}n.toRgba=e;function t(s,o,r,a){return{css:n.toCss(s,o,r,a),rgba:n.toRgba(s,o,r,a)}}n.toColor=t})(X||={});var Ue;(a=>{function i(l,u){if(U=(u.rgba&255)/255,U===1)return{css:u.css,rgba:u.rgba};let c=u.rgba>>24&255,d=u.rgba>>16&255,h=u.rgba>>8&255,f=l.rgba>>24&255,I=l.rgba>>16&255,L=l.rgba>>8&255;K=f+Math.round((c-f)*U),V=I+Math.round((d-I)*U),C=L+Math.round((h-L)*U);let M=X.toCss(K,V,C),q=X.toRgba(K,V,C);return{css:M,rgba:q}}a.blend=i;function e(l){return(l.rgba&255)===255}a.isOpaque=e;function t(l,u,c){let d=ve.ensureContrastRatio(l.rgba,u.rgba,c);if(d)return X.toColor(d>>24&255,d>>16&255,d>>8&255)}a.ensureContrastRatio=t;function n(l){let u=(l.rgba|255)>>>0;return[K,V,C]=ve.toChannels(u),{css:X.toCss(K,V,C),rgba:u}}a.opaque=n;function s(l,u){return U=Math.round(u*255),[K,V,C]=ve.toChannels(l.rgba),{css:X.toCss(K,V,C,U),rgba:X.toRgba(K,V,C,U)}}a.opacity=s;function o(l,u){return U=l.rgba&255,s(l,U*u/255)}a.multiplyOpacity=o;function r(l){return[l.rgba>>24&255,l.rgba>>16&255,l.rgba>>8&255]}a.toColorRGB=r})(Ue||={});var Fr;(n=>{let i,e;try{let s=document.createElement("canvas");s.width=1,s.height=1;let o=s.getContext("2d",{willReadFrequently:!0});o&&(i=o,i.globalCompositeOperation="copy",e=i.createLinearGradient(0,0,1,1))}catch{}function t(s){if(s.match(/#[\da-f]{3,8}/i))switch(s.length){case 4:return K=parseInt(s.slice(1,2).repeat(2),16),V=parseInt(s.slice(2,3).repeat(2),16),C=parseInt(s.slice(3,4).repeat(2),16),X.toColor(K,V,C);case 5:return K=parseInt(s.slice(1,2).repeat(2),16),V=parseInt(s.slice(2,3).repeat(2),16),C=parseInt(s.slice(3,4).repeat(2),16),U=parseInt(s.slice(4,5).repeat(2),16),X.toColor(K,V,C,U);case 7:return{css:s,rgba:(parseInt(s.slice(1),16)<<8|255)>>>0};case 9:return{css:s,rgba:parseInt(s.slice(1),16)>>>0}}let o=s.match(/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(,\s*(0|1|\d?\.(\d+))\s*)?\)/);if(o)return K=parseInt(o[1]),V=parseInt(o[2]),C=parseInt(o[3]),U=Math.round((o[5]===void 0?1:parseFloat(o[5]))*255),X.toColor(K,V,C,U);if(!i||!e)throw new Error("css.toColor: Unsupported css format");if(i.fillStyle=e,i.fillStyle=s,typeof i.fillStyle!="string")throw new Error("css.toColor: Unsupported css format");if(i.fillRect(0,0,1,1),[K,V,C,U]=i.getImageData(0,0,1,1).data,U!==255)throw new Error("css.toColor: Unsupported css format");return{rgba:X.toRgba(K,V,C,U),css:s}}n.toColor=t})(Fr||={});var Y;(t=>{function i(n){return e(n>>16&255,n>>8&255,n&255)}t.relativeLuminance=i;function e(n,s,o){let r=n/255,a=s/255,l=o/255,u=r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4),c=a<=.03928?a/12.92:Math.pow((a+.055)/1.055,2.4),d=l<=.03928?l/12.92:Math.pow((l+.055)/1.055,2.4);return u*.2126+c*.7152+d*.0722}t.relativeLuminance2=e})(Y||={});var ve;(o=>{function i(r,a){if(U=(a&255)/255,U===1)return a;let l=a>>24&255,u=a>>16&255,c=a>>8&255,d=r>>24&255,h=r>>16&255,f=r>>8&255;return K=d+Math.round((l-d)*U),V=h+Math.round((u-h)*U),C=f+Math.round((c-f)*U),X.toRgba(K,V,C)}o.blend=i;function e(r,a,l){let u=Y.relativeLuminance(r>>8),c=Y.relativeLuminance(a>>8);if(Te(u,c)<l){if(c<u){let I=t(r,a,l),L=Te(u,Y.relativeLuminance(I>>8));if(L<l){let M=n(r,a,l),q=Te(u,Y.relativeLuminance(M>>8));return L>q?I:M}return I}let h=n(r,a,l),f=Te(u,Y.relativeLuminance(h>>8));if(f<l){let I=t(r,a,l),L=Te(u,Y.relativeLuminance(I>>8));return f>L?h:I}return h}}o.ensureContrastRatio=e;function t(r,a,l){let u=r>>24&255,c=r>>16&255,d=r>>8&255,h=a>>24&255,f=a>>16&255,I=a>>8&255,L=Te(Y.relativeLuminance2(h,f,I),Y.relativeLuminance2(u,c,d));for(;L<l&&(h>0||f>0||I>0);)h-=Math.max(0,Math.ceil(h*.1)),f-=Math.max(0,Math.ceil(f*.1)),I-=Math.max(0,Math.ceil(I*.1)),L=Te(Y.relativeLuminance2(h,f,I),Y.relativeLuminance2(u,c,d));return(h<<24|f<<16|I<<8|255)>>>0}o.reduceLuminance=t;function n(r,a,l){let u=r>>24&255,c=r>>16&255,d=r>>8&255,h=a>>24&255,f=a>>16&255,I=a>>8&255,L=Te(Y.relativeLuminance2(h,f,I),Y.relativeLuminance2(u,c,d));for(;L<l&&(h<255||f<255||I<255);)h=Math.min(255,h+Math.ceil((255-h)*.1)),f=Math.min(255,f+Math.ceil((255-f)*.1)),I=Math.min(255,I+Math.ceil((255-I)*.1)),L=Te(Y.relativeLuminance2(h,f,I),Y.relativeLuminance2(u,c,d));return(h<<24|f<<16|I<<8|255)>>>0}o.increaseLuminance=n;function s(r){return[r>>24&255,r>>16&255,r>>8&255,r&255]}o.toChannels=s})(ve||={});function Oe(i){let e=i.toString(16);return e.length<2?"0"+e:e}function Te(i,e){return i<e?(e+.05)/(i+.05):(i+.05)/(e+.05)}function F(i){if(!i)throw new Error("value must not be falsy");return i}function Rt(i){return 57508<=i&&i<=57558}function fn(i){return 57520<=i&&i<=57527}function kr(i){return 57344<=i&&i<=63743}function Pr(i){return 9472<=i&&i<=9631}function Br(i){return i>=128512&&i<=128591||i>=127744&&i<=128511||i>=128640&&i<=128767||i>=9728&&i<=9983||i>=9984&&i<=10175||i>=65024&&i<=65039||i>=129280&&i<=129535||i>=127462&&i<=127487}function mn(i,e,t,n){return e===1&&t>Math.ceil(n*1.5)&&i!==void 0&&i>255&&!Br(i)&&!Rt(i)&&!kr(i)}function Dt(i){return Rt(i)||Pr(i)}function _n(){return{css:{canvas:wt(),cell:wt()},device:{canvas:wt(),cell:wt(),char:{width:0,height:0,left:0,top:0}}}}function wt(){return{width:0,height:0}}function bn(i,e,t=0){return(i-(Math.round(e)*2-t))%(Math.round(e)*2)}var j=0,z=0,me=!1,ge=!1,Mt=!1,J,vi=0,At=class{constructor(e,t,n,s,o,r){this._terminal=e;this._optionService=t;this._selectionRenderModel=n;this._decorationService=s;this._coreBrowserService=o;this._themeService=r;this.result={fg:0,bg:0,ext:0}}resolve(e,t,n,s){if(this.result.bg=e.bg,this.result.fg=e.fg,this.result.ext=e.bg&268435456?e.extended.ext:0,z=0,j=0,ge=!1,me=!1,Mt=!1,J=this._themeService.colors,vi=0,e.getCode()!==0&&e.extended.underlineStyle===4){let r=Math.max(1,Math.floor(this._optionService.rawOptions.fontSize*this._coreBrowserService.dpr/15));vi=t*s%(Math.round(r)*2)}if(this._decorationService.forEachDecorationAtCell(t,n,"bottom",r=>{r.backgroundColorRGB&&(z=r.backgroundColorRGB.rgba>>8&16777215,ge=!0),r.foregroundColorRGB&&(j=r.foregroundColorRGB.rgba>>8&16777215,me=!0)}),Mt=this._selectionRenderModel.isCellSelected(this._terminal,t,n),Mt){if(this.result.fg&67108864||(this.result.bg&50331648)!==0){if(this.result.fg&67108864)switch(this.result.fg&50331648){case 16777216:case 33554432:z=this._themeService.colors.ansi[this.result.fg&255].rgba;break;case 50331648:z=(this.result.fg&16777215)<<8|255;break;case 0:default:z=this._themeService.colors.foreground.rgba}else switch(this.result.bg&50331648){case 16777216:case 33554432:z=this._themeService.colors.ansi[this.result.bg&255].rgba;break;case 50331648:z=(this.result.bg&16777215)<<8|255;break}z=ve.blend(z,(this._coreBrowserService.isFocused?J.selectionBackgroundOpaque:J.selectionInactiveBackgroundOpaque).rgba&4294967040|128)>>8&16777215}else z=(this._coreBrowserService.isFocused?J.selectionBackgroundOpaque:J.selectionInactiveBackgroundOpaque).rgba>>8&16777215;if(ge=!0,J.selectionForeground&&(j=J.selectionForeground.rgba>>8&16777215,me=!0),Dt(e.getCode())){if(this.result.fg&67108864&&(this.result.bg&50331648)===0)j=(this._coreBrowserService.isFocused?J.selectionBackgroundOpaque:J.selectionInactiveBackgroundOpaque).rgba>>8&16777215;else{if(this.result.fg&67108864)switch(this.result.bg&50331648){case 16777216:case 33554432:j=this._themeService.colors.ansi[this.result.bg&255].rgba;break;case 50331648:j=(this.result.bg&16777215)<<8|255;break}else switch(this.result.fg&50331648){case 16777216:case 33554432:j=this._themeService.colors.ansi[this.result.fg&255].rgba;break;case 50331648:j=(this.result.fg&16777215)<<8|255;break;case 0:default:j=this._themeService.colors.foreground.rgba}j=ve.blend(j,(this._coreBrowserService.isFocused?J.selectionBackgroundOpaque:J.selectionInactiveBackgroundOpaque).rgba&4294967040|128)>>8&16777215}me=!0}}this._decorationService.forEachDecorationAtCell(t,n,"top",r=>{r.backgroundColorRGB&&(z=r.backgroundColorRGB.rgba>>8&16777215,ge=!0),r.foregroundColorRGB&&(j=r.foregroundColorRGB.rgba>>8&16777215,me=!0)}),ge&&(Mt?z=e.bg&-16777216&-134217729|z|50331648:z=e.bg&-16777216|z|50331648),me&&(j=e.fg&-16777216&-67108865|j|50331648),this.result.fg&67108864&&(ge&&!me&&((this.result.bg&50331648)===0?j=this.result.fg&-134217728|J.background.rgba>>8&16777215&16777215|50331648:j=this.result.fg&-134217728|this.result.bg&67108863,me=!0),!ge&&me&&((this.result.fg&50331648)===0?z=this.result.bg&-67108864|J.foreground.rgba>>8&16777215&16777215|50331648:z=this.result.bg&-67108864|this.result.fg&67108863,ge=!0)),J=void 0,this.result.bg=ge?z:this.result.bg,this.result.fg=me?j:this.result.fg,this.result.ext&=536870911,this.result.ext|=vi<<29&3758096384}};var gn=.5,St=cn||dn?"bottom":"ideographic";var Hr={"\u2580":[{x:0,y:0,w:8,h:4}],"\u2581":[{x:0,y:7,w:8,h:1}],"\u2582":[{x:0,y:6,w:8,h:2}],"\u2583":[{x:0,y:5,w:8,h:3}],"\u2584":[{x:0,y:4,w:8,h:4}],"\u2585":[{x:0,y:3,w:8,h:5}],"\u2586":[{x:0,y:2,w:8,h:6}],"\u2587":[{x:0,y:1,w:8,h:7}],"\u2588":[{x:0,y:0,w:8,h:8}],"\u2589":[{x:0,y:0,w:7,h:8}],"\u258A":[{x:0,y:0,w:6,h:8}],"\u258B":[{x:0,y:0,w:5,h:8}],"\u258C":[{x:0,y:0,w:4,h:8}],"\u258D":[{x:0,y:0,w:3,h:8}],"\u258E":[{x:0,y:0,w:2,h:8}],"\u258F":[{x:0,y:0,w:1,h:8}],"\u2590":[{x:4,y:0,w:4,h:8}],"\u2594":[{x:0,y:0,w:8,h:1}],"\u2595":[{x:7,y:0,w:1,h:8}],"\u2596":[{x:0,y:4,w:4,h:4}],"\u2597":[{x:4,y:4,w:4,h:4}],"\u2598":[{x:0,y:0,w:4,h:4}],"\u2599":[{x:0,y:0,w:4,h:8},{x:0,y:4,w:8,h:4}],"\u259A":[{x:0,y:0,w:4,h:4},{x:4,y:4,w:4,h:4}],"\u259B":[{x:0,y:0,w:4,h:8},{x:4,y:0,w:4,h:4}],"\u259C":[{x:0,y:0,w:8,h:4},{x:4,y:0,w:4,h:8}],"\u259D":[{x:4,y:0,w:4,h:4}],"\u259E":[{x:4,y:0,w:4,h:4},{x:0,y:4,w:4,h:4}],"\u259F":[{x:4,y:0,w:4,h:8},{x:0,y:4,w:8,h:4}],"\u{1FB70}":[{x:1,y:0,w:1,h:8}],"\u{1FB71}":[{x:2,y:0,w:1,h:8}],"\u{1FB72}":[{x:3,y:0,w:1,h:8}],"\u{1FB73}":[{x:4,y:0,w:1,h:8}],"\u{1FB74}":[{x:5,y:0,w:1,h:8}],"\u{1FB75}":[{x:6,y:0,w:1,h:8}],"\u{1FB76}":[{x:0,y:1,w:8,h:1}],"\u{1FB77}":[{x:0,y:2,w:8,h:1}],"\u{1FB78}":[{x:0,y:3,w:8,h:1}],"\u{1FB79}":[{x:0,y:4,w:8,h:1}],"\u{1FB7A}":[{x:0,y:5,w:8,h:1}],"\u{1FB7B}":[{x:0,y:6,w:8,h:1}],"\u{1FB7C}":[{x:0,y:0,w:1,h:8},{x:0,y:7,w:8,h:1}],"\u{1FB7D}":[{x:0,y:0,w:1,h:8},{x:0,y:0,w:8,h:1}],"\u{1FB7E}":[{x:7,y:0,w:1,h:8},{x:0,y:0,w:8,h:1}],"\u{1FB7F}":[{x:7,y:0,w:1,h:8},{x:0,y:7,w:8,h:1}],"\u{1FB80}":[{x:0,y:0,w:8,h:1},{x:0,y:7,w:8,h:1}],"\u{1FB81}":[{x:0,y:0,w:8,h:1},{x:0,y:2,w:8,h:1},{x:0,y:4,w:8,h:1},{x:0,y:7,w:8,h:1}],"\u{1FB82}":[{x:0,y:0,w:8,h:2}],"\u{1FB83}":[{x:0,y:0,w:8,h:3}],"\u{1FB84}":[{x:0,y:0,w:8,h:5}],"\u{1FB85}":[{x:0,y:0,w:8,h:6}],"\u{1FB86}":[{x:0,y:0,w:8,h:7}],"\u{1FB87}":[{x:6,y:0,w:2,h:8}],"\u{1FB88}":[{x:5,y:0,w:3,h:8}],"\u{1FB89}":[{x:3,y:0,w:5,h:8}],"\u{1FB8A}":[{x:2,y:0,w:6,h:8}],"\u{1FB8B}":[{x:1,y:0,w:7,h:8}],"\u{1FB95}":[{x:0,y:0,w:2,h:2},{x:4,y:0,w:2,h:2},{x:2,y:2,w:2,h:2},{x:6,y:2,w:2,h:2},{x:0,y:4,w:2,h:2},{x:4,y:4,w:2,h:2},{x:2,y:6,w:2,h:2},{x:6,y:6,w:2,h:2}],"\u{1FB96}":[{x:2,y:0,w:2,h:2},{x:6,y:0,w:2,h:2},{x:0,y:2,w:2,h:2},{x:4,y:2,w:2,h:2},{x:2,y:4,w:2,h:2},{x:6,y:4,w:2,h:2},{x:0,y:6,w:2,h:2},{x:4,y:6,w:2,h:2}],"\u{1FB97}":[{x:0,y:2,w:8,h:2},{x:0,y:6,w:8,h:2}]},Wr={"\u2591":[[1,0,0,0],[0,0,0,0],[0,0,1,0],[0,0,0,0]],"\u2592":[[1,0],[0,0],[0,1],[0,0]],"\u2593":[[0,1],[1,1],[1,0],[1,1]]};var Gr={"\u2500":{1:"M0,.5 L1,.5"},"\u2501":{3:"M0,.5 L1,.5"},"\u2502":{1:"M.5,0 L.5,1"},"\u2503":{3:"M.5,0 L.5,1"},"\u250C":{1:"M0.5,1 L.5,.5 L1,.5"},"\u250F":{3:"M0.5,1 L.5,.5 L1,.5"},"\u2510":{1:"M0,.5 L.5,.5 L.5,1"},"\u2513":{3:"M0,.5 L.5,.5 L.5,1"},"\u2514":{1:"M.5,0 L.5,.5 L1,.5"},"\u2517":{3:"M.5,0 L.5,.5 L1,.5"},"\u2518":{1:"M.5,0 L.5,.5 L0,.5"},"\u251B":{3:"M.5,0 L.5,.5 L0,.5"},"\u251C":{1:"M.5,0 L.5,1 M.5,.5 L1,.5"},"\u2523":{3:"M.5,0 L.5,1 M.5,.5 L1,.5"},"\u2524":{1:"M.5,0 L.5,1 M.5,.5 L0,.5"},"\u252B":{3:"M.5,0 L.5,1 M.5,.5 L0,.5"},"\u252C":{1:"M0,.5 L1,.5 M.5,.5 L.5,1"},"\u2533":{3:"M0,.5 L1,.5 M.5,.5 L.5,1"},"\u2534":{1:"M0,.5 L1,.5 M.5,.5 L.5,0"},"\u253B":{3:"M0,.5 L1,.5 M.5,.5 L.5,0"},"\u253C":{1:"M0,.5 L1,.5 M.5,0 L.5,1"},"\u254B":{3:"M0,.5 L1,.5 M.5,0 L.5,1"},"\u2574":{1:"M.5,.5 L0,.5"},"\u2578":{3:"M.5,.5 L0,.5"},"\u2575":{1:"M.5,.5 L.5,0"},"\u2579":{3:"M.5,.5 L.5,0"},"\u2576":{1:"M.5,.5 L1,.5"},"\u257A":{3:"M.5,.5 L1,.5"},"\u2577":{1:"M.5,.5 L.5,1"},"\u257B":{3:"M.5,.5 L.5,1"},"\u2550":{1:(i,e)=>`M0,${.5-e} L1,${.5-e} M0,${.5+e} L1,${.5+e}`},"\u2551":{1:(i,e)=>`M${.5-i},0 L${.5-i},1 M${.5+i},0 L${.5+i},1`},"\u2552":{1:(i,e)=>`M.5,1 L.5,${.5-e} L1,${.5-e} M.5,${.5+e} L1,${.5+e}`},"\u2553":{1:(i,e)=>`M${.5-i},1 L${.5-i},.5 L1,.5 M${.5+i},.5 L${.5+i},1`},"\u2554":{1:(i,e)=>`M1,${.5-e} L${.5-i},${.5-e} L${.5-i},1 M1,${.5+e} L${.5+i},${.5+e} L${.5+i},1`},"\u2555":{1:(i,e)=>`M0,${.5-e} L.5,${.5-e} L.5,1 M0,${.5+e} L.5,${.5+e}`},"\u2556":{1:(i,e)=>`M${.5+i},1 L${.5+i},.5 L0,.5 M${.5-i},.5 L${.5-i},1`},"\u2557":{1:(i,e)=>`M0,${.5+e} L${.5-i},${.5+e} L${.5-i},1 M0,${.5-e} L${.5+i},${.5-e} L${.5+i},1`},"\u2558":{1:(i,e)=>`M.5,0 L.5,${.5+e} L1,${.5+e} M.5,${.5-e} L1,${.5-e}`},"\u2559":{1:(i,e)=>`M1,.5 L${.5-i},.5 L${.5-i},0 M${.5+i},.5 L${.5+i},0`},"\u255A":{1:(i,e)=>`M1,${.5-e} L${.5+i},${.5-e} L${.5+i},0 M1,${.5+e} L${.5-i},${.5+e} L${.5-i},0`},"\u255B":{1:(i,e)=>`M0,${.5+e} L.5,${.5+e} L.5,0 M0,${.5-e} L.5,${.5-e}`},"\u255C":{1:(i,e)=>`M0,.5 L${.5+i},.5 L${.5+i},0 M${.5-i},.5 L${.5-i},0`},"\u255D":{1:(i,e)=>`M0,${.5-e} L${.5-i},${.5-e} L${.5-i},0 M0,${.5+e} L${.5+i},${.5+e} L${.5+i},0`},"\u255E":{1:(i,e)=>`M.5,0 L.5,1 M.5,${.5-e} L1,${.5-e} M.5,${.5+e} L1,${.5+e}`},"\u255F":{1:(i,e)=>`M${.5-i},0 L${.5-i},1 M${.5+i},0 L${.5+i},1 M${.5+i},.5 L1,.5`},"\u2560":{1:(i,e)=>`M${.5-i},0 L${.5-i},1 M1,${.5+e} L${.5+i},${.5+e} L${.5+i},1 M1,${.5-e} L${.5+i},${.5-e} L${.5+i},0`},"\u2561":{1:(i,e)=>`M.5,0 L.5,1 M0,${.5-e} L.5,${.5-e} M0,${.5+e} L.5,${.5+e}`},"\u2562":{1:(i,e)=>`M0,.5 L${.5-i},.5 M${.5-i},0 L${.5-i},1 M${.5+i},0 L${.5+i},1`},"\u2563":{1:(i,e)=>`M${.5+i},0 L${.5+i},1 M0,${.5+e} L${.5-i},${.5+e} L${.5-i},1 M0,${.5-e} L${.5-i},${.5-e} L${.5-i},0`},"\u2564":{1:(i,e)=>`M0,${.5-e} L1,${.5-e} M0,${.5+e} L1,${.5+e} M.5,${.5+e} L.5,1`},"\u2565":{1:(i,e)=>`M0,.5 L1,.5 M${.5-i},.5 L${.5-i},1 M${.5+i},.5 L${.5+i},1`},"\u2566":{1:(i,e)=>`M0,${.5-e} L1,${.5-e} M0,${.5+e} L${.5-i},${.5+e} L${.5-i},1 M1,${.5+e} L${.5+i},${.5+e} L${.5+i},1`},"\u2567":{1:(i,e)=>`M.5,0 L.5,${.5-e} M0,${.5-e} L1,${.5-e} M0,${.5+e} L1,${.5+e}`},"\u2568":{1:(i,e)=>`M0,.5 L1,.5 M${.5-i},.5 L${.5-i},0 M${.5+i},.5 L${.5+i},0`},"\u2569":{1:(i,e)=>`M0,${.5+e} L1,${.5+e} M0,${.5-e} L${.5-i},${.5-e} L${.5-i},0 M1,${.5-e} L${.5+i},${.5-e} L${.5+i},0`},"\u256A":{1:(i,e)=>`M.5,0 L.5,1 M0,${.5-e} L1,${.5-e} M0,${.5+e} L1,${.5+e}`},"\u256B":{1:(i,e)=>`M0,.5 L1,.5 M${.5-i},0 L${.5-i},1 M${.5+i},0 L${.5+i},1`},"\u256C":{1:(i,e)=>`M0,${.5+e} L${.5-i},${.5+e} L${.5-i},1 M1,${.5+e} L${.5+i},${.5+e} L${.5+i},1 M0,${.5-e} L${.5-i},${.5-e} L${.5-i},0 M1,${.5-e} L${.5+i},${.5-e} L${.5+i},0`},"\u2571":{1:"M1,0 L0,1"},"\u2572":{1:"M0,0 L1,1"},"\u2573":{1:"M1,0 L0,1 M0,0 L1,1"},"\u257C":{1:"M.5,.5 L0,.5",3:"M.5,.5 L1,.5"},"\u257D":{1:"M.5,.5 L.5,0",3:"M.5,.5 L.5,1"},"\u257E":{1:"M.5,.5 L1,.5",3:"M.5,.5 L0,.5"},"\u257F":{1:"M.5,.5 L.5,1",3:"M.5,.5 L.5,0"},"\u250D":{1:"M.5,.5 L.5,1",3:"M.5,.5 L1,.5"},"\u250E":{1:"M.5,.5 L1,.5",3:"M.5,.5 L.5,1"},"\u2511":{1:"M.5,.5 L.5,1",3:"M.5,.5 L0,.5"},"\u2512":{1:"M.5,.5 L0,.5",3:"M.5,.5 L.5,1"},"\u2515":{1:"M.5,.5 L.5,0",3:"M.5,.5 L1,.5"},"\u2516":{1:"M.5,.5 L1,.5",3:"M.5,.5 L.5,0"},"\u2519":{1:"M.5,.5 L.5,0",3:"M.5,.5 L0,.5"},"\u251A":{1:"M.5,.5 L0,.5",3:"M.5,.5 L.5,0"},"\u251D":{1:"M.5,0 L.5,1",3:"M.5,.5 L1,.5"},"\u251E":{1:"M0.5,1 L.5,.5 L1,.5",3:"M.5,.5 L.5,0"},"\u251F":{1:"M.5,0 L.5,.5 L1,.5",3:"M.5,.5 L.5,1"},"\u2520":{1:"M.5,.5 L1,.5",3:"M.5,0 L.5,1"},"\u2521":{1:"M.5,.5 L.5,1",3:"M.5,0 L.5,.5 L1,.5"},"\u2522":{1:"M.5,.5 L.5,0",3:"M0.5,1 L.5,.5 L1,.5"},"\u2525":{1:"M.5,0 L.5,1",3:"M.5,.5 L0,.5"},"\u2526":{1:"M0,.5 L.5,.5 L.5,1",3:"M.5,.5 L.5,0"},"\u2527":{1:"M.5,0 L.5,.5 L0,.5",3:"M.5,.5 L.5,1"},"\u2528":{1:"M.5,.5 L0,.5",3:"M.5,0 L.5,1"},"\u2529":{1:"M.5,.5 L.5,1",3:"M.5,0 L.5,.5 L0,.5"},"\u252A":{1:"M.5,.5 L.5,0",3:"M0,.5 L.5,.5 L.5,1"},"\u252D":{1:"M0.5,1 L.5,.5 L1,.5",3:"M.5,.5 L0,.5"},"\u252E":{1:"M0,.5 L.5,.5 L.5,1",3:"M.5,.5 L1,.5"},"\u252F":{1:"M.5,.5 L.5,1",3:"M0,.5 L1,.5"},"\u2530":{1:"M0,.5 L1,.5",3:"M.5,.5 L.5,1"},"\u2531":{1:"M.5,.5 L1,.5",3:"M0,.5 L.5,.5 L.5,1"},"\u2532":{1:"M.5,.5 L0,.5",3:"M0.5,1 L.5,.5 L1,.5"},"\u2535":{1:"M.5,0 L.5,.5 L1,.5",3:"M.5,.5 L0,.5"},"\u2536":{1:"M.5,0 L.5,.5 L0,.5",3:"M.5,.5 L1,.5"},"\u2537":{1:"M.5,.5 L.5,0",3:"M0,.5 L1,.5"},"\u2538":{1:"M0,.5 L1,.5",3:"M.5,.5 L.5,0"},"\u2539":{1:"M.5,.5 L1,.5",3:"M.5,0 L.5,.5 L0,.5"},"\u253A":{1:"M.5,.5 L0,.5",3:"M.5,0 L.5,.5 L1,.5"},"\u253D":{1:"M.5,0 L.5,1 M.5,.5 L1,.5",3:"M.5,.5 L0,.5"},"\u253E":{1:"M.5,0 L.5,1 M.5,.5 L0,.5",3:"M.5,.5 L1,.5"},"\u253F":{1:"M.5,0 L.5,1",3:"M0,.5 L1,.5"},"\u2540":{1:"M0,.5 L1,.5 M.5,.5 L.5,1",3:"M.5,.5 L.5,0"},"\u2541":{1:"M.5,.5 L.5,0 M0,.5 L1,.5",3:"M.5,.5 L.5,1"},"\u2542":{1:"M0,.5 L1,.5",3:"M.5,0 L.5,1"},"\u2543":{1:"M0.5,1 L.5,.5 L1,.5",3:"M.5,0 L.5,.5 L0,.5"},"\u2544":{1:"M0,.5 L.5,.5 L.5,1",3:"M.5,0 L.5,.5 L1,.5"},"\u2545":{1:"M.5,0 L.5,.5 L1,.5",3:"M0,.5 L.5,.5 L.5,1"},"\u2546":{1:"M.5,0 L.5,.5 L0,.5",3:"M0.5,1 L.5,.5 L1,.5"},"\u2547":{1:"M.5,.5 L.5,1",3:"M.5,.5 L.5,0 M0,.5 L1,.5"},"\u2548":{1:"M.5,.5 L.5,0",3:"M0,.5 L1,.5 M.5,.5 L.5,1"},"\u2549":{1:"M.5,.5 L1,.5",3:"M.5,0 L.5,1 M.5,.5 L0,.5"},"\u254A":{1:"M.5,.5 L0,.5",3:"M.5,0 L.5,1 M.5,.5 L1,.5"},"\u254C":{1:"M.1,.5 L.4,.5 M.6,.5 L.9,.5"},"\u254D":{3:"M.1,.5 L.4,.5 M.6,.5 L.9,.5"},"\u2504":{1:"M.0667,.5 L.2667,.5 M.4,.5 L.6,.5 M.7333,.5 L.9333,.5"},"\u2505":{3:"M.0667,.5 L.2667,.5 M.4,.5 L.6,.5 M.7333,.5 L.9333,.5"},"\u2508":{1:"M.05,.5 L.2,.5 M.3,.5 L.45,.5 M.55,.5 L.7,.5 M.8,.5 L.95,.5"},"\u2509":{3:"M.05,.5 L.2,.5 M.3,.5 L.45,.5 M.55,.5 L.7,.5 M.8,.5 L.95,.5"},"\u254E":{1:"M.5,.1 L.5,.4 M.5,.6 L.5,.9"},"\u254F":{3:"M.5,.1 L.5,.4 M.5,.6 L.5,.9"},"\u2506":{1:"M.5,.0667 L.5,.2667 M.5,.4 L.5,.6 M.5,.7333 L.5,.9333"},"\u2507":{3:"M.5,.0667 L.5,.2667 M.5,.4 L.5,.6 M.5,.7333 L.5,.9333"},"\u250A":{1:"M.5,.05 L.5,.2 M.5,.3 L.5,.45 L.5,.55 M.5,.7 L.5,.95"},"\u250B":{3:"M.5,.05 L.5,.2 M.5,.3 L.5,.45 L.5,.55 M.5,.7 L.5,.95"},"\u256D":{1:(i,e)=>`M.5,1 L.5,${.5+e/.15*.5} C.5,${.5+e/.15*.5},.5,.5,1,.5`},"\u256E":{1:(i,e)=>`M.5,1 L.5,${.5+e/.15*.5} C.5,${.5+e/.15*.5},.5,.5,0,.5`},"\u256F":{1:(i,e)=>`M.5,0 L.5,${.5-e/.15*.5} C.5,${.5-e/.15*.5},.5,.5,0,.5`},"\u2570":{1:(i,e)=>`M.5,0 L.5,${.5-e/.15*.5} C.5,${.5-e/.15*.5},.5,.5,1,.5`}};var et={"\uE0A0":{d:"M.3,1 L.03,1 L.03,.88 C.03,.82,.06,.78,.11,.73 C.15,.7,.2,.68,.28,.65 L.43,.6 C.49,.58,.53,.56,.56,.53 C.59,.5,.6,.47,.6,.43 L.6,.27 L.4,.27 L.69,.1 L.98,.27 L.78,.27 L.78,.46 C.78,.52,.76,.56,.72,.61 C.68,.66,.63,.67,.56,.7 L.48,.72 C.42,.74,.38,.76,.35,.78 C.32,.8,.31,.84,.31,.88 L.31,1 M.3,.5 L.03,.59 L.03,.09 L.3,.09 L.3,.655",type:0},"\uE0A1":{d:"M.7,.4 L.7,.47 L.2,.47 L.2,.03 L.355,.03 L.355,.4 L.705,.4 M.7,.5 L.86,.5 L.86,.95 L.69,.95 L.44,.66 L.46,.86 L.46,.95 L.3,.95 L.3,.49 L.46,.49 L.71,.78 L.69,.565 L.69,.5",type:0},"\uE0A2":{d:"M.25,.94 C.16,.94,.11,.92,.11,.87 L.11,.53 C.11,.48,.15,.455,.23,.45 L.23,.3 C.23,.25,.26,.22,.31,.19 C.36,.16,.43,.15,.51,.15 C.59,.15,.66,.16,.71,.19 C.77,.22,.79,.26,.79,.3 L.79,.45 C.87,.45,.91,.48,.91,.53 L.91,.87 C.91,.92,.86,.94,.77,.94 L.24,.94 M.53,.2 C.49,.2,.45,.21,.42,.23 C.39,.25,.38,.27,.38,.3 L.38,.45 L.68,.45 L.68,.3 C.68,.27,.67,.25,.64,.23 C.61,.21,.58,.2,.53,.2 M.58,.82 L.58,.66 C.63,.65,.65,.63,.65,.6 C.65,.58,.64,.57,.61,.56 C.58,.55,.56,.54,.52,.54 C.48,.54,.46,.55,.43,.56 C.4,.57,.39,.59,.39,.6 C.39,.63,.41,.64,.46,.66 L.46,.82 L.57,.82",type:0},"\uE0B0":{d:"M0,0 L1,.5 L0,1",type:0,rightPadding:2},"\uE0B1":{d:"M-1,-.5 L1,.5 L-1,1.5",type:1,leftPadding:1,rightPadding:1},"\uE0B2":{d:"M1,0 L0,.5 L1,1",type:0,leftPadding:2},"\uE0B3":{d:"M2,-.5 L0,.5 L2,1.5",type:1,leftPadding:1,rightPadding:1},"\uE0B4":{d:"M0,0 L0,1 C0.552,1,1,0.776,1,.5 C1,0.224,0.552,0,0,0",type:0,rightPadding:1},"\uE0B5":{d:"M.2,1 C.422,1,.8,.826,.78,.5 C.8,.174,0.422,0,.2,0",type:1,rightPadding:1},"\uE0B6":{d:"M1,0 L1,1 C0.448,1,0,0.776,0,.5 C0,0.224,0.448,0,1,0",type:0,leftPadding:1},"\uE0B7":{d:"M.8,1 C0.578,1,0.2,.826,.22,.5 C0.2,0.174,0.578,0,0.8,0",type:1,leftPadding:1},"\uE0B8":{d:"M-.5,-.5 L1.5,1.5 L-.5,1.5",type:0},"\uE0B9":{d:"M-.5,-.5 L1.5,1.5",type:1,leftPadding:1,rightPadding:1},"\uE0BA":{d:"M1.5,-.5 L-.5,1.5 L1.5,1.5",type:0},"\uE0BC":{d:"M1.5,-.5 L-.5,1.5 L-.5,-.5",type:0},"\uE0BD":{d:"M1.5,-.5 L-.5,1.5",type:1,leftPadding:1,rightPadding:1},"\uE0BE":{d:"M-.5,-.5 L1.5,1.5 L1.5,-.5",type:0}};et["\uE0BB"]=et["\uE0BD"];et["\uE0BF"]=et["\uE0B9"];function yn(i,e,t,n,s,o,r,a){let l=Hr[e];if(l)return $r(i,l,t,n,s,o),!0;let u=Wr[e];if(u)return Kr(i,u,t,n,s,o),!0;let c=Gr[e];if(c)return Vr(i,c,t,n,s,o,a),!0;let d=et[e];return d?(Cr(i,d,t,n,s,o,r,a),!0):!1}function $r(i,e,t,n,s,o){for(let r=0;r<e.length;r++){let a=e[r],l=s/8,u=o/8;i.fillRect(t+a.x*l,n+a.y*u,a.w*l,a.h*u)}}var xn=new Map;function Kr(i,e,t,n,s,o){let r=xn.get(e);r||(r=new Map,xn.set(e,r));let a=i.fillStyle;if(typeof a!="string")throw new Error(`Unexpected fillStyle type "${a}"`);let l=r.get(a);if(!l){let u=e[0].length,c=e.length,d=i.canvas.ownerDocument.createElement("canvas");d.width=u,d.height=c;let h=F(d.getContext("2d")),f=new ImageData(u,c),I,L,M,q;if(a.startsWith("#"))I=parseInt(a.slice(1,3),16),L=parseInt(a.slice(3,5),16),M=parseInt(a.slice(5,7),16),q=a.length>7&&parseInt(a.slice(7,9),16)||1;else if(a.startsWith("rgba"))[I,L,M,q]=a.substring(5,a.length-1).split(",").map(S=>parseFloat(S));else throw new Error(`Unexpected fillStyle color format "${a}" when drawing pattern glyph`);for(let S=0;S<c;S++)for(let W=0;W<u;W++)f.data[(S*u+W)*4]=I,f.data[(S*u+W)*4+1]=L,f.data[(S*u+W)*4+2]=M,f.data[(S*u+W)*4+3]=e[S][W]*(q*255);h.putImageData(f,0,0),l=F(i.createPattern(d,null)),r.set(a,l)}i.fillStyle=l,i.fillRect(t,n,s,o)}function Vr(i,e,t,n,s,o,r){i.strokeStyle=i.fillStyle;for(let[a,l]of Object.entries(e)){i.beginPath(),i.lineWidth=r*Number.parseInt(a);let u;if(typeof l=="function"){let d=.15/o*s;u=l(.15,d)}else u=l;for(let c of u.split(" ")){let d=c[0],h=In[d];if(!h){console.error(`Could not find drawing instructions for "${d}"`);continue}let f=c.substring(1).split(",");!f[0]||!f[1]||h(i,Ln(f,s,o,t,n,!0,r))}i.stroke(),i.closePath()}}function Cr(i,e,t,n,s,o,r,a){let l=new Path2D;l.rect(t,n,s,o),i.clip(l),i.beginPath();let u=r/12;i.lineWidth=a*u;for(let c of e.d.split(" ")){let d=c[0],h=In[d];if(!h){console.error(`Could not find drawing instructions for "${d}"`);continue}let f=c.substring(1).split(",");!f[0]||!f[1]||h(i,Ln(f,s,o,t,n,!1,a,(e.leftPadding??0)*(u/2),(e.rightPadding??0)*(u/2)))}e.type===1?(i.strokeStyle=i.fillStyle,i.stroke()):i.fill(),i.closePath()}function En(i,e,t=0){return Math.max(Math.min(i,e),t)}var In={C:(i,e)=>i.bezierCurveTo(e[0],e[1],e[2],e[3],e[4],e[5]),L:(i,e)=>i.lineTo(e[0],e[1]),M:(i,e)=>i.moveTo(e[0],e[1])};function Ln(i,e,t,n,s,o,r,a=0,l=0){let u=i.map(c=>parseFloat(c)||parseInt(c));if(u.length<2)throw new Error("Too few arguments for instruction");for(let c=0;c<u.length;c+=2)u[c]*=e-a*r-l*r,o&&u[c]!==0&&(u[c]=En(Math.round(u[c]+.5)-.5,e,0)),u[c]+=n+a*r;for(let c=1;c<u.length;c+=2)u[c]*=t,o&&u[c]!==0&&(u[c]=En(Math.round(u[c]+.5)-.5,t,0)),u[c]+=s;return u}var Ot=class{constructor(){this._data={}}set(e,t,n){this._data[e]||(this._data[e]={}),this._data[e][t]=n}get(e,t){return this._data[e]?this._data[e][t]:void 0}clear(){this._data={}}},tt=class{constructor(){this._data=new Ot}set(e,t,n,s,o){this._data.get(e,t)||this._data.set(e,t,new Ot),this._data.get(e,t).set(n,s,o)}get(e,t,n,s){return this._data.get(e,t)?.get(n,s)}clear(){this._data.clear()}};var Ft=class{constructor(){this._tasks=[];this._i=0}enqueue(e){this._tasks.push(e),this._start()}flush(){for(;this._i<this._tasks.length;)this._tasks[this._i]()||this._i++;this.clear()}clear(){this._idleCallback&&(this._cancelCallback(this._idleCallback),this._idleCallback=void 0),this._i=0,this._tasks.length=0}_start(){this._idleCallback||(this._idleCallback=this._requestCallback(this._process.bind(this)))}_process(e){this._idleCallback=void 0;let t=0,n=0,s=e.timeRemaining(),o=0;for(;this._i<this._tasks.length;){if(t=Date.now(),this._tasks[this._i]()||this._i++,t=Math.max(1,Date.now()-t),n=Math.max(t,n),o=e.timeRemaining(),n*1.5>o){s-t<-20&&console.warn(`task queue exceeded allotted deadline by ${Math.abs(Math.round(s-t))}ms`),this._start();return}s=o}this.clear()}},gi=class extends Ft{_requestCallback(e){return setTimeout(()=>e(this._createDeadline(16)))}_cancelCallback(e){clearTimeout(e)}_createDeadline(e){let t=Date.now()+e;return{timeRemaining:()=>Math.max(0,t-Date.now())}}},xi=class extends Ft{_requestCallback(e){return requestIdleCallback(e)}_cancelCallback(e){cancelIdleCallback(e)}},wn=!Lt&&"requestIdleCallback"in window?xi:gi;var he=class i{constructor(){this.fg=0;this.bg=0;this.extended=new it}static toColorRGB(e){return[e>>>16&255,e>>>8&255,e&255]}static fromColorRGB(e){return(e[0]&255)<<16|(e[1]&255)<<8|e[2]&255}clone(){let e=new i;return e.fg=this.fg,e.bg=this.bg,e.extended=this.extended.clone(),e}isInverse(){return this.fg&67108864}isBold(){return this.fg&134217728}isUnderline(){return this.hasExtendedAttrs()&&this.extended.underlineStyle!==0?1:this.fg&268435456}isBlink(){return this.fg&536870912}isInvisible(){return this.fg&1073741824}isItalic(){return this.bg&67108864}isDim(){return this.bg&134217728}isStrikethrough(){return this.fg&2147483648}isProtected(){return this.bg&536870912}isOverline(){return this.bg&1073741824}getFgColorMode(){return this.fg&50331648}getBgColorMode(){return this.bg&50331648}isFgRGB(){return(this.fg&50331648)===50331648}isBgRGB(){return(this.bg&50331648)===50331648}isFgPalette(){return(this.fg&50331648)===16777216||(this.fg&50331648)===33554432}isBgPalette(){return(this.bg&50331648)===16777216||(this.bg&50331648)===33554432}isFgDefault(){return(this.fg&50331648)===0}isBgDefault(){return(this.bg&50331648)===0}isAttributeDefault(){return this.fg===0&&this.bg===0}getFgColor(){switch(this.fg&50331648){case 16777216:case 33554432:return this.fg&255;case 50331648:return this.fg&16777215;default:return-1}}getBgColor(){switch(this.bg&50331648){case 16777216:case 33554432:return this.bg&255;case 50331648:return this.bg&16777215;default:return-1}}hasExtendedAttrs(){return this.bg&268435456}updateExtended(){this.extended.isEmpty()?this.bg&=-268435457:this.bg|=268435456}getUnderlineColor(){if(this.bg&268435456&&~this.extended.underlineColor)switch(this.extended.underlineColor&50331648){case 16777216:case 33554432:return this.extended.underlineColor&255;case 50331648:return this.extended.underlineColor&16777215;default:return this.getFgColor()}return this.getFgColor()}getUnderlineColorMode(){return this.bg&268435456&&~this.extended.underlineColor?this.extended.underlineColor&50331648:this.getFgColorMode()}isUnderlineColorRGB(){return this.bg&268435456&&~this.extended.underlineColor?(this.extended.underlineColor&50331648)===50331648:this.isFgRGB()}isUnderlineColorPalette(){return this.bg&268435456&&~this.extended.underlineColor?(this.extended.underlineColor&50331648)===16777216||(this.extended.underlineColor&50331648)===33554432:this.isFgPalette()}isUnderlineColorDefault(){return this.bg&268435456&&~this.extended.underlineColor?(this.extended.underlineColor&50331648)===0:this.isFgDefault()}getUnderlineStyle(){return this.fg&268435456?this.bg&268435456?this.extended.underlineStyle:1:0}getUnderlineVariantOffset(){return this.extended.underlineVariantOffset}},it=class i{constructor(e=0,t=0){this._ext=0;this._urlId=0;this._ext=e,this._urlId=t}get ext(){return this._urlId?this._ext&-469762049|this.underlineStyle<<26:this._ext}set ext(e){this._ext=e}get underlineStyle(){return this._urlId?5:(this._ext&469762048)>>26}set underlineStyle(e){this._ext&=-469762049,this._ext|=e<<26&469762048}get underlineColor(){return this._ext&67108863}set underlineColor(e){this._ext&=-67108864,this._ext|=e&67108863}get urlId(){return this._urlId}set urlId(e){this._urlId=e}get underlineVariantOffset(){let e=(this._ext&3758096384)>>29;return e<0?e^4294967288:e}set underlineVariantOffset(e){this._ext&=536870911,this._ext|=e<<29&3758096384}clone(){return new i(this._ext,this._urlId)}isEmpty(){return this.underlineStyle===0&&this._urlId===0}};var He=class He{constructor(e){this.element=e,this.next=He.Undefined,this.prev=He.Undefined}};He.Undefined=new He(void 0);var Rn=He;var zr=globalThis.performance&&typeof globalThis.performance.now=="function",kt=class i{static create(e){return new i(e)}constructor(e){this._now=zr&&e===!1?Date.now:globalThis.performance.now.bind(globalThis.performance),this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}reset(){this._startTime=this._now(),this._stopTime=-1}elapsed(){return this._stopTime!==-1?this._stopTime-this._startTime:this._now()-this._startTime}};var qr=!1,Dn=!1,jr=!1,ee;(se=>{se.None=()=>B.None;function e(T){if(jr){let{onDidAddListener:p}=T,g=nt.create(),b=0;T.onDidAddListener=()=>{++b===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),g.print()),p?.()}}}function t(T,p){return h(T,()=>{},0,void 0,!0,void 0,p)}se.defer=t;function n(T){return(p,g=null,b)=>{let m=!1,_;return _=T(v=>{if(!m)return _?_.dispose():m=!0,p.call(g,v)},null,b),m&&_.dispose(),_}}se.once=n;function s(T,p,g){return c((b,m=null,_)=>T(v=>b.call(m,p(v)),null,_),g)}se.map=s;function o(T,p,g){return c((b,m=null,_)=>T(v=>{p(v),b.call(m,v)},null,_),g)}se.forEach=o;function r(T,p,g){return c((b,m=null,_)=>T(v=>p(v)&&b.call(m,v),null,_),g)}se.filter=r;function a(T){return T}se.signal=a;function l(...T){return(p,g=null,b)=>{let m=It(...T.map(_=>_(v=>p.call(g,v))));return d(m,b)}}se.any=l;function u(T,p,g,b){let m=g;return s(T,_=>(m=p(m,_),m),b)}se.reduce=u;function c(T,p){let g,b={onWillAddFirstListener(){g=T(m.fire,m)},onDidRemoveLastListener(){g?.dispose()}};p||e(b);let m=new D(b);return p?.add(m),m.event}function d(T,p){return p instanceof Array?p.push(T):p&&p.add(T),T}function h(T,p,g=100,b=!1,m=!1,_,v){let x,R,$,P=0,de,Re={leakWarningThreshold:_,onWillAddFirstListener(){x=T(ie=>{P++,R=p(R,ie),b&&!$&&(oe.fire(R),R=void 0),de=()=>{let N=R;R=void 0,$=void 0,(!b||P>1)&&oe.fire(N),P=0},typeof g=="number"?(clearTimeout($),$=setTimeout(de,g)):$===void 0&&($=0,queueMicrotask(de))})},onWillRemoveListener(){m&&P>0&&de?.()},onDidRemoveLastListener(){de=void 0,x.dispose()}};v||e(Re);let oe=new D(Re);return v?.add(oe),oe.event}se.debounce=h;function f(T,p=0,g){return se.debounce(T,(b,m)=>b?(b.push(m),b):[m],p,void 0,!0,void 0,g)}se.accumulate=f;function I(T,p=(b,m)=>b===m,g){let b=!0,m;return r(T,_=>{let v=b||!p(_,m);return b=!1,m=_,v},g)}se.latch=I;function L(T,p,g){return[se.filter(T,p,g),se.filter(T,b=>!p(b),g)]}se.split=L;function M(T,p=!1,g=[],b){let m=g.slice(),_=T(R=>{m?m.push(R):x.fire(R)});b&&b.add(_);let v=()=>{m?.forEach(R=>x.fire(R)),m=null},x=new D({onWillAddFirstListener(){_||(_=T(R=>x.fire(R)),b&&b.add(_))},onDidAddFirstListener(){m&&(p?setTimeout(v):v())},onDidRemoveLastListener(){_&&_.dispose(),_=null}});return b&&b.add(x),x.event}se.buffer=M;function q(T,p){return(b,m,_)=>{let v=p(new W);return T(function(x){let R=v.evaluate(x);R!==S&&b.call(m,R)},void 0,_)}}se.chain=q;let S=Symbol("HaltChainable");class W{constructor(){this.steps=[]}map(p){return this.steps.push(p),this}forEach(p){return this.steps.push(g=>(p(g),g)),this}filter(p){return this.steps.push(g=>p(g)?g:S),this}reduce(p,g){let b=g;return this.steps.push(m=>(b=p(b,m),b)),this}latch(p=(g,b)=>g===b){let g=!0,b;return this.steps.push(m=>{let _=g||!p(m,b);return g=!1,b=m,_?m:S}),this}evaluate(p){for(let g of this.steps)if(p=g(p),p===S)break;return p}}function E(T,p,g=b=>b){let b=(...x)=>v.fire(g(...x)),m=()=>T.on(p,b),_=()=>T.removeListener(p,b),v=new D({onWillAddFirstListener:m,onDidRemoveLastListener:_});return v.event}se.fromNodeEventEmitter=E;function y(T,p,g=b=>b){let b=(...x)=>v.fire(g(...x)),m=()=>T.addEventListener(p,b),_=()=>T.removeEventListener(p,b),v=new D({onWillAddFirstListener:m,onDidRemoveLastListener:_});return v.event}se.fromDOMEventEmitter=y;function w(T){return new Promise(p=>n(T)(p))}se.toPromise=w;function G(T){let p=new D;return T.then(g=>{p.fire(g)},()=>{p.fire(void 0)}).finally(()=>{p.dispose()}),p.event}se.fromPromise=G;function ue(T,p){return T(g=>p.fire(g))}se.forward=ue;function Se(T,p,g){return p(g),T(b=>p(b))}se.runAndSubscribe=Se;class ce{constructor(p,g){this._observable=p;this._counter=0;this._hasChanged=!1;let b={onWillAddFirstListener:()=>{p.addObserver(this)},onDidRemoveLastListener:()=>{p.removeObserver(this)}};g||e(b),this.emitter=new D(b),g&&g.add(this.emitter)}beginUpdate(p){this._counter++}handlePossibleChange(p){}handleChange(p,g){this._hasChanged=!0}endUpdate(p){this._counter--,this._counter===0&&(this._observable.reportChanges(),this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this._observable.get())))}}function we(T,p){return new ce(T,p).emitter.event}se.fromObservable=we;function A(T){return(p,g,b)=>{let m=0,_=!1,v={beginUpdate(){m++},endUpdate(){m--,m===0&&(T.reportChanges(),_&&(_=!1,p.call(g)))},handlePossibleChange(){},handleChange(){_=!0}};T.addObserver(v),T.reportChanges();let x={dispose(){T.removeObserver(v)}};return b instanceof fe?b.add(x):Array.isArray(b)&&b.push(x),x}}se.fromObservableLight=A})(ee||={});var We=class We{constructor(e){this.listenerCount=0;this.invocationCount=0;this.elapsedOverall=0;this.durations=[];this.name=`${e}_${We._idPool++}`,We.all.add(this)}start(e){this._stopWatch=new kt,this.listenerCount=e}stop(){if(this._stopWatch){let e=this._stopWatch.elapsed();this.durations.push(e),this.elapsedOverall+=e,this.invocationCount+=1,this._stopWatch=void 0}}};We.all=new Set,We._idPool=0;var Ei=We,Mn=-1;var Bt=class Bt{constructor(e,t,n=(Bt._idPool++).toString(16).padStart(3,"0")){this._errorHandler=e;this.threshold=t;this.name=n;this._warnCountdown=0}dispose(){this._stacks?.clear()}check(e,t){let n=this.threshold;if(n<=0||t<n)return;this._stacks||(this._stacks=new Map);let s=this._stacks.get(e.value)||0;if(this._stacks.set(e.value,s+1),this._warnCountdown-=1,this._warnCountdown<=0){this._warnCountdown=n*.5;let[o,r]=this.getMostFrequentStack(),a=`[${this.name}] potential listener LEAK detected, having ${t} listeners already. MOST frequent listener (${r}):`;console.warn(a),console.warn(o);let l=new Ii(a,o);this._errorHandler(l)}return()=>{let o=this._stacks.get(e.value)||0;this._stacks.set(e.value,o-1)}}getMostFrequentStack(){if(!this._stacks)return;let e,t=0;for(let[n,s]of this._stacks)(!e||t<s)&&(e=[n,s],t=s);return e}};Bt._idPool=1;var yi=Bt,nt=class i{constructor(e){this.value=e}static create(){let e=new Error;return new i(e.stack??"")}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},Ii=class extends Error{constructor(e,t){super(e),this.name="ListenerLeakError",this.stack=t}},Li=class extends Error{constructor(e,t){super(e),this.name="ListenerRefusalError",this.stack=t}},Xr=0,Ge=class{constructor(e){this.value=e;this.id=Xr++}},Yr=2,Qr=(i,e)=>{if(i instanceof Ge)e(i);else for(let t=0;t<i.length;t++){let n=i[t];n&&e(n)}},Pt;if(qr){let i=[];setInterval(()=>{i.length!==0&&(console.warn("[LEAKING LISTENERS] GC'ed these listeners that were NOT yet disposed:"),console.warn(i.join(`
`)),i.length=0)},3e3),Pt=new FinalizationRegistry(e=>{typeof e=="string"&&i.push(e)})}var D=class{constructor(e){this._size=0;this._options=e,this._leakageMon=Mn>0||this._options?.leakWarningThreshold?new yi(e?.onListenerError??Pe,this._options?.leakWarningThreshold??Mn):void 0,this._perfMon=this._options?._profName?new Ei(this._options._profName):void 0,this._deliveryQueue=this._options?.deliveryQueue}dispose(){if(!this._disposed){if(this._disposed=!0,this._deliveryQueue?.current===this&&this._deliveryQueue.reset(),this._listeners){if(Dn){let e=this._listeners;queueMicrotask(()=>{Qr(e,t=>t.stack?.print())})}this._listeners=void 0,this._size=0}this._options?.onDidRemoveLastListener?.(),this._leakageMon?.dispose()}}get event(){return this._event??=(e,t,n)=>{if(this._leakageMon&&this._size>this._leakageMon.threshold**2){let l=`[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this._size} vs ${this._leakageMon.threshold})`;console.warn(l);let u=this._leakageMon.getMostFrequentStack()??["UNKNOWN stack",-1],c=new Li(`${l}. HINT: Stack shows most frequent listener (${u[1]}-times)`,u[0]);return(this._options?.onListenerError||Pe)(c),B.None}if(this._disposed)return B.None;t&&(e=e.bind(t));let s=new Ge(e),o,r;this._leakageMon&&this._size>=Math.ceil(this._leakageMon.threshold*.2)&&(s.stack=nt.create(),o=this._leakageMon.check(s.stack,this._size+1)),Dn&&(s.stack=r??nt.create()),this._listeners?this._listeners instanceof Ge?(this._deliveryQueue??=new wi,this._listeners=[this._listeners,s]):this._listeners.push(s):(this._options?.onWillAddFirstListener?.(this),this._listeners=s,this._options?.onDidAddFirstListener?.(this)),this._size++;let a=O(()=>{Pt?.unregister(a),o?.(),this._removeListener(s)});if(n instanceof fe?n.add(a):Array.isArray(n)&&n.push(a),Pt){let l=new Error().stack.split(`
`).slice(2,3).join(`
`).trim(),u=/(file:|vscode-file:\/\/vscode-app)?(\/[^:]*:\d+:\d+)/.exec(l);Pt.register(a,u?.[2]??l,a)}return a},this._event}_removeListener(e){if(this._options?.onWillRemoveListener?.(this),!this._listeners)return;if(this._size===1){this._listeners=void 0,this._options?.onDidRemoveLastListener?.(this),this._size=0;return}let t=this._listeners,n=t.indexOf(e);if(n===-1)throw console.log("disposed?",this._disposed),console.log("size?",this._size),console.log("arr?",JSON.stringify(this._listeners)),new Error("Attempted to dispose unknown listener");this._size--,t[n]=void 0;let s=this._deliveryQueue.current===this;if(this._size*Yr<=t.length){let o=0;for(let r=0;r<t.length;r++)t[r]?t[o++]=t[r]:s&&(this._deliveryQueue.end--,o<this._deliveryQueue.i&&this._deliveryQueue.i--);t.length=o}}_deliver(e,t){if(!e)return;let n=this._options?.onListenerError||Pe;if(!n){e.value(t);return}try{e.value(t)}catch(s){n(s)}}_deliverQueue(e){let t=e.current._listeners;for(;e.i<e.end;)this._deliver(t[e.i++],e.value);e.reset()}fire(e){if(this._deliveryQueue?.current&&(this._deliverQueue(this._deliveryQueue),this._perfMon?.stop()),this._perfMon?.start(this._size),this._listeners)if(this._listeners instanceof Ge)this._deliver(this._listeners,e);else{let t=this._deliveryQueue;t.enqueue(this,e,this._listeners.length),this._deliverQueue(t)}this._perfMon?.stop()}hasListeners(){return this._size>0}};var wi=class{constructor(){this.i=-1;this.end=0}enqueue(e,t,n){this.i=0,this.end=n,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}};var An={texturePage:0,texturePosition:{x:0,y:0},texturePositionClipSpace:{x:0,y:0},offset:{x:0,y:0},size:{x:0,y:0},sizeClipSpace:{x:0,y:0}},rt=2;var st,ae=class i{constructor(e,t,n){this._document=e;this._config=t;this._unicodeService=n;this._didWarmUp=!1;this._cacheMap=new tt;this._cacheMapCombined=new tt;this._pages=[];this._activePages=[];this._workBoundingBox={top:0,left:0,bottom:0,right:0};this._workAttributeData=new he;this._textureSize=512;this._onAddTextureAtlasCanvas=new D;this.onAddTextureAtlasCanvas=this._onAddTextureAtlasCanvas.event;this._onRemoveTextureAtlasCanvas=new D;this.onRemoveTextureAtlasCanvas=this._onRemoveTextureAtlasCanvas.event;this._requestClearModel=!1;this._createNewPage(),this._tmpCanvas=Sn(e,this._config.deviceCellWidth*4+rt*2,this._config.deviceCellHeight+rt*2),this._tmpCtx=F(this._tmpCanvas.getContext("2d",{alpha:this._config.allowTransparency,willReadFrequently:!0}))}get pages(){return this._pages}dispose(){this._tmpCanvas.remove();for(let e of this.pages)e.canvas.remove();this._onAddTextureAtlasCanvas.dispose()}warmUp(){this._didWarmUp||(this._doWarmUp(),this._didWarmUp=!0)}_doWarmUp(){let e=new wn;for(let t=33;t<126;t++)e.enqueue(()=>{if(!this._cacheMap.get(t,0,0,0)){let n=this._drawToCache(t,0,0,0,!1,void 0);this._cacheMap.set(t,0,0,0,n)}})}beginFrame(){return this._requestClearModel}clearTexture(){if(!(this._pages[0].currentRow.x===0&&this._pages[0].currentRow.y===0)){for(let e of this._pages)e.clear();this._cacheMap.clear(),this._cacheMapCombined.clear(),this._didWarmUp=!1}}_createNewPage(){if(i.maxAtlasPages&&this._pages.length>=Math.max(4,i.maxAtlasPages)){let t=this._pages.filter(u=>u.canvas.width*2<=(i.maxTextureSize||4096)).sort((u,c)=>c.canvas.width!==u.canvas.width?c.canvas.width-u.canvas.width:c.percentageUsed-u.percentageUsed),n=-1,s=0;for(let u=0;u<t.length;u++)if(t[u].canvas.width!==s)n=u,s=t[u].canvas.width;else if(u-n===3)break;let o=t.slice(n,n+4),r=o.map(u=>u.glyphs[0].texturePage).sort((u,c)=>u>c?1:-1),a=this.pages.length-o.length,l=this._mergePages(o,a);l.version++;for(let u=r.length-1;u>=0;u--)this._deletePage(r[u]);this.pages.push(l),this._requestClearModel=!0,this._onAddTextureAtlasCanvas.fire(l.canvas)}let e=new ot(this._document,this._textureSize);return this._pages.push(e),this._activePages.push(e),this._onAddTextureAtlasCanvas.fire(e.canvas),e}_mergePages(e,t){let n=e[0].canvas.width*2,s=new ot(this._document,n,e);for(let[o,r]of e.entries()){let a=o*r.canvas.width%n,l=Math.floor(o/2)*r.canvas.height;s.ctx.drawImage(r.canvas,a,l);for(let c of r.glyphs)c.texturePage=t,c.sizeClipSpace.x=c.size.x/n,c.sizeClipSpace.y=c.size.y/n,c.texturePosition.x+=a,c.texturePosition.y+=l,c.texturePositionClipSpace.x=c.texturePosition.x/n,c.texturePositionClipSpace.y=c.texturePosition.y/n;this._onRemoveTextureAtlasCanvas.fire(r.canvas);let u=this._activePages.indexOf(r);u!==-1&&this._activePages.splice(u,1)}return s}_deletePage(e){this._pages.splice(e,1);for(let t=e;t<this._pages.length;t++){let n=this._pages[t];for(let s of n.glyphs)s.texturePage--;n.version++}}getRasterizedGlyphCombinedChar(e,t,n,s,o,r){return this._getFromCacheMap(this._cacheMapCombined,e,t,n,s,o,r)}getRasterizedGlyph(e,t,n,s,o,r){return this._getFromCacheMap(this._cacheMap,e,t,n,s,o,r)}_getFromCacheMap(e,t,n,s,o,r,a){return st=e.get(t,n,s,o),st||(st=this._drawToCache(t,n,s,o,r,a),e.set(t,n,s,o,st)),st}_getColorFromAnsiIndex(e){if(e>=this._config.colors.ansi.length)throw new Error("No color found for idx "+e);return this._config.colors.ansi[e]}_getBackgroundColor(e,t,n,s){if(this._config.allowTransparency)return Z;let o;switch(e){case 16777216:case 33554432:o=this._getColorFromAnsiIndex(t);break;case 50331648:let r=he.toColorRGB(t);o=X.toColor(r[0],r[1],r[2]);break;case 0:default:n?o=Ue.opaque(this._config.colors.foreground):o=this._config.colors.background;break}return this._config.allowTransparency||(o=Ue.opaque(o)),o}_getForegroundColor(e,t,n,s,o,r,a,l,u,c){let d=this._getMinimumContrastColor(e,t,n,s,o,r,a,u,l,c);if(d)return d;let h;switch(o){case 16777216:case 33554432:this._config.drawBoldTextInBrightColors&&u&&r<8&&(r+=8),h=this._getColorFromAnsiIndex(r);break;case 50331648:let f=he.toColorRGB(r);h=X.toColor(f[0],f[1],f[2]);break;case 0:default:a?h=this._config.colors.background:h=this._config.colors.foreground}return this._config.allowTransparency&&(h=Ue.opaque(h)),l&&(h=Ue.multiplyOpacity(h,gn)),h}_resolveBackgroundRgba(e,t,n){switch(e){case 16777216:case 33554432:return this._getColorFromAnsiIndex(t).rgba;case 50331648:return t<<8;case 0:default:return n?this._config.colors.foreground.rgba:this._config.colors.background.rgba}}_resolveForegroundRgba(e,t,n,s){switch(e){case 16777216:case 33554432:return this._config.drawBoldTextInBrightColors&&s&&t<8&&(t+=8),this._getColorFromAnsiIndex(t).rgba;case 50331648:return t<<8;case 0:default:return n?this._config.colors.background.rgba:this._config.colors.foreground.rgba}}_getMinimumContrastColor(e,t,n,s,o,r,a,l,u,c){if(this._config.minimumContrastRatio===1||c)return;let d=this._getContrastCache(u),h=d.getColor(e,s);if(h!==void 0)return h||void 0;let f=this._resolveBackgroundRgba(t,n,a),I=this._resolveForegroundRgba(o,r,a,l),L=ve.ensureContrastRatio(f,I,this._config.minimumContrastRatio/(u?2:1));if(!L){d.setColor(e,s,null);return}let M=X.toColor(L>>24&255,L>>16&255,L>>8&255);return d.setColor(e,s,M),M}_getContrastCache(e){return e?this._config.colors.halfContrastCache:this._config.colors.contrastCache}_drawToCache(e,t,n,s,o,r){let a=typeof e=="number"?String.fromCharCode(e):e;r&&this._tmpCanvas.parentElement!==r&&(this._tmpCanvas.style.display="none",r.append(this._tmpCanvas));let l=Math.min(this._config.deviceCellWidth*Math.max(a.length,2)+rt*2,this._config.deviceMaxTextureSize);this._tmpCanvas.width<l&&(this._tmpCanvas.width=l);let u=Math.min(this._config.deviceCellHeight+rt*4,this._textureSize);if(this._tmpCanvas.height<u&&(this._tmpCanvas.height=u),this._tmpCtx.save(),this._workAttributeData.fg=n,this._workAttributeData.bg=t,this._workAttributeData.extended.ext=s,!!this._workAttributeData.isInvisible())return An;let d=!!this._workAttributeData.isBold(),h=!!this._workAttributeData.isInverse(),f=!!this._workAttributeData.isDim(),I=!!this._workAttributeData.isItalic(),L=!!this._workAttributeData.isUnderline(),M=!!this._workAttributeData.isStrikethrough(),q=!!this._workAttributeData.isOverline(),S=this._workAttributeData.getFgColor(),W=this._workAttributeData.getFgColorMode(),E=this._workAttributeData.getBgColor(),y=this._workAttributeData.getBgColorMode();if(h){let x=S;S=E,E=x;let R=W;W=y,y=R}let w=this._getBackgroundColor(y,E,h,f);this._tmpCtx.globalCompositeOperation="copy",this._tmpCtx.fillStyle=w.css,this._tmpCtx.fillRect(0,0,this._tmpCanvas.width,this._tmpCanvas.height),this._tmpCtx.globalCompositeOperation="source-over";let G=d?this._config.fontWeightBold:this._config.fontWeight,ue=I?"italic":"";this._tmpCtx.font=`${ue} ${G} ${this._config.fontSize*this._config.devicePixelRatio}px ${this._config.fontFamily}`,this._tmpCtx.textBaseline=St;let Se=a.length===1&&Rt(a.charCodeAt(0)),ce=a.length===1&&fn(a.charCodeAt(0)),we=this._getForegroundColor(t,y,E,n,W,S,h,f,d,Dt(a.charCodeAt(0)));this._tmpCtx.fillStyle=we.css;let A=ce?0:rt*2,se=!1;this._config.customGlyphs!==!1&&(se=yn(this._tmpCtx,a,A,A,this._config.deviceCellWidth,this._config.deviceCellHeight,this._config.fontSize,this._config.devicePixelRatio));let T=!Se,p;if(typeof e=="number"?p=this._unicodeService.wcwidth(e):p=this._unicodeService.getStringCellWidth(e),L){this._tmpCtx.save();let x=Math.max(1,Math.floor(this._config.fontSize*this._config.devicePixelRatio/15)),R=x%2===1?.5:0;if(this._tmpCtx.lineWidth=x,this._workAttributeData.isUnderlineColorDefault())this._tmpCtx.strokeStyle=this._tmpCtx.fillStyle;else if(this._workAttributeData.isUnderlineColorRGB())T=!1,this._tmpCtx.strokeStyle=`rgb(${he.toColorRGB(this._workAttributeData.getUnderlineColor()).join(",")})`;else{T=!1;let ie=this._workAttributeData.getUnderlineColor();this._config.drawBoldTextInBrightColors&&this._workAttributeData.isBold()&&ie<8&&(ie+=8),this._tmpCtx.strokeStyle=this._getColorFromAnsiIndex(ie).css}this._tmpCtx.beginPath();let $=A,P=Math.ceil(A+this._config.deviceCharHeight)-R-(o?x*2:0),de=P+x,Re=P+x*2,oe=this._workAttributeData.getUnderlineVariantOffset();for(let ie=0;ie<p;ie++){this._tmpCtx.save();let N=$+ie*this._config.deviceCellWidth,ne=$+(ie+1)*this._config.deviceCellWidth,di=N+this._config.deviceCellWidth/2;switch(this._workAttributeData.extended.underlineStyle){case 2:this._tmpCtx.moveTo(N,P),this._tmpCtx.lineTo(ne,P),this._tmpCtx.moveTo(N,Re),this._tmpCtx.lineTo(ne,Re);break;case 3:let ft=x<=1?Re:Math.ceil(A+this._config.deviceCharHeight-x/2)-R,mt=x<=1?P:Math.ceil(A+this._config.deviceCharHeight+x/2)-R,qi=new Path2D;qi.rect(N,P,this._config.deviceCellWidth,Re-P),this._tmpCtx.clip(qi),this._tmpCtx.moveTo(N-this._config.deviceCellWidth/2,de),this._tmpCtx.bezierCurveTo(N-this._config.deviceCellWidth/2,mt,N,mt,N,de),this._tmpCtx.bezierCurveTo(N,ft,di,ft,di,de),this._tmpCtx.bezierCurveTo(di,mt,ne,mt,ne,de),this._tmpCtx.bezierCurveTo(ne,ft,ne+this._config.deviceCellWidth/2,ft,ne+this._config.deviceCellWidth/2,de);break;case 4:let _t=oe===0?0:oe>=x?x*2-oe:x-oe;!(oe>=x)===!1||_t===0?(this._tmpCtx.setLineDash([Math.round(x),Math.round(x)]),this._tmpCtx.moveTo(N+_t,P),this._tmpCtx.lineTo(ne,P)):(this._tmpCtx.setLineDash([Math.round(x),Math.round(x)]),this._tmpCtx.moveTo(N,P),this._tmpCtx.lineTo(N+_t,P),this._tmpCtx.moveTo(N+_t+x,P),this._tmpCtx.lineTo(ne,P)),oe=bn(ne-N,x,oe);break;case 5:let Er=.6,yr=.3,hi=ne-N,ji=Math.floor(Er*hi),Xi=Math.floor(yr*hi),Ir=hi-ji-Xi;this._tmpCtx.setLineDash([ji,Xi,Ir]),this._tmpCtx.moveTo(N,P),this._tmpCtx.lineTo(ne,P);break;case 1:default:this._tmpCtx.moveTo(N,P),this._tmpCtx.lineTo(ne,P);break}this._tmpCtx.stroke(),this._tmpCtx.restore()}if(this._tmpCtx.restore(),!se&&this._config.fontSize>=12&&!this._config.allowTransparency&&a!==" "){this._tmpCtx.save(),this._tmpCtx.textBaseline="alphabetic";let ie=this._tmpCtx.measureText(a);if(this._tmpCtx.restore(),"actualBoundingBoxDescent"in ie&&ie.actualBoundingBoxDescent>0){this._tmpCtx.save();let N=new Path2D;N.rect($,P-Math.ceil(x/2),this._config.deviceCellWidth*p,Re-P+Math.ceil(x/2)),this._tmpCtx.clip(N),this._tmpCtx.lineWidth=this._config.devicePixelRatio*3,this._tmpCtx.strokeStyle=w.css,this._tmpCtx.strokeText(a,A,A+this._config.deviceCharHeight),this._tmpCtx.restore()}}}if(q){let x=Math.max(1,Math.floor(this._config.fontSize*this._config.devicePixelRatio/15)),R=x%2===1?.5:0;this._tmpCtx.lineWidth=x,this._tmpCtx.strokeStyle=this._tmpCtx.fillStyle,this._tmpCtx.beginPath(),this._tmpCtx.moveTo(A,A+R),this._tmpCtx.lineTo(A+this._config.deviceCharWidth*p,A+R),this._tmpCtx.stroke()}if(se||this._tmpCtx.fillText(a,A,A+this._config.deviceCharHeight),a==="_"&&!this._config.allowTransparency){let x=Di(this._tmpCtx.getImageData(A,A,this._config.deviceCellWidth,this._config.deviceCellHeight),w,we,T);if(x)for(let R=1;R<=5&&(this._tmpCtx.save(),this._tmpCtx.fillStyle=w.css,this._tmpCtx.fillRect(0,0,this._tmpCanvas.width,this._tmpCanvas.height),this._tmpCtx.restore(),this._tmpCtx.fillText(a,A,A+this._config.deviceCharHeight-R),x=Di(this._tmpCtx.getImageData(A,A,this._config.deviceCellWidth,this._config.deviceCellHeight),w,we,T),!!x);R++);}if(M){let x=Math.max(1,Math.floor(this._config.fontSize*this._config.devicePixelRatio/10)),R=this._tmpCtx.lineWidth%2===1?.5:0;this._tmpCtx.lineWidth=x,this._tmpCtx.strokeStyle=this._tmpCtx.fillStyle,this._tmpCtx.beginPath(),this._tmpCtx.moveTo(A,A+Math.floor(this._config.deviceCharHeight/2)-R),this._tmpCtx.lineTo(A+this._config.deviceCharWidth*p,A+Math.floor(this._config.deviceCharHeight/2)-R),this._tmpCtx.stroke()}this._tmpCtx.restore();let g=this._tmpCtx.getImageData(0,0,this._tmpCanvas.width,this._tmpCanvas.height),b;if(this._config.allowTransparency?b=Jr(g):b=Di(g,w,we,T),b)return An;let m=this._findGlyphBoundingBox(g,this._workBoundingBox,l,ce,se,A),_,v;for(;;){if(this._activePages.length===0){let x=this._createNewPage();_=x,v=x.currentRow,v.height=m.size.y;break}_=this._activePages[this._activePages.length-1],v=_.currentRow;for(let x of this._activePages)m.size.y<=x.currentRow.height&&(_=x,v=x.currentRow);for(let x=this._activePages.length-1;x>=0;x--)for(let R of this._activePages[x].fixedRows)R.height<=v.height&&m.size.y<=R.height&&(_=this._activePages[x],v=R);if(m.size.x>this._textureSize){this._overflowSizePage||(this._overflowSizePage=new ot(this._document,this._config.deviceMaxTextureSize),this.pages.push(this._overflowSizePage),this._requestClearModel=!0,this._onAddTextureAtlasCanvas.fire(this._overflowSizePage.canvas)),_=this._overflowSizePage,v=this._overflowSizePage.currentRow,v.x+m.size.x>=_.canvas.width&&(v.x=0,v.y+=v.height,v.height=0);break}if(v.y+m.size.y>=_.canvas.height||v.height>m.size.y+2){let x=!1;if(_.currentRow.y+_.currentRow.height+m.size.y>=_.canvas.height){let R;for(let $ of this._activePages)if($.currentRow.y+$.currentRow.height+m.size.y<$.canvas.height){R=$;break}if(R)_=R;else if(i.maxAtlasPages&&this._pages.length>=i.maxAtlasPages&&v.y+m.size.y<=_.canvas.height&&v.height>=m.size.y&&v.x+m.size.x<=_.canvas.width)x=!0;else{let $=this._createNewPage();_=$,v=$.currentRow,v.height=m.size.y,x=!0}}x||(_.currentRow.height>0&&_.fixedRows.push(_.currentRow),v={x:0,y:_.currentRow.y+_.currentRow.height,height:m.size.y},_.fixedRows.push(v),_.currentRow={x:0,y:v.y+v.height,height:0})}if(v.x+m.size.x<=_.canvas.width)break;v===_.currentRow?(v.x=0,v.y+=v.height,v.height=0):_.fixedRows.splice(_.fixedRows.indexOf(v),1)}return m.texturePage=this._pages.indexOf(_),m.texturePosition.x=v.x,m.texturePosition.y=v.y,m.texturePositionClipSpace.x=v.x/_.canvas.width,m.texturePositionClipSpace.y=v.y/_.canvas.height,m.sizeClipSpace.x/=_.canvas.width,m.sizeClipSpace.y/=_.canvas.height,v.height=Math.max(v.height,m.size.y),v.x+=m.size.x,_.ctx.putImageData(g,m.texturePosition.x-this._workBoundingBox.left,m.texturePosition.y-this._workBoundingBox.top,this._workBoundingBox.left,this._workBoundingBox.top,m.size.x,m.size.y),_.addGlyph(m),_.version++,m}_findGlyphBoundingBox(e,t,n,s,o,r){t.top=0;let a=s?this._config.deviceCellHeight:this._tmpCanvas.height,l=s?this._config.deviceCellWidth:n,u=!1;for(let c=0;c<a;c++){for(let d=0;d<l;d++){let h=c*this._tmpCanvas.width*4+d*4+3;if(e.data[h]!==0){t.top=c,u=!0;break}}if(u)break}t.left=0,u=!1;for(let c=0;c<r+l;c++){for(let d=0;d<a;d++){let h=d*this._tmpCanvas.width*4+c*4+3;if(e.data[h]!==0){t.left=c,u=!0;break}}if(u)break}t.right=l,u=!1;for(let c=r+l-1;c>=r;c--){for(let d=0;d<a;d++){let h=d*this._tmpCanvas.width*4+c*4+3;if(e.data[h]!==0){t.right=c,u=!0;break}}if(u)break}t.bottom=a,u=!1;for(let c=a-1;c>=0;c--){for(let d=0;d<l;d++){let h=c*this._tmpCanvas.width*4+d*4+3;if(e.data[h]!==0){t.bottom=c,u=!0;break}}if(u)break}return{texturePage:0,texturePosition:{x:0,y:0},texturePositionClipSpace:{x:0,y:0},size:{x:t.right-t.left+1,y:t.bottom-t.top+1},sizeClipSpace:{x:t.right-t.left+1,y:t.bottom-t.top+1},offset:{x:-t.left+r+(s||o?Math.floor((this._config.deviceCellWidth-this._config.deviceCharWidth)/2):0),y:-t.top+r+(s||o?this._config.lineHeight===1?0:Math.round((this._config.deviceCellHeight-this._config.deviceCharHeight)/2):0)}}}},ot=class{constructor(e,t,n){this._usedPixels=0;this._glyphs=[];this.version=0;this.currentRow={x:0,y:0,height:0};this.fixedRows=[];if(n)for(let s of n)this._glyphs.push(...s.glyphs),this._usedPixels+=s._usedPixels;this.canvas=Sn(e,t,t),this.ctx=F(this.canvas.getContext("2d",{alpha:!0}))}get percentageUsed(){return this._usedPixels/(this.canvas.width*this.canvas.height)}get glyphs(){return this._glyphs}addGlyph(e){this._glyphs.push(e),this._usedPixels+=e.size.x*e.size.y}clear(){this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.currentRow.x=0,this.currentRow.y=0,this.currentRow.height=0,this.fixedRows.length=0,this.version++}};function Di(i,e,t,n){let s=e.rgba>>>24,o=e.rgba>>>16&255,r=e.rgba>>>8&255,a=t.rgba>>>24,l=t.rgba>>>16&255,u=t.rgba>>>8&255,c=Math.floor((Math.abs(s-a)+Math.abs(o-l)+Math.abs(r-u))/12),d=!0;for(let h=0;h<i.data.length;h+=4)i.data[h]===s&&i.data[h+1]===o&&i.data[h+2]===r||n&&Math.abs(i.data[h]-s)+Math.abs(i.data[h+1]-o)+Math.abs(i.data[h+2]-r)<c?i.data[h+3]=0:d=!1;return d}function Jr(i){for(let e=0;e<i.data.length;e+=4)if(i.data[e+3]>0)return!1;return!0}function Sn(i,e,t){let n=i.createElement("canvas");return n.width=e,n.height=t,n}function On(i,e,t,n,s,o,r,a){let l={foreground:o.foreground,background:o.background,cursor:Z,cursorAccent:Z,selectionForeground:Z,selectionBackgroundTransparent:Z,selectionBackgroundOpaque:Z,selectionInactiveBackgroundTransparent:Z,selectionInactiveBackgroundOpaque:Z,overviewRulerBorder:Z,scrollbarSliderBackground:Z,scrollbarSliderHoverBackground:Z,scrollbarSliderActiveBackground:Z,ansi:o.ansi.slice(),contrastCache:o.contrastCache,halfContrastCache:o.halfContrastCache};return{customGlyphs:s.customGlyphs,devicePixelRatio:r,deviceMaxTextureSize:a,letterSpacing:s.letterSpacing,lineHeight:s.lineHeight,deviceCellWidth:i,deviceCellHeight:e,deviceCharWidth:t,deviceCharHeight:n,fontFamily:s.fontFamily,fontSize:s.fontSize,fontWeight:s.fontWeight,fontWeightBold:s.fontWeightBold,allowTransparency:s.allowTransparency,drawBoldTextInBrightColors:s.drawBoldTextInBrightColors,minimumContrastRatio:s.minimumContrastRatio,colors:l}}function Mi(i,e){for(let t=0;t<i.colors.ansi.length;t++)if(i.colors.ansi[t].rgba!==e.colors.ansi[t].rgba)return!1;return i.devicePixelRatio===e.devicePixelRatio&&i.customGlyphs===e.customGlyphs&&i.lineHeight===e.lineHeight&&i.letterSpacing===e.letterSpacing&&i.fontFamily===e.fontFamily&&i.fontSize===e.fontSize&&i.fontWeight===e.fontWeight&&i.fontWeightBold===e.fontWeightBold&&i.allowTransparency===e.allowTransparency&&i.deviceCharWidth===e.deviceCharWidth&&i.deviceCharHeight===e.deviceCharHeight&&i.drawBoldTextInBrightColors===e.drawBoldTextInBrightColors&&i.minimumContrastRatio===e.minimumContrastRatio&&i.colors.foreground.rgba===e.colors.foreground.rgba&&i.colors.background.rgba===e.colors.background.rgba}function Fn(i){return(i&50331648)===16777216||(i&50331648)===33554432}var le=[];function Nt(i,e,t,n,s,o,r,a,l){let u=On(n,s,o,r,e,t,a,l);for(let h=0;h<le.length;h++){let f=le[h],I=f.ownedBy.indexOf(i);if(I>=0){if(Mi(f.config,u))return f.atlas;f.ownedBy.length===1?(f.atlas.dispose(),le.splice(h,1)):f.ownedBy.splice(I,1);break}}for(let h=0;h<le.length;h++){let f=le[h];if(Mi(f.config,u))return f.ownedBy.push(i),f.atlas}let c=i._core,d={atlas:new ae(document,u,c.unicodeService),config:u,ownedBy:[i]};return le.push(d),d.atlas}function Ai(i){for(let e=0;e<le.length;e++){let t=le[e].ownedBy.indexOf(i);if(t!==-1){le[e].ownedBy.length===1?(le[e].atlas.dispose(),le.splice(e,1)):le[e].ownedBy.splice(t,1);break}}}var Ut=600,Ht=class{constructor(e,t){this._renderCallback=e;this._coreBrowserService=t;this.isCursorVisible=!0,this._coreBrowserService.isFocused&&this._restartInterval()}get isPaused(){return!(this._blinkStartTimeout||this._blinkInterval)}dispose(){this._blinkInterval&&(this._coreBrowserService.window.clearInterval(this._blinkInterval),this._blinkInterval=void 0),this._blinkStartTimeout&&(this._coreBrowserService.window.clearTimeout(this._blinkStartTimeout),this._blinkStartTimeout=void 0),this._animationFrame&&(this._coreBrowserService.window.cancelAnimationFrame(this._animationFrame),this._animationFrame=void 0)}restartBlinkAnimation(){this.isPaused||(this._animationTimeRestarted=Date.now(),this.isCursorVisible=!0,this._animationFrame||(this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>{this._renderCallback(),this._animationFrame=void 0})))}_restartInterval(e=Ut){this._blinkInterval&&(this._coreBrowserService.window.clearInterval(this._blinkInterval),this._blinkInterval=void 0),this._blinkStartTimeout=this._coreBrowserService.window.setTimeout(()=>{if(this._animationTimeRestarted){let t=Ut-(Date.now()-this._animationTimeRestarted);if(this._animationTimeRestarted=void 0,t>0){this._restartInterval(t);return}}this.isCursorVisible=!1,this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>{this._renderCallback(),this._animationFrame=void 0}),this._blinkInterval=this._coreBrowserService.window.setInterval(()=>{if(this._animationTimeRestarted){let t=Ut-(Date.now()-this._animationTimeRestarted);this._animationTimeRestarted=void 0,this._restartInterval(t);return}this.isCursorVisible=!this.isCursorVisible,this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>{this._renderCallback(),this._animationFrame=void 0})},Ut)},e)}pause(){this.isCursorVisible=!0,this._blinkInterval&&(this._coreBrowserService.window.clearInterval(this._blinkInterval),this._blinkInterval=void 0),this._blinkStartTimeout&&(this._coreBrowserService.window.clearTimeout(this._blinkStartTimeout),this._blinkStartTimeout=void 0),this._animationFrame&&(this._coreBrowserService.window.cancelAnimationFrame(this._animationFrame),this._animationFrame=void 0)}resume(){this.pause(),this._animationTimeRestarted=void 0,this._restartInterval(),this.restartBlinkAnimation()}};function Si(i,e,t){let n=new e.ResizeObserver(s=>{let o=s.find(l=>l.target===i);if(!o)return;if(!("devicePixelContentBoxSize"in o)){n?.disconnect(),n=void 0;return}let r=o.devicePixelContentBoxSize[0].inlineSize,a=o.devicePixelContentBoxSize[0].blockSize;r>0&&a>0&&t(r,a)});try{n.observe(i,{box:["device-pixel-content-box"]})}catch{n.disconnect(),n=void 0}return O(()=>n?.disconnect())}function kn(i){return i>65535?(i-=65536,String.fromCharCode((i>>10)+55296)+String.fromCharCode(i%1024+56320)):String.fromCharCode(i)}var at=class i extends he{constructor(){super(...arguments);this.content=0;this.fg=0;this.bg=0;this.extended=new it;this.combinedData=""}static fromCharData(t){let n=new i;return n.setFromCharData(t),n}isCombined(){return this.content&2097152}getWidth(){return this.content>>22}getChars(){return this.content&2097152?this.combinedData:this.content&2097151?kn(this.content&2097151):""}getCode(){return this.isCombined()?this.combinedData.charCodeAt(this.combinedData.length-1):this.content&2097151}setFromCharData(t){this.fg=t[0],this.bg=0;let n=!1;if(t[1].length>2)n=!0;else if(t[1].length===2){let s=t[1].charCodeAt(0);if(55296<=s&&s<=56319){let o=t[1].charCodeAt(1);56320<=o&&o<=57343?this.content=(s-55296)*1024+o-56320+65536|t[2]<<22:n=!0}else n=!0}else this.content=t[1].charCodeAt(0)|t[2]<<22;n&&(this.combinedData=t[1],this.content=2097152|t[2]<<22)}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}};var Gt=new Float32Array([2,0,0,0,0,-2,0,0,0,0,1,0,-1,1,0,1]);function $t(i,e,t){let n=F(i.createProgram());if(i.attachShader(n,F(Pn(i,i.VERTEX_SHADER,e))),i.attachShader(n,F(Pn(i,i.FRAGMENT_SHADER,t))),i.linkProgram(n),i.getProgramParameter(n,i.LINK_STATUS))return n;console.error(i.getProgramInfoLog(n)),i.deleteProgram(n)}function Pn(i,e,t){let n=F(i.createShader(e));if(i.shaderSource(n,t),i.compileShader(n),i.getShaderParameter(n,i.COMPILE_STATUS))return n;console.error(i.getShaderInfoLog(n)),i.deleteShader(n)}function Bn(i,e){let t=Math.min(i.length*2,e),n=new Float32Array(t);for(let s=0;s<i.length;s++)n[s]=i[s];return n}var Wt=class{constructor(e){this.texture=e,this.version=-1}};var is=`#version 300 es
layout (location = 0) in vec2 a_unitquad;
layout (location = 1) in vec2 a_cellpos;
layout (location = 2) in vec2 a_offset;
layout (location = 3) in vec2 a_size;
layout (location = 4) in float a_texpage;
layout (location = 5) in vec2 a_texcoord;
layout (location = 6) in vec2 a_texsize;

uniform mat4 u_projection;
uniform vec2 u_resolution;

out vec2 v_texcoord;
flat out int v_texpage;

void main() {
  vec2 zeroToOne = (a_offset / u_resolution) + a_cellpos + (a_unitquad * a_size);
  gl_Position = u_projection * vec4(zeroToOne, 0.0, 1.0);
  v_texpage = int(a_texpage);
  v_texcoord = a_texcoord + a_unitquad * a_texsize;
}`;function ns(i){let e="";for(let t=1;t<i;t++)e+=` else if (v_texpage == ${t}) { outColor = texture(u_texture[${t}], v_texcoord); }`;return`#version 300 es
precision lowp float;

in vec2 v_texcoord;
flat in int v_texpage;

uniform sampler2D u_texture[${i}];

out vec4 outColor;

void main() {
  if (v_texpage == 0) {
    outColor = texture(u_texture[0], v_texcoord);
  } ${e}
}`}var De=11,Ve=De*Float32Array.BYTES_PER_ELEMENT,rs=2,H=0,k,Fi=0,lt=0,Kt=class extends B{constructor(t,n,s,o){super();this._terminal=t;this._gl=n;this._dimensions=s;this._optionsService=o;this._activeBuffer=0;this._vertices={count:0,attributes:new Float32Array(0),attributesBuffers:[new Float32Array(0),new Float32Array(0)]};let r=this._gl;ae.maxAtlasPages===void 0&&(ae.maxAtlasPages=Math.min(32,F(r.getParameter(r.MAX_TEXTURE_IMAGE_UNITS))),ae.maxTextureSize=F(r.getParameter(r.MAX_TEXTURE_SIZE))),this._program=F($t(r,is,ns(ae.maxAtlasPages))),this._register(O(()=>r.deleteProgram(this._program))),this._projectionLocation=F(r.getUniformLocation(this._program,"u_projection")),this._resolutionLocation=F(r.getUniformLocation(this._program,"u_resolution")),this._textureLocation=F(r.getUniformLocation(this._program,"u_texture")),this._vertexArrayObject=r.createVertexArray(),r.bindVertexArray(this._vertexArrayObject);let a=new Float32Array([0,0,1,0,0,1,1,1]),l=r.createBuffer();this._register(O(()=>r.deleteBuffer(l))),r.bindBuffer(r.ARRAY_BUFFER,l),r.bufferData(r.ARRAY_BUFFER,a,r.STATIC_DRAW),r.enableVertexAttribArray(0),r.vertexAttribPointer(0,2,this._gl.FLOAT,!1,0,0);let u=new Uint8Array([0,1,2,3]),c=r.createBuffer();this._register(O(()=>r.deleteBuffer(c))),r.bindBuffer(r.ELEMENT_ARRAY_BUFFER,c),r.bufferData(r.ELEMENT_ARRAY_BUFFER,u,r.STATIC_DRAW),this._attributesBuffer=F(r.createBuffer()),this._register(O(()=>r.deleteBuffer(this._attributesBuffer))),r.bindBuffer(r.ARRAY_BUFFER,this._attributesBuffer),r.enableVertexAttribArray(2),r.vertexAttribPointer(2,2,r.FLOAT,!1,Ve,0),r.vertexAttribDivisor(2,1),r.enableVertexAttribArray(3),r.vertexAttribPointer(3,2,r.FLOAT,!1,Ve,2*Float32Array.BYTES_PER_ELEMENT),r.vertexAttribDivisor(3,1),r.enableVertexAttribArray(4),r.vertexAttribPointer(4,1,r.FLOAT,!1,Ve,4*Float32Array.BYTES_PER_ELEMENT),r.vertexAttribDivisor(4,1),r.enableVertexAttribArray(5),r.vertexAttribPointer(5,2,r.FLOAT,!1,Ve,5*Float32Array.BYTES_PER_ELEMENT),r.vertexAttribDivisor(5,1),r.enableVertexAttribArray(6),r.vertexAttribPointer(6,2,r.FLOAT,!1,Ve,7*Float32Array.BYTES_PER_ELEMENT),r.vertexAttribDivisor(6,1),r.enableVertexAttribArray(1),r.vertexAttribPointer(1,2,r.FLOAT,!1,Ve,9*Float32Array.BYTES_PER_ELEMENT),r.vertexAttribDivisor(1,1),r.useProgram(this._program);let d=new Int32Array(ae.maxAtlasPages);for(let h=0;h<ae.maxAtlasPages;h++)d[h]=h;r.uniform1iv(this._textureLocation,d),r.uniformMatrix4fv(this._projectionLocation,!1,Gt),this._atlasTextures=[];for(let h=0;h<ae.maxAtlasPages;h++){let f=new Wt(F(r.createTexture()));this._register(O(()=>r.deleteTexture(f.texture))),r.activeTexture(r.TEXTURE0+h),r.bindTexture(r.TEXTURE_2D,f.texture),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_S,r.CLAMP_TO_EDGE),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_T,r.CLAMP_TO_EDGE),r.texImage2D(r.TEXTURE_2D,0,r.RGBA,1,1,0,r.RGBA,r.UNSIGNED_BYTE,new Uint8Array([255,0,0,255])),this._atlasTextures[h]=f}r.enable(r.BLEND),r.blendFunc(r.SRC_ALPHA,r.ONE_MINUS_SRC_ALPHA),this.handleResize()}beginFrame(){return this._atlas?this._atlas.beginFrame():!0}updateCell(t,n,s,o,r,a,l,u,c){this._updateCell(this._vertices.attributes,t,n,s,o,r,a,l,u,c)}_updateCell(t,n,s,o,r,a,l,u,c,d){if(H=(s*this._terminal.cols+n)*De,o===0||o===void 0){t.fill(0,H,H+De-1-rs);return}this._atlas&&(u&&u.length>1?k=this._atlas.getRasterizedGlyphCombinedChar(u,r,a,l,!1,this._terminal.element):k=this._atlas.getRasterizedGlyph(o,r,a,l,!1,this._terminal.element),Fi=Math.floor((this._dimensions.device.cell.width-this._dimensions.device.char.width)/2),r!==d&&k.offset.x>Fi?(lt=k.offset.x-Fi,t[H]=-(k.offset.x-lt)+this._dimensions.device.char.left,t[H+1]=-k.offset.y+this._dimensions.device.char.top,t[H+2]=(k.size.x-lt)/this._dimensions.device.canvas.width,t[H+3]=k.size.y/this._dimensions.device.canvas.height,t[H+4]=k.texturePage,t[H+5]=k.texturePositionClipSpace.x+lt/this._atlas.pages[k.texturePage].canvas.width,t[H+6]=k.texturePositionClipSpace.y,t[H+7]=k.sizeClipSpace.x-lt/this._atlas.pages[k.texturePage].canvas.width,t[H+8]=k.sizeClipSpace.y):(t[H]=-k.offset.x+this._dimensions.device.char.left,t[H+1]=-k.offset.y+this._dimensions.device.char.top,t[H+2]=k.size.x/this._dimensions.device.canvas.width,t[H+3]=k.size.y/this._dimensions.device.canvas.height,t[H+4]=k.texturePage,t[H+5]=k.texturePositionClipSpace.x,t[H+6]=k.texturePositionClipSpace.y,t[H+7]=k.sizeClipSpace.x,t[H+8]=k.sizeClipSpace.y),this._optionsService.rawOptions.rescaleOverlappingGlyphs&&mn(o,c,k.size.x,this._dimensions.device.cell.width)&&(t[H+2]=(this._dimensions.device.cell.width-1)/this._dimensions.device.canvas.width))}clear(){let t=this._terminal,n=t.cols*t.rows*De;this._vertices.count!==n?this._vertices.attributes=new Float32Array(n):this._vertices.attributes.fill(0);let s=0;for(;s<this._vertices.attributesBuffers.length;s++)this._vertices.count!==n?this._vertices.attributesBuffers[s]=new Float32Array(n):this._vertices.attributesBuffers[s].fill(0);this._vertices.count=n,s=0;for(let o=0;o<t.rows;o++)for(let r=0;r<t.cols;r++)this._vertices.attributes[s+9]=r/t.cols,this._vertices.attributes[s+10]=o/t.rows,s+=De}handleResize(){let t=this._gl;t.useProgram(this._program),t.viewport(0,0,t.canvas.width,t.canvas.height),t.uniform2f(this._resolutionLocation,t.canvas.width,t.canvas.height),this.clear()}render(t){if(!this._atlas)return;let n=this._gl;n.useProgram(this._program),n.bindVertexArray(this._vertexArrayObject),this._activeBuffer=(this._activeBuffer+1)%2;let s=this._vertices.attributesBuffers[this._activeBuffer],o=0;for(let r=0;r<t.lineLengths.length;r++){let a=r*this._terminal.cols*De,l=this._vertices.attributes.subarray(a,a+t.lineLengths[r]*De);s.set(l,o),o+=l.length}n.bindBuffer(n.ARRAY_BUFFER,this._attributesBuffer),n.bufferData(n.ARRAY_BUFFER,s.subarray(0,o),n.STREAM_DRAW);for(let r=0;r<this._atlas.pages.length;r++)this._atlas.pages[r].version!==this._atlasTextures[r].version&&this._bindAtlasPageTexture(n,this._atlas,r);n.drawElementsInstanced(n.TRIANGLE_STRIP,4,n.UNSIGNED_BYTE,0,o/De)}setAtlas(t){this._atlas=t;for(let n of this._atlasTextures)n.version=-1}_bindAtlasPageTexture(t,n,s){t.activeTexture(t.TEXTURE0+s),t.bindTexture(t.TEXTURE_2D,this._atlasTextures[s].texture),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,n.pages[s].canvas),t.generateMipmap(t.TEXTURE_2D),this._atlasTextures[s].version=n.pages[s].version}setDimensions(t){this._dimensions=t}};var ki=class{constructor(){this.clear()}clear(){this.hasSelection=!1,this.columnSelectMode=!1,this.viewportStartRow=0,this.viewportEndRow=0,this.viewportCappedStartRow=0,this.viewportCappedEndRow=0,this.startCol=0,this.endCol=0,this.selectionStart=void 0,this.selectionEnd=void 0}update(e,t,n,s=!1){if(this.selectionStart=t,this.selectionEnd=n,!t||!n||t[0]===n[0]&&t[1]===n[1]){this.clear();return}let o=e.buffers.active.ydisp,r=t[1]-o,a=n[1]-o,l=Math.max(r,0),u=Math.min(a,e.rows-1);if(l>=e.rows||u<0){this.clear();return}this.hasSelection=!0,this.columnSelectMode=s,this.viewportStartRow=r,this.viewportEndRow=a,this.viewportCappedStartRow=l,this.viewportCappedEndRow=u,this.startCol=t[0],this.endCol=n[0]}isCellSelected(e,t,n){return this.hasSelection?(n-=e.buffer.active.viewportY,this.columnSelectMode?this.startCol<=this.endCol?t>=this.startCol&&n>=this.viewportCappedStartRow&&t<this.endCol&&n<=this.viewportCappedEndRow:t<this.startCol&&n>=this.viewportCappedStartRow&&t>=this.endCol&&n<=this.viewportCappedEndRow:n>this.viewportStartRow&&n<this.viewportEndRow||this.viewportStartRow===this.viewportEndRow&&n===this.viewportStartRow&&t>=this.startCol&&t<this.endCol||this.viewportStartRow<this.viewportEndRow&&n===this.viewportEndRow&&t<this.endCol||this.viewportStartRow<this.viewportEndRow&&n===this.viewportStartRow&&t>=this.startCol):!1}};function Nn(){return new ki}var Ce=4,ze=1,qe=2,Ct=3,Un=2147483648,Vt=class{constructor(){this.cells=new Uint32Array(0),this.lineLengths=new Uint32Array(0),this.selection=Nn()}resize(e,t){let n=e*t*Ce;n!==this.cells.length&&(this.cells=new Uint32Array(n),this.lineLengths=new Uint32Array(t))}clear(){this.cells.fill(0,0),this.lineLengths.fill(0,0)}};var ss=`#version 300 es
layout (location = 0) in vec2 a_position;
layout (location = 1) in vec2 a_size;
layout (location = 2) in vec4 a_color;
layout (location = 3) in vec2 a_unitquad;

uniform mat4 u_projection;

out vec4 v_color;

void main() {
  vec2 zeroToOne = a_position + (a_unitquad * a_size);
  gl_Position = u_projection * vec4(zeroToOne, 0.0, 1.0);
  v_color = a_color;
}`,os=`#version 300 es
precision lowp float;

in vec4 v_color;

out vec4 outColor;

void main() {
  outColor = v_color;
}`,Ee=8,Pi=Ee*Float32Array.BYTES_PER_ELEMENT,as=20*Ee,zt=class{constructor(){this.attributes=new Float32Array(as),this.count=0}},xe=0,Hn=0,Wn=0,Gn=0,$n=0,Kn=0,Vn=0,qt=class extends B{constructor(t,n,s,o){super();this._terminal=t;this._gl=n;this._dimensions=s;this._themeService=o;this._vertices=new zt;this._verticesCursor=new zt;let r=this._gl;this._program=F($t(r,ss,os)),this._register(O(()=>r.deleteProgram(this._program))),this._projectionLocation=F(r.getUniformLocation(this._program,"u_projection")),this._vertexArrayObject=r.createVertexArray(),r.bindVertexArray(this._vertexArrayObject);let a=new Float32Array([0,0,1,0,0,1,1,1]),l=r.createBuffer();this._register(O(()=>r.deleteBuffer(l))),r.bindBuffer(r.ARRAY_BUFFER,l),r.bufferData(r.ARRAY_BUFFER,a,r.STATIC_DRAW),r.enableVertexAttribArray(3),r.vertexAttribPointer(3,2,this._gl.FLOAT,!1,0,0);let u=new Uint8Array([0,1,2,3]),c=r.createBuffer();this._register(O(()=>r.deleteBuffer(c))),r.bindBuffer(r.ELEMENT_ARRAY_BUFFER,c),r.bufferData(r.ELEMENT_ARRAY_BUFFER,u,r.STATIC_DRAW),this._attributesBuffer=F(r.createBuffer()),this._register(O(()=>r.deleteBuffer(this._attributesBuffer))),r.bindBuffer(r.ARRAY_BUFFER,this._attributesBuffer),r.enableVertexAttribArray(0),r.vertexAttribPointer(0,2,r.FLOAT,!1,Pi,0),r.vertexAttribDivisor(0,1),r.enableVertexAttribArray(1),r.vertexAttribPointer(1,2,r.FLOAT,!1,Pi,2*Float32Array.BYTES_PER_ELEMENT),r.vertexAttribDivisor(1,1),r.enableVertexAttribArray(2),r.vertexAttribPointer(2,4,r.FLOAT,!1,Pi,4*Float32Array.BYTES_PER_ELEMENT),r.vertexAttribDivisor(2,1),this._updateCachedColors(o.colors),this._register(this._themeService.onChangeColors(d=>{this._updateCachedColors(d),this._updateViewportRectangle()}))}renderBackgrounds(){this._renderVertices(this._vertices)}renderCursor(){this._renderVertices(this._verticesCursor)}_renderVertices(t){let n=this._gl;n.useProgram(this._program),n.bindVertexArray(this._vertexArrayObject),n.uniformMatrix4fv(this._projectionLocation,!1,Gt),n.bindBuffer(n.ARRAY_BUFFER,this._attributesBuffer),n.bufferData(n.ARRAY_BUFFER,t.attributes,n.DYNAMIC_DRAW),n.drawElementsInstanced(this._gl.TRIANGLE_STRIP,4,n.UNSIGNED_BYTE,0,t.count)}handleResize(){this._updateViewportRectangle()}setDimensions(t){this._dimensions=t}_updateCachedColors(t){this._bgFloat=this._colorToFloat32Array(t.background),this._cursorFloat=this._colorToFloat32Array(t.cursor)}_updateViewportRectangle(){this._addRectangleFloat(this._vertices.attributes,0,0,0,this._terminal.cols*this._dimensions.device.cell.width,this._terminal.rows*this._dimensions.device.cell.height,this._bgFloat)}updateBackgrounds(t){let n=this._terminal,s=this._vertices,o=1,r,a,l,u,c,d,h,f,I,L,M;for(r=0;r<n.rows;r++){for(l=-1,u=0,c=0,d=!1,a=0;a<n.cols;a++)h=(r*n.cols+a)*Ce,f=t.cells[h+ze],I=t.cells[h+qe],L=!!(I&67108864),(f!==u||I!==c&&(d||L))&&((u!==0||d&&c!==0)&&(M=o++*Ee,this._updateRectangle(s,M,c,u,l,a,r)),l=a,u=f,c=I,d=L);(u!==0||d&&c!==0)&&(M=o++*Ee,this._updateRectangle(s,M,c,u,l,n.cols,r))}s.count=o}updateCursor(t){let n=this._verticesCursor,s=t.cursor;if(!s||s.style==="block"){n.count=0;return}let o,r=0;(s.style==="bar"||s.style==="outline")&&(o=r++*Ee,this._addRectangleFloat(n.attributes,o,s.x*this._dimensions.device.cell.width,s.y*this._dimensions.device.cell.height,s.style==="bar"?s.dpr*s.cursorWidth:s.dpr,this._dimensions.device.cell.height,this._cursorFloat)),(s.style==="underline"||s.style==="outline")&&(o=r++*Ee,this._addRectangleFloat(n.attributes,o,s.x*this._dimensions.device.cell.width,(s.y+1)*this._dimensions.device.cell.height-s.dpr,s.width*this._dimensions.device.cell.width,s.dpr,this._cursorFloat)),s.style==="outline"&&(o=r++*Ee,this._addRectangleFloat(n.attributes,o,s.x*this._dimensions.device.cell.width,s.y*this._dimensions.device.cell.height,s.width*this._dimensions.device.cell.width,s.dpr,this._cursorFloat),o=r++*Ee,this._addRectangleFloat(n.attributes,o,(s.x+s.width)*this._dimensions.device.cell.width-s.dpr,s.y*this._dimensions.device.cell.height,s.dpr,this._dimensions.device.cell.height,this._cursorFloat)),n.count=r}_updateRectangle(t,n,s,o,r,a,l){if(s&67108864)switch(s&50331648){case 16777216:case 33554432:xe=this._themeService.colors.ansi[s&255].rgba;break;case 50331648:xe=(s&16777215)<<8;break;case 0:default:xe=this._themeService.colors.foreground.rgba}else switch(o&50331648){case 16777216:case 33554432:xe=this._themeService.colors.ansi[o&255].rgba;break;case 50331648:xe=(o&16777215)<<8;break;case 0:default:xe=this._themeService.colors.background.rgba}t.attributes.length<n+4&&(t.attributes=Bn(t.attributes,this._terminal.rows*this._terminal.cols*Ee)),Hn=r*this._dimensions.device.cell.width,Wn=l*this._dimensions.device.cell.height,Gn=(xe>>24&255)/255,$n=(xe>>16&255)/255,Kn=(xe>>8&255)/255,Vn=1,this._addRectangle(t.attributes,n,Hn,Wn,(a-r)*this._dimensions.device.cell.width,this._dimensions.device.cell.height,Gn,$n,Kn,Vn)}_addRectangle(t,n,s,o,r,a,l,u,c,d){t[n]=s/this._dimensions.device.canvas.width,t[n+1]=o/this._dimensions.device.canvas.height,t[n+2]=r/this._dimensions.device.canvas.width,t[n+3]=a/this._dimensions.device.canvas.height,t[n+4]=l,t[n+5]=u,t[n+6]=c,t[n+7]=d}_addRectangleFloat(t,n,s,o,r,a,l){t[n]=s/this._dimensions.device.canvas.width,t[n+1]=o/this._dimensions.device.canvas.height,t[n+2]=r/this._dimensions.device.canvas.width,t[n+3]=a/this._dimensions.device.canvas.height,t[n+4]=l[0],t[n+5]=l[1],t[n+6]=l[2],t[n+7]=l[3]}_colorToFloat32Array(t){return new Float32Array([(t.rgba>>24&255)/255,(t.rgba>>16&255)/255,(t.rgba>>8&255)/255,(t.rgba&255)/255])}};var jt=class extends B{constructor(t,n,s,o,r,a,l,u){super();this._container=n;this._alpha=r;this._coreBrowserService=a;this._optionsService=l;this._themeService=u;this._deviceCharWidth=0;this._deviceCharHeight=0;this._deviceCellWidth=0;this._deviceCellHeight=0;this._deviceCharLeft=0;this._deviceCharTop=0;this._canvas=this._coreBrowserService.mainDocument.createElement("canvas"),this._canvas.classList.add(`xterm-${s}-layer`),this._canvas.style.zIndex=o.toString(),this._initCanvas(),this._container.appendChild(this._canvas),this._register(this._themeService.onChangeColors(c=>{this._refreshCharAtlas(t,c),this.reset(t)})),this._register(O(()=>{this._canvas.remove()}))}_initCanvas(){this._ctx=F(this._canvas.getContext("2d",{alpha:this._alpha})),this._alpha||this._clearAll()}handleBlur(t){}handleFocus(t){}handleCursorMove(t){}handleGridChanged(t,n,s){}handleSelectionChanged(t,n,s,o=!1){}_setTransparency(t,n){if(n===this._alpha)return;let s=this._canvas;this._alpha=n,this._canvas=this._canvas.cloneNode(),this._initCanvas(),this._container.replaceChild(this._canvas,s),this._refreshCharAtlas(t,this._themeService.colors),this.handleGridChanged(t,0,t.rows-1)}_refreshCharAtlas(t,n){this._deviceCharWidth<=0&&this._deviceCharHeight<=0||(this._charAtlas=Nt(t,this._optionsService.rawOptions,n,this._deviceCellWidth,this._deviceCellHeight,this._deviceCharWidth,this._deviceCharHeight,this._coreBrowserService.dpr,2048),this._charAtlas.warmUp())}resize(t,n){this._deviceCellWidth=n.device.cell.width,this._deviceCellHeight=n.device.cell.height,this._deviceCharWidth=n.device.char.width,this._deviceCharHeight=n.device.char.height,this._deviceCharLeft=n.device.char.left,this._deviceCharTop=n.device.char.top,this._canvas.width=n.device.canvas.width,this._canvas.height=n.device.canvas.height,this._canvas.style.width=`${n.css.canvas.width}px`,this._canvas.style.height=`${n.css.canvas.height}px`,this._alpha||this._clearAll(),this._refreshCharAtlas(t,this._themeService.colors)}_fillBottomLineAtCells(t,n,s=1){this._ctx.fillRect(t*this._deviceCellWidth,(n+1)*this._deviceCellHeight-this._coreBrowserService.dpr-1,s*this._deviceCellWidth,this._coreBrowserService.dpr)}_clearAll(){this._alpha?this._ctx.clearRect(0,0,this._canvas.width,this._canvas.height):(this._ctx.fillStyle=this._themeService.colors.background.css,this._ctx.fillRect(0,0,this._canvas.width,this._canvas.height))}_clearCells(t,n,s,o){this._alpha?this._ctx.clearRect(t*this._deviceCellWidth,n*this._deviceCellHeight,s*this._deviceCellWidth,o*this._deviceCellHeight):(this._ctx.fillStyle=this._themeService.colors.background.css,this._ctx.fillRect(t*this._deviceCellWidth,n*this._deviceCellHeight,s*this._deviceCellWidth,o*this._deviceCellHeight))}_fillCharTrueColor(t,n,s,o){this._ctx.font=this._getFont(t,!1,!1),this._ctx.textBaseline=St,this._clipCell(s,o,n.getWidth()),this._ctx.fillText(n.getChars(),s*this._deviceCellWidth+this._deviceCharLeft,o*this._deviceCellHeight+this._deviceCharTop+this._deviceCharHeight)}_clipCell(t,n,s){this._ctx.beginPath(),this._ctx.rect(t*this._deviceCellWidth,n*this._deviceCellHeight,s*this._deviceCellWidth,this._deviceCellHeight),this._ctx.clip()}_getFont(t,n,s){let o=n?t.options.fontWeightBold:t.options.fontWeight;return`${s?"italic":""} ${o} ${t.options.fontSize*this._coreBrowserService.dpr}px ${t.options.fontFamily}`}};var Xt=class extends jt{constructor(e,t,n,s,o,r,a){super(n,e,"link",t,!0,o,r,a),this._register(s.onShowLinkUnderline(l=>this._handleShowLinkUnderline(l))),this._register(s.onHideLinkUnderline(l=>this._handleHideLinkUnderline(l)))}resize(e,t){super.resize(e,t),this._state=void 0}reset(e){this._clearCurrentLink()}_clearCurrentLink(){if(this._state){this._clearCells(this._state.x1,this._state.y1,this._state.cols-this._state.x1,1);let e=this._state.y2-this._state.y1-1;e>0&&this._clearCells(0,this._state.y1+1,this._state.cols,e),this._clearCells(0,this._state.y2,this._state.x2,1),this._state=void 0}}_handleShowLinkUnderline(e){if(e.fg===257?this._ctx.fillStyle=this._themeService.colors.background.css:e.fg!==void 0&&Fn(e.fg)?this._ctx.fillStyle=this._themeService.colors.ansi[e.fg].css:this._ctx.fillStyle=this._themeService.colors.foreground.css,e.y1===e.y2)this._fillBottomLineAtCells(e.x1,e.y1,e.x2-e.x1);else{this._fillBottomLineAtCells(e.x1,e.y1,e.cols-e.x1);for(let t=e.y1+1;t<e.y2;t++)this._fillBottomLineAtCells(0,t,e.cols);this._fillBottomLineAtCells(0,e.y2,e.x2)}this._state=e}_handleHideLinkUnderline(e){this._clearCurrentLink()}};var te=typeof window=="object"?window:globalThis;var Zt=class Zt{constructor(){this.mapWindowIdToZoomLevel=new Map;this._onDidChangeZoomLevel=new D;this.onDidChangeZoomLevel=this._onDidChangeZoomLevel.event;this.mapWindowIdToZoomFactor=new Map;this._onDidChangeFullscreen=new D;this.onDidChangeFullscreen=this._onDidChangeFullscreen.event;this.mapWindowIdToFullScreen=new Map}getZoomLevel(e){return this.mapWindowIdToZoomLevel.get(this.getWindowId(e))??0}setZoomLevel(e,t){if(this.getZoomLevel(t)===e)return;let n=this.getWindowId(t);this.mapWindowIdToZoomLevel.set(n,e),this._onDidChangeZoomLevel.fire(n)}getZoomFactor(e){return this.mapWindowIdToZoomFactor.get(this.getWindowId(e))??1}setZoomFactor(e,t){this.mapWindowIdToZoomFactor.set(this.getWindowId(t),e)}setFullscreen(e,t){if(this.isFullscreen(t)===e)return;let n=this.getWindowId(t);this.mapWindowIdToFullScreen.set(n,e),this._onDidChangeFullscreen.fire(n)}isFullscreen(e){return!!this.mapWindowIdToFullScreen.get(this.getWindowId(e))}getWindowId(e){return e.vscodeWindowId}};Zt.INSTANCE=new Zt;var Qt=Zt;function us(i,e,t){typeof e=="string"&&(e=i.matchMedia(e)),e.addEventListener("change",t)}var Wa=Qt.INSTANCE.onDidChangeZoomLevel;var Ga=Qt.INSTANCE.onDidChangeFullscreen,je=typeof navigator=="object"?navigator.userAgent:"",Cn=je.indexOf("Firefox")>=0,ut=je.indexOf("AppleWebKit")>=0,zn=je.indexOf("Chrome")>=0,Bi=!zn&&je.indexOf("Safari")>=0;var $a=je.indexOf("Electron/")>=0,Ka=je.indexOf("Android")>=0,Yt=!1;if(typeof te.matchMedia=="function"){let i=te.matchMedia("(display-mode: standalone) or (display-mode: window-controls-overlay)"),e=te.matchMedia("(display-mode: fullscreen)");Yt=i.matches,us(te,i,({matches:t})=>{Yt&&e.matches||(Yt=t)})}function qn(){return Yt}var Xe="en",Ui=!1,ni=!1,ti=!1,cs=!1,Xn=!1,Yn=!1,ds=!1,hs=!1,ps=!1,fs=!1,ei,ii=Xe,jn=Xe,ms,ye,Ie=globalThis,re;typeof Ie.vscode<"u"&&typeof Ie.vscode.process<"u"?re=Ie.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(re=process);var Qn=typeof re?.versions?.electron=="string",_s=Qn&&re?.type==="renderer";if(typeof re=="object"){Ui=re.platform==="win32",ni=re.platform==="darwin",ti=re.platform==="linux",cs=ti&&!!re.env.SNAP&&!!re.env.SNAP_REVISION,ds=Qn,ps=!!re.env.CI||!!re.env.BUILD_ARTIFACTSTAGINGDIRECTORY,ei=Xe,ii=Xe;let i=re.env.VSCODE_NLS_CONFIG;if(i)try{let e=JSON.parse(i);ei=e.userLocale,jn=e.osLocale,ii=e.resolvedLanguage||Xe,ms=e.languagePack?.translationsConfigFile}catch{}Xn=!0}else typeof navigator=="object"&&!_s?(ye=navigator.userAgent,Ui=ye.indexOf("Windows")>=0,ni=ye.indexOf("Macintosh")>=0,hs=(ye.indexOf("Macintosh")>=0||ye.indexOf("iPad")>=0||ye.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,ti=ye.indexOf("Linux")>=0,fs=ye?.indexOf("Mobi")>=0,Yn=!0,ii=globalThis._VSCODE_NLS_LANGUAGE||Xe,ei=navigator.language.toLowerCase(),jn=ei):console.error("Unable to resolve platform.");var Ni=0;ni?Ni=1:Ui?Ni=3:ti&&(Ni=2);var ct=ni;var ri=Xn;var bs=Yn&&typeof Ie.importScripts=="function",Va=bs?Ie.origin:void 0;var _e=ye,Me=ii,Ts;(n=>{function i(){return Me}n.value=i;function e(){return Me.length===2?Me==="en":Me.length>=3?Me[0]==="e"&&Me[1]==="n"&&Me[2]==="-":!1}n.isDefaultVariant=e;function t(){return Me==="en"}n.isDefault=t})(Ts||={});var vs=typeof Ie.postMessage=="function"&&!Ie.importScripts,Zn=(()=>{if(vs){let i=[];Ie.addEventListener("message",t=>{if(t.data&&t.data.vscodeScheduleAsyncWork)for(let n=0,s=i.length;n<s;n++){let o=i[n];if(o.id===t.data.vscodeScheduleAsyncWork){i.splice(n,1),o.callback();return}}});let e=0;return t=>{let n=++e;i.push({id:n,callback:t}),Ie.postMessage({vscodeScheduleAsyncWork:n},"*")}}return i=>setTimeout(i)})();var gs=!!(_e&&_e.indexOf("Chrome")>=0),Ca=!!(_e&&_e.indexOf("Firefox")>=0),za=!!(!gs&&_e&&_e.indexOf("Safari")>=0),qa=!!(_e&&_e.indexOf("Edg/")>=0),ja=!!(_e&&_e.indexOf("Android")>=0);var Ae=typeof navigator=="object"?navigator:{},xs={clipboard:{writeText:ri||document.queryCommandSupported&&document.queryCommandSupported("copy")||!!(Ae&&Ae.clipboard&&Ae.clipboard.writeText),readText:ri||!!(Ae&&Ae.clipboard&&Ae.clipboard.readText)},keyboard:ri||qn()?0:Ae.keyboard||Bi?1:2,touch:"ontouchstart"in te||Ae.maxTouchPoints>0,pointerEvents:te.PointerEvent&&("ontouchstart"in te||navigator.maxTouchPoints>0)};var dt=class{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(e,t){this._keyCodeToStr[e]=t,this._strToKeyCode[t.toLowerCase()]=e}keyCodeToStr(e){return this._keyCodeToStr[e]}strToKeyCode(e){return this._strToKeyCode[e.toLowerCase()]||0}},Hi=new dt,Jn=new dt,er=new dt,Es=new Array(230);var tr;(r=>{function i(a){return Hi.keyCodeToStr(a)}r.toString=i;function e(a){return Hi.strToKeyCode(a)}r.fromString=e;function t(a){return Jn.keyCodeToStr(a)}r.toUserSettingsUS=t;function n(a){return er.keyCodeToStr(a)}r.toUserSettingsGeneral=n;function s(a){return Jn.strToKeyCode(a)||er.strToKeyCode(a)}r.fromUserSettings=s;function o(a){if(a>=98&&a<=113)return null;switch(a){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return Hi.keyCodeToStr(a)}r.toElectronAccelerator=o})(tr||={});var ul=ct?256:2048,cl=512,dl=1024,hl=ct?2048:256;var nr=Object.freeze(function(i,e){let t=setTimeout(i.bind(e),0);return{dispose(){clearTimeout(t)}}}),Is;(n=>{function i(s){return s===n.None||s===n.Cancelled||s instanceof Wi?!0:!s||typeof s!="object"?!1:typeof s.isCancellationRequested=="boolean"&&typeof s.onCancellationRequested=="function"}n.isCancellationToken=i,n.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:ee.None}),n.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:nr})})(Is||={});var Wi=class{constructor(){this._isCancelled=!1;this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?nr:(this._emitter||(this._emitter=new D),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}};var Ls=Symbol("MicrotaskDelay");var ws,oi;(function(){typeof globalThis.requestIdleCallback!="function"||typeof globalThis.cancelIdleCallback!="function"?oi=(i,e)=>{Zn(()=>{if(t)return;let n=Date.now()+15;e(Object.freeze({didTimeout:!0,timeRemaining(){return Math.max(0,n-Date.now())}}))});let t=!1;return{dispose(){t||(t=!0)}}}:oi=(i,e,t)=>{let n=i.requestIdleCallback(e,typeof t=="number"?{timeout:t}:void 0),s=!1;return{dispose(){s||(s=!0,i.cancelIdleCallback(n))}}},ws=i=>oi(globalThis,i)})();var Rs;(t=>{async function i(n){let s,o=await Promise.all(n.map(r=>r.then(a=>a,a=>{s||(s=a)})));if(typeof s<"u")throw s;return o}t.settled=i;function e(n){return new Promise(async(s,o)=>{try{await n(s,o)}catch(r){o(r)}})}t.withAsyncBody=e})(Rs||={});var Q=class Q{static fromArray(e){return new Q(t=>{t.emitMany(e)})}static fromPromise(e){return new Q(async t=>{t.emitMany(await e)})}static fromPromises(e){return new Q(async t=>{await Promise.all(e.map(async n=>t.emitOne(await n)))})}static merge(e){return new Q(async t=>{await Promise.all(e.map(async n=>{for await(let s of n)t.emitOne(s)}))})}constructor(e,t){this._state=0,this._results=[],this._error=null,this._onReturn=t,this._onStateChanged=new D,queueMicrotask(async()=>{let n={emitOne:s=>this.emitOne(s),emitMany:s=>this.emitMany(s),reject:s=>this.reject(s)};try{await Promise.resolve(e(n)),this.resolve()}catch(s){this.reject(s)}finally{n.emitOne=void 0,n.emitMany=void 0,n.reject=void 0}})}[Symbol.asyncIterator](){let e=0;return{next:async()=>{do{if(this._state===2)throw this._error;if(e<this._results.length)return{done:!1,value:this._results[e++]};if(this._state===1)return{done:!0,value:void 0};await ee.toPromise(this._onStateChanged.event)}while(!0)},return:async()=>(this._onReturn?.(),{done:!0,value:void 0})}}static map(e,t){return new Q(async n=>{for await(let s of e)n.emitOne(t(s))})}map(e){return Q.map(this,e)}static filter(e,t){return new Q(async n=>{for await(let s of e)t(s)&&n.emitOne(s)})}filter(e){return Q.filter(this,e)}static coalesce(e){return Q.filter(e,t=>!!t)}coalesce(){return Q.coalesce(this)}static async toPromise(e){let t=[];for await(let n of e)t.push(n);return t}toPromise(){return Q.toPromise(this)}emitOne(e){this._state===0&&(this._results.push(e),this._onStateChanged.fire())}emitMany(e){this._state===0&&(this._results=this._results.concat(e),this._onStateChanged.fire())}resolve(){this._state===0&&(this._state=1,this._onStateChanged.fire())}reject(e){this._state===0&&(this._state=2,this._error=e,this._onStateChanged.fire())}};Q.EMPTY=Q.fromArray([]);var rr=Q;function sr(i){return 55296<=i&&i<=56319}function Gi(i){return 56320<=i&&i<=57343}function or(i,e){return(i-55296<<10)+(e-56320)+65536}function ur(i){return Ki(i,0)}function Ki(i,e){switch(typeof i){case"object":return i===null?Le(349,e):Array.isArray(i)?As(i,e):Ss(i,e);case"string":return cr(i,e);case"boolean":return Ms(i,e);case"number":return Le(i,e);case"undefined":return Le(937,e);default:return Le(617,e)}}function Le(i,e){return(e<<5)-e+i|0}function Ms(i,e){return Le(i?433:863,e)}function cr(i,e){e=Le(149417,e);for(let t=0,n=i.length;t<n;t++)e=Le(i.charCodeAt(t),e);return e}function As(i,e){return e=Le(104579,e),i.reduce((t,n)=>Ki(n,t),e)}function Ss(i,e){return e=Le(181387,e),Object.keys(i).sort().reduce((t,n)=>(t=cr(n,t),Ki(i[n],t)),e)}function $i(i,e,t=32){let n=t-e,s=~((1<<n)-1);return(i<<e|(s&i)>>>n)>>>0}function ar(i,e=0,t=i.byteLength,n=0){for(let s=0;s<t;s++)i[e+s]=n}function Os(i,e,t="0"){for(;i.length<e;)i=t+i;return i}function ht(i,e=32){return i instanceof ArrayBuffer?Array.from(new Uint8Array(i)).map(t=>t.toString(16).padStart(2,"0")).join(""):Os((i>>>0).toString(16),e/4)}var ai=class ai{constructor(){this._h0=1732584193;this._h1=4023233417;this._h2=2562383102;this._h3=271733878;this._h4=3285377520;this._buff=new Uint8Array(67),this._buffDV=new DataView(this._buff.buffer),this._buffLen=0,this._totalLen=0,this._leftoverHighSurrogate=0,this._finished=!1}update(e){let t=e.length;if(t===0)return;let n=this._buff,s=this._buffLen,o=this._leftoverHighSurrogate,r,a;for(o!==0?(r=o,a=-1,o=0):(r=e.charCodeAt(0),a=0);;){let l=r;if(sr(r))if(a+1<t){let u=e.charCodeAt(a+1);Gi(u)?(a++,l=or(r,u)):l=65533}else{o=r;break}else Gi(r)&&(l=65533);if(s=this._push(n,s,l),a++,a<t)r=e.charCodeAt(a);else break}this._buffLen=s,this._leftoverHighSurrogate=o}_push(e,t,n){return n<128?e[t++]=n:n<2048?(e[t++]=192|(n&1984)>>>6,e[t++]=128|(n&63)>>>0):n<65536?(e[t++]=224|(n&61440)>>>12,e[t++]=128|(n&4032)>>>6,e[t++]=128|(n&63)>>>0):(e[t++]=240|(n&1835008)>>>18,e[t++]=128|(n&258048)>>>12,e[t++]=128|(n&4032)>>>6,e[t++]=128|(n&63)>>>0),t>=64&&(this._step(),t-=64,this._totalLen+=64,e[0]=e[64],e[1]=e[65],e[2]=e[66]),t}digest(){return this._finished||(this._finished=!0,this._leftoverHighSurrogate&&(this._leftoverHighSurrogate=0,this._buffLen=this._push(this._buff,this._buffLen,65533)),this._totalLen+=this._buffLen,this._wrapUp()),ht(this._h0)+ht(this._h1)+ht(this._h2)+ht(this._h3)+ht(this._h4)}_wrapUp(){this._buff[this._buffLen++]=128,ar(this._buff,this._buffLen),this._buffLen>56&&(this._step(),ar(this._buff));let e=8*this._totalLen;this._buffDV.setUint32(56,Math.floor(e/4294967296),!1),this._buffDV.setUint32(60,e%4294967296,!1),this._step()}_step(){let e=ai._bigBlock32,t=this._buffDV;for(let d=0;d<64;d+=4)e.setUint32(d,t.getUint32(d,!1),!1);for(let d=64;d<320;d+=4)e.setUint32(d,$i(e.getUint32(d-12,!1)^e.getUint32(d-32,!1)^e.getUint32(d-56,!1)^e.getUint32(d-64,!1),1),!1);let n=this._h0,s=this._h1,o=this._h2,r=this._h3,a=this._h4,l,u,c;for(let d=0;d<80;d++)d<20?(l=s&o|~s&r,u=1518500249):d<40?(l=s^o^r,u=1859775393):d<60?(l=s&o|s&r|o&r,u=2400959708):(l=s^o^r,u=3395469782),c=$i(n,5)+l+a+u+e.getUint32(d*4,!1)&4294967295,a=r,r=o,o=$i(s,30),s=n,n=c;this._h0=this._h0+n&4294967295,this._h1=this._h1+s&4294967295,this._h2=this._h2+o&4294967295,this._h3=this._h3+r&4294967295,this._h4=this._h4+a&4294967295}};ai._bigBlock32=new DataView(new ArrayBuffer(320));var lr=ai;var{registerWindow:fu,getWindow:Fs,getDocument:mu,getWindows:_u,getWindowsCount:bu,getWindowId:dr,getWindowById:Tu,hasWindow:vu,onDidRegisterWindow:gu,onWillUnregisterWindow:xu,onDidUnregisterWindow:Eu}=function(){let i=new Map;te;let e={window:te,disposables:new fe};i.set(te.vscodeWindowId,e);let t=new D,n=new D,s=new D;function o(r,a){return(typeof r=="number"?i.get(r):void 0)??(a?e:void 0)}return{onDidRegisterWindow:t.event,onWillUnregisterWindow:s.event,onDidUnregisterWindow:n.event,registerWindow(r){if(i.has(r.vscodeWindowId))return B.None;let a=new fe,l={window:r,disposables:a.add(new fe)};return i.set(r.vscodeWindowId,l),a.add(O(()=>{i.delete(r.vscodeWindowId),n.fire(r)})),a.add(li(r,Ps.BEFORE_UNLOAD,()=>{s.fire(r)})),t.fire(l),a},getWindows(){return i.values()},getWindowsCount(){return i.size},getWindowId(r){return r.vscodeWindowId},hasWindow(r){return i.has(r)},getWindowById:o,getWindow(r){let a=r;if(a?.ownerDocument?.defaultView)return a.ownerDocument.defaultView.window;let l=r;return l?.view?l.view.window:te},getDocument(r){return Fs(r).document}}}();var Vi=class{constructor(e,t,n,s){this._node=e,this._type=t,this._handler=n,this._options=s||!1,this._node.addEventListener(this._type,this._handler,this._options)}dispose(){this._handler&&(this._node.removeEventListener(this._type,this._handler,this._options),this._node=null,this._handler=null)}};function li(i,e,t,n){return new Vi(i,e,t,n)}var ks,hr;var pt=class{constructor(e,t=0){this._runner=e,this.priority=t,this._canceled=!1}dispose(){this._canceled=!0}execute(){if(!this._canceled)try{this._runner()}catch(e){Pe(e)}}static sort(e,t){return t.priority-e.priority}};(function(){let i=new Map,e=new Map,t=new Map,n=new Map,s=o=>{t.set(o,!1);let r=i.get(o)??[];for(e.set(o,r),i.set(o,[]),n.set(o,!0);r.length>0;)r.sort(pt.sort),r.shift().execute();n.set(o,!1)};hr=(o,r,a=0)=>{let l=dr(o),u=new pt(r,a),c=i.get(l);return c||(c=[],i.set(l,c)),c.push(u),t.get(l)||(t.set(l,!0),o.requestAnimationFrame(()=>s(l))),u},ks=(o,r,a)=>{let l=dr(o);if(n.get(l)){let u=new pt(r,a),c=e.get(l);return c||(c=[],e.set(l,c)),c.push(u),u}else return hr(o,r,a)}})();var ke=class ke{constructor(e,t){this.width=e;this.height=t}with(e=this.width,t=this.height){return e!==this.width||t!==this.height?new ke(e,t):this}static is(e){return typeof e=="object"&&typeof e.height=="number"&&typeof e.width=="number"}static lift(e){return e instanceof ke?e:new ke(e.width,e.height)}static equals(e,t){return e===t?!0:!e||!t?!1:e.width===t.width&&e.height===t.height}};ke.None=new ke(0,0);var pr=ke;var yu=new class{constructor(){this.mutationObservers=new Map}observe(i,e,t){let n=this.mutationObservers.get(i);n||(n=new Map,this.mutationObservers.set(i,n));let s=ur(t),o=n.get(s);if(o)o.users+=1;else{let r=new D,a=new MutationObserver(u=>r.fire(u));a.observe(i,t);let l=o={users:1,observer:a,onDidMutate:r.event};e.add(O(()=>{l.users-=1,l.users===0&&(r.dispose(),a.disconnect(),n?.delete(s),n?.size===0&&this.mutationObservers.delete(i))})),n.set(s,o)}return o.onDidMutate}};var Ps={CLICK:"click",AUXCLICK:"auxclick",DBLCLICK:"dblclick",MOUSE_UP:"mouseup",MOUSE_DOWN:"mousedown",MOUSE_OVER:"mouseover",MOUSE_MOVE:"mousemove",MOUSE_OUT:"mouseout",MOUSE_ENTER:"mouseenter",MOUSE_LEAVE:"mouseleave",MOUSE_WHEEL:"wheel",POINTER_UP:"pointerup",POINTER_DOWN:"pointerdown",POINTER_MOVE:"pointermove",POINTER_LEAVE:"pointerleave",CONTEXT_MENU:"contextmenu",WHEEL:"wheel",KEY_DOWN:"keydown",KEY_PRESS:"keypress",KEY_UP:"keyup",LOAD:"load",BEFORE_UNLOAD:"beforeunload",UNLOAD:"unload",PAGE_SHOW:"pageshow",PAGE_HIDE:"pagehide",PASTE:"paste",ABORT:"abort",ERROR:"error",RESIZE:"resize",SCROLL:"scroll",FULLSCREEN_CHANGE:"fullscreenchange",WK_FULLSCREEN_CHANGE:"webkitfullscreenchange",SELECT:"select",CHANGE:"change",SUBMIT:"submit",RESET:"reset",FOCUS:"focus",FOCUS_IN:"focusin",FOCUS_OUT:"focusout",BLUR:"blur",INPUT:"input",STORAGE:"storage",DRAG_START:"dragstart",DRAG:"drag",DRAG_ENTER:"dragenter",DRAG_LEAVE:"dragleave",DRAG_OVER:"dragover",DROP:"drop",DRAG_END:"dragend",ANIMATION_START:ut?"webkitAnimationStart":"animationstart",ANIMATION_END:ut?"webkitAnimationEnd":"animationend",ANIMATION_ITERATION:ut?"webkitAnimationIteration":"animationiteration"};var Bs=/([\w\-]+)?(#([\w\-]+))?((\.([\w\-]+))*)/;function fr(i,e,t,...n){let s=Bs.exec(e);if(!s)throw new Error("Bad use of emmet");let o=s[1]||"div",r;return i!=="http://www.w3.org/1999/xhtml"?r=document.createElementNS(i,o):r=document.createElement(o),s[3]&&(r.id=s[3]),s[4]&&(r.className=s[4].replace(/\./g," ").trim()),t&&Object.entries(t).forEach(([a,l])=>{typeof l>"u"||(/^on\w+$/.test(a)?r[a]=l:a==="selected"?l&&r.setAttribute(a,"true"):r.setAttribute(a,l))}),r.append(...n),r}function Ns(i,e,...t){return fr("http://www.w3.org/1999/xhtml",i,e,...t)}Ns.SVG=function(i,e,...t){return fr("http://www.w3.org/2000/svg",i,e,...t)};var ui=class extends B{constructor(t,n,s,o,r,a,l,u,c){super();this._terminal=t;this._characterJoinerService=n;this._charSizeService=s;this._coreBrowserService=o;this._coreService=r;this._decorationService=a;this._optionsService=l;this._themeService=u;this._cursorBlinkStateManager=new be;this._charAtlasDisposable=this._register(new be);this._observerDisposable=this._register(new be);this._model=new Vt;this._workCell=new at;this._workCell2=new at;this._rectangleRenderer=this._register(new be);this._glyphRenderer=this._register(new be);this._onChangeTextureAtlas=this._register(new D);this.onChangeTextureAtlas=this._onChangeTextureAtlas.event;this._onAddTextureAtlasCanvas=this._register(new D);this.onAddTextureAtlasCanvas=this._onAddTextureAtlasCanvas.event;this._onRemoveTextureAtlasCanvas=this._register(new D);this.onRemoveTextureAtlasCanvas=this._onRemoveTextureAtlasCanvas.event;this._onRequestRedraw=this._register(new D);this.onRequestRedraw=this._onRequestRedraw.event;this._onContextLoss=this._register(new D);this.onContextLoss=this._onContextLoss.event;this._canvas=this._coreBrowserService.mainDocument.createElement("canvas");let d={antialias:!1,depth:!1,preserveDrawingBuffer:c};if(this._gl=this._canvas.getContext("webgl2",d),!this._gl)throw new Error("WebGL2 not supported "+this._gl);this._register(this._themeService.onChangeColors(()=>this._handleColorChange())),this._cellColorResolver=new At(this._terminal,this._optionsService,this._model.selection,this._decorationService,this._coreBrowserService,this._themeService),this._core=this._terminal._core,this._renderLayers=[new Xt(this._core.screenElement,2,this._terminal,this._core.linkifier,this._coreBrowserService,l,this._themeService)],this.dimensions=_n(),this._devicePixelRatio=this._coreBrowserService.dpr,this._updateDimensions(),this._updateCursorBlink(),this._register(l.onOptionChange(()=>this._handleOptionsChanged())),this._deviceMaxTextureSize=this._gl.getParameter(this._gl.MAX_TEXTURE_SIZE),this._register(li(this._canvas,"webglcontextlost",h=>{console.log("webglcontextlost event received"),h.preventDefault(),this._contextRestorationTimeout=setTimeout(()=>{this._contextRestorationTimeout=void 0,console.warn("webgl context not restored; firing onContextLoss"),this._onContextLoss.fire(h)},3e3)})),this._register(li(this._canvas,"webglcontextrestored",h=>{console.warn("webglcontextrestored event received"),clearTimeout(this._contextRestorationTimeout),this._contextRestorationTimeout=void 0,Ai(this._terminal),this._initializeWebGLState(),this._requestRedrawViewport()})),this._observerDisposable.value=Si(this._canvas,this._coreBrowserService.window,(h,f)=>this._setCanvasDevicePixelDimensions(h,f)),this._register(this._coreBrowserService.onWindowChange(h=>{this._observerDisposable.value=Si(this._canvas,h,(f,I)=>this._setCanvasDevicePixelDimensions(f,I))})),this._core.screenElement.appendChild(this._canvas),[this._rectangleRenderer.value,this._glyphRenderer.value]=this._initializeWebGLState(),this._isAttached=this._coreBrowserService.window.document.body.contains(this._core.screenElement),this._register(O(()=>{for(let h of this._renderLayers)h.dispose();this._canvas.parentElement?.removeChild(this._canvas),Ai(this._terminal)}))}get textureAtlas(){return this._charAtlas?.pages[0].canvas}_handleColorChange(){this._refreshCharAtlas(),this._clearModel(!0)}handleDevicePixelRatioChange(){this._devicePixelRatio!==this._coreBrowserService.dpr&&(this._devicePixelRatio=this._coreBrowserService.dpr,this.handleResize(this._terminal.cols,this._terminal.rows))}handleResize(t,n){this._updateDimensions(),this._model.resize(this._terminal.cols,this._terminal.rows);for(let s of this._renderLayers)s.resize(this._terminal,this.dimensions);this._canvas.width=this.dimensions.device.canvas.width,this._canvas.height=this.dimensions.device.canvas.height,this._canvas.style.width=`${this.dimensions.css.canvas.width}px`,this._canvas.style.height=`${this.dimensions.css.canvas.height}px`,this._core.screenElement.style.width=`${this.dimensions.css.canvas.width}px`,this._core.screenElement.style.height=`${this.dimensions.css.canvas.height}px`,this._rectangleRenderer.value?.setDimensions(this.dimensions),this._rectangleRenderer.value?.handleResize(),this._glyphRenderer.value?.setDimensions(this.dimensions),this._glyphRenderer.value?.handleResize(),this._refreshCharAtlas(),this._clearModel(!1)}handleCharSizeChanged(){this.handleResize(this._terminal.cols,this._terminal.rows)}handleBlur(){for(let t of this._renderLayers)t.handleBlur(this._terminal);this._cursorBlinkStateManager.value?.pause(),this._requestRedrawViewport()}handleFocus(){for(let t of this._renderLayers)t.handleFocus(this._terminal);this._cursorBlinkStateManager.value?.resume(),this._requestRedrawViewport()}handleSelectionChanged(t,n,s){for(let o of this._renderLayers)o.handleSelectionChanged(this._terminal,t,n,s);this._model.selection.update(this._core,t,n,s),this._requestRedrawViewport()}handleCursorMove(){for(let t of this._renderLayers)t.handleCursorMove(this._terminal);this._cursorBlinkStateManager.value?.restartBlinkAnimation()}_handleOptionsChanged(){this._updateDimensions(),this._refreshCharAtlas(),this._updateCursorBlink()}_initializeWebGLState(){return this._rectangleRenderer.value=new qt(this._terminal,this._gl,this.dimensions,this._themeService),this._glyphRenderer.value=new Kt(this._terminal,this._gl,this.dimensions,this._optionsService),this.handleCharSizeChanged(),[this._rectangleRenderer.value,this._glyphRenderer.value]}_refreshCharAtlas(){if(this.dimensions.device.char.width<=0&&this.dimensions.device.char.height<=0){this._isAttached=!1;return}let t=Nt(this._terminal,this._optionsService.rawOptions,this._themeService.colors,this.dimensions.device.cell.width,this.dimensions.device.cell.height,this.dimensions.device.char.width,this.dimensions.device.char.height,this._coreBrowserService.dpr,this._deviceMaxTextureSize);this._charAtlas!==t&&(this._onChangeTextureAtlas.fire(t.pages[0].canvas),this._charAtlasDisposable.value=It(ee.forward(t.onAddTextureAtlasCanvas,this._onAddTextureAtlasCanvas),ee.forward(t.onRemoveTextureAtlasCanvas,this._onRemoveTextureAtlasCanvas))),this._charAtlas=t,this._charAtlas.warmUp(),this._glyphRenderer.value?.setAtlas(this._charAtlas)}_clearModel(t){this._model.clear(),t&&this._glyphRenderer.value?.clear()}clearTextureAtlas(){this._charAtlas?.clearTexture(),this._clearModel(!0),this._requestRedrawViewport()}clear(){this._clearModel(!0);for(let t of this._renderLayers)t.reset(this._terminal);this._cursorBlinkStateManager.value?.restartBlinkAnimation(),this._updateCursorBlink()}renderRows(t,n){if(!this._isAttached)if(this._coreBrowserService.window.document.body.contains(this._core.screenElement)&&this._charSizeService.width&&this._charSizeService.height)this._updateDimensions(),this._refreshCharAtlas(),this._isAttached=!0;else return;for(let s of this._renderLayers)s.handleGridChanged(this._terminal,t,n);!this._glyphRenderer.value||!this._rectangleRenderer.value||(this._glyphRenderer.value.beginFrame()?(this._clearModel(!0),this._updateModel(0,this._terminal.rows-1)):this._updateModel(t,n),this._rectangleRenderer.value.renderBackgrounds(),this._glyphRenderer.value.render(this._model),(!this._cursorBlinkStateManager.value||this._cursorBlinkStateManager.value.isCursorVisible)&&this._rectangleRenderer.value.renderCursor())}_updateCursorBlink(){this._coreService.decPrivateModes.cursorBlink??this._terminal.options.cursorBlink?this._cursorBlinkStateManager.value=new Ht(()=>{this._requestRedrawCursor()},this._coreBrowserService):this._cursorBlinkStateManager.clear(),this._requestRedrawCursor()}_updateModel(t,n){let s=this._core,o=this._workCell,r,a,l,u,c,d,h=0,f=!0,I,L,M,q,S,W,E,y,w;t=mr(t,s.rows-1,0),n=mr(n,s.rows-1,0);let G=this._coreService.decPrivateModes.cursorStyle??s.options.cursorStyle??"block",ue=this._terminal.buffer.active.baseY+this._terminal.buffer.active.cursorY,Se=ue-s.buffer.ydisp,ce=Math.min(this._terminal.buffer.active.cursorX,s.cols-1),we=-1,A=this._coreService.isCursorInitialized&&!this._coreService.isCursorHidden&&(!this._cursorBlinkStateManager.value||this._cursorBlinkStateManager.value.isCursorVisible);this._model.cursor=void 0;let se=!1;for(a=t;a<=n;a++)for(l=a+s.buffer.ydisp,u=s.buffer.lines.get(l),this._model.lineLengths[a]=0,M=ue===l,h=0,c=this._characterJoinerService.getJoinedCharacters(l),y=0;y<s.cols;y++){if(r=this._cellColorResolver.result.bg,u.loadCell(y,o),y===0&&(r=this._cellColorResolver.result.bg),d=!1,f=y>=h,I=y,c.length>0&&y===c[0][0]&&f){L=c.shift();let T=this._model.selection.isCellSelected(this._terminal,L[0],l);for(E=L[0]+1;E<L[1];E++)f&&=T===this._model.selection.isCellSelected(this._terminal,E,l);f&&=!M||ce<L[0]||ce>=L[1],f?(d=!0,o=new Ci(o,u.translateToString(!0,L[0],L[1]),L[1]-L[0]),I=L[1]-1):h=L[1]}if(q=o.getChars(),S=o.getCode(),E=(a*s.cols+y)*Ce,this._cellColorResolver.resolve(o,y,l,this.dimensions.device.cell.width),A&&l===ue&&(y===ce&&(this._model.cursor={x:ce,y:Se,width:o.getWidth(),style:this._coreBrowserService.isFocused?G:s.options.cursorInactiveStyle,cursorWidth:s.options.cursorWidth,dpr:this._devicePixelRatio},we=ce+o.getWidth()-1),y>=ce&&y<=we&&(this._coreBrowserService.isFocused&&G==="block"||this._coreBrowserService.isFocused===!1&&s.options.cursorInactiveStyle==="block")&&(this._cellColorResolver.result.fg=50331648|this._themeService.colors.cursorAccent.rgba>>8&16777215,this._cellColorResolver.result.bg=50331648|this._themeService.colors.cursor.rgba>>8&16777215)),S!==0&&(this._model.lineLengths[a]=y+1),!(this._model.cells[E]===S&&this._model.cells[E+ze]===this._cellColorResolver.result.bg&&this._model.cells[E+qe]===this._cellColorResolver.result.fg&&this._model.cells[E+Ct]===this._cellColorResolver.result.ext)&&(se=!0,q.length>1&&(S|=Un),this._model.cells[E]=S,this._model.cells[E+ze]=this._cellColorResolver.result.bg,this._model.cells[E+qe]=this._cellColorResolver.result.fg,this._model.cells[E+Ct]=this._cellColorResolver.result.ext,W=o.getWidth(),this._glyphRenderer.value.updateCell(y,a,S,this._cellColorResolver.result.bg,this._cellColorResolver.result.fg,this._cellColorResolver.result.ext,q,W,r),d)){for(o=this._workCell,y++;y<=I;y++)w=(a*s.cols+y)*Ce,this._glyphRenderer.value.updateCell(y,a,0,0,0,0,pn,0,0),this._model.cells[w]=0,this._model.cells[w+ze]=this._cellColorResolver.result.bg,this._model.cells[w+qe]=this._cellColorResolver.result.fg,this._model.cells[w+Ct]=this._cellColorResolver.result.ext;y--}}se&&this._rectangleRenderer.value.updateBackgrounds(this._model),this._rectangleRenderer.value.updateCursor(this._model)}_updateDimensions(){!this._charSizeService.width||!this._charSizeService.height||(this.dimensions.device.char.width=Math.floor(this._charSizeService.width*this._devicePixelRatio),this.dimensions.device.char.height=Math.ceil(this._charSizeService.height*this._devicePixelRatio),this.dimensions.device.cell.height=Math.floor(this.dimensions.device.char.height*this._optionsService.rawOptions.lineHeight),this.dimensions.device.char.top=this._optionsService.rawOptions.lineHeight===1?0:Math.round((this.dimensions.device.cell.height-this.dimensions.device.char.height)/2),this.dimensions.device.cell.width=this.dimensions.device.char.width+Math.round(this._optionsService.rawOptions.letterSpacing),this.dimensions.device.char.left=Math.floor(this._optionsService.rawOptions.letterSpacing/2),this.dimensions.device.canvas.height=this._terminal.rows*this.dimensions.device.cell.height,this.dimensions.device.canvas.width=this._terminal.cols*this.dimensions.device.cell.width,this.dimensions.css.canvas.height=Math.round(this.dimensions.device.canvas.height/this._devicePixelRatio),this.dimensions.css.canvas.width=Math.round(this.dimensions.device.canvas.width/this._devicePixelRatio),this.dimensions.css.cell.height=this.dimensions.device.cell.height/this._devicePixelRatio,this.dimensions.css.cell.width=this.dimensions.device.cell.width/this._devicePixelRatio)}_setCanvasDevicePixelDimensions(t,n){this._canvas.width===t&&this._canvas.height===n||(this._canvas.width=t,this._canvas.height=n,this._requestRedrawViewport())}_requestRedrawViewport(){this._onRequestRedraw.fire({start:0,end:this._terminal.rows-1})}_requestRedrawCursor(){let t=this._terminal.buffer.active.cursorY;this._onRequestRedraw.fire({start:t,end:t})}},Ci=class extends he{constructor(t,n,s){super();this.content=0;this.combinedData="";this.fg=t.fg,this.bg=t.bg,this.combinedData=n,this._width=s}isCombined(){return 2097152}getWidth(){return this._width}getChars(){return this.combinedData}getCode(){return 2097151}setFromCharData(t){throw new Error("not implemented")}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}};function mr(i,e,t=0){return Math.max(Math.min(i,e),t)}var _r="di$target",br="di$dependencies",zi=new Map;function pe(i){if(zi.has(i))return zi.get(i);let e=function(t,n,s){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");Us(e,t,s)};return e._id=i,zi.set(i,e),e}function Us(i,e,t){e[_r]===e?e[br].push({id:i,index:t}):(e[br]=[{id:i,index:t}],e[_r]=e)}var Vu=pe("BufferService"),Cu=pe("CoreMouseService"),zu=pe("CoreService"),qu=pe("CharsetService"),ju=pe("InstantiationService");var Xu=pe("LogService"),Tr=pe("OptionsService"),Yu=pe("OscLinkService"),Qu=pe("UnicodeService"),Zu=pe("DecorationService");var Hs={trace:0,debug:1,info:2,warn:3,error:4,off:5},Ws="xterm.js: ",ci=class extends B{constructor(t){super();this._optionsService=t;this._logLevel=5;this._updateLogLevel(),this._register(this._optionsService.onSpecificOptionChange("logLevel",()=>this._updateLogLevel())),vr=this}get logLevel(){return this._logLevel}_updateLogLevel(){this._logLevel=Hs[this._optionsService.rawOptions.logLevel]}_evalLazyOptionalParams(t){for(let n=0;n<t.length;n++)typeof t[n]=="function"&&(t[n]=t[n]())}_log(t,n,s){this._evalLazyOptionalParams(s),t.call(console,(this._optionsService.options.logger?"":Ws)+n,...s)}trace(t,...n){this._logLevel<=0&&this._log(this._optionsService.options.logger?.trace.bind(this._optionsService.options.logger)??console.log,t,n)}debug(t,...n){this._logLevel<=1&&this._log(this._optionsService.options.logger?.debug.bind(this._optionsService.options.logger)??console.log,t,n)}info(t,...n){this._logLevel<=2&&this._log(this._optionsService.options.logger?.info.bind(this._optionsService.options.logger)??console.info,t,n)}warn(t,...n){this._logLevel<=3&&this._log(this._optionsService.options.logger?.warn.bind(this._optionsService.options.logger)??console.warn,t,n)}error(t,...n){this._logLevel<=4&&this._log(this._optionsService.options.logger?.error.bind(this._optionsService.options.logger)??console.error,t,n)}};ci=Yi([Qi(0,Tr)],ci);var vr;function gr(i){vr=i}var xr=class extends B{constructor(t){if(Ti&&hn()<16){let n={antialias:!1,depth:!1,preserveDrawingBuffer:!0};if(!document.createElement("canvas").getContext("webgl2",n))throw new Error("Webgl2 is only supported on Safari 16 and above")}super();this._preserveDrawingBuffer=t;this._onChangeTextureAtlas=this._register(new D);this.onChangeTextureAtlas=this._onChangeTextureAtlas.event;this._onAddTextureAtlasCanvas=this._register(new D);this.onAddTextureAtlasCanvas=this._onAddTextureAtlasCanvas.event;this._onRemoveTextureAtlasCanvas=this._register(new D);this.onRemoveTextureAtlasCanvas=this._onRemoveTextureAtlasCanvas.event;this._onContextLoss=this._register(new D);this.onContextLoss=this._onContextLoss.event}activate(t){let n=t._core;if(!t.element){this._register(n.onWillOpen(()=>this.activate(t)));return}this._terminal=t;let s=n.coreService,o=n.optionsService,r=n,a=r._renderService,l=r._characterJoinerService,u=r._charSizeService,c=r._coreBrowserService,d=r._decorationService,h=r._logService,f=r._themeService;gr(h),this._renderer=this._register(new ui(t,l,u,c,s,d,o,f,this._preserveDrawingBuffer)),this._register(ee.forward(this._renderer.onContextLoss,this._onContextLoss)),this._register(ee.forward(this._renderer.onChangeTextureAtlas,this._onChangeTextureAtlas)),this._register(ee.forward(this._renderer.onAddTextureAtlasCanvas,this._onAddTextureAtlasCanvas)),this._register(ee.forward(this._renderer.onRemoveTextureAtlasCanvas,this._onRemoveTextureAtlasCanvas)),a.setRenderer(this._renderer),this._register(O(()=>{if(this._terminal._core._store._isDisposed)return;let I=this._terminal._core._renderService;I.setRenderer(this._terminal._core._createRenderer()),I.handleResize(t.cols,t.rows)}))}get textureAtlas(){return this._renderer?.textureAtlas}clearTextureAtlas(){this._renderer?.clearTextureAtlas()}};export{xr as WebglAddon};
//# sourceMappingURL=addon-webgl.mjs.map
