{"name": "@xterm/headless", "description": "A headless terminal component that runs in Node.js", "version": "5.6.0-beta.107", "main": "lib-headless/xterm-headless.js", "module": "lib/xterm.mjs", "types": "typings/xterm-headless.d.ts", "repository": "https://github.com/xtermjs/xterm.js", "license": "MIT", "keywords": ["cli", "command-line", "console", "pty", "shell", "ssh", "styles", "terminal-emulator", "terminal", "tty", "vt100", "webgl", "xterm"], "commit": "e9c547c1c6b67e9f09c24ccc007e19305f536e60"}