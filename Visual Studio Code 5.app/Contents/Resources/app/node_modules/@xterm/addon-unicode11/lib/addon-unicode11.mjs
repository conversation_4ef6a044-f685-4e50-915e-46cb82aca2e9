/**
 * Copyright (c) 2014-2024 The xterm.js authors. All rights reserved.
 * @license MIT
 *
 * Copyright (c) 2012-2013, <PERSON> (MIT License)
 * @license MIT
 *
 * Originally forked from (with the author's permission):
 *   <PERSON><PERSON><PERSON>'s javascript vt100 for jslinux:
 *   http://bellard.org/jslinux/
 *   Copyright (c) 2011 F<PERSON><PERSON>
 */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var ue=[[768,879],[1155,1158],[1160,1161],[1425,1469],[1471,1471],[1473,1474],[1476,1477],[1479,1479],[1536,1539],[1552,1557],[1611,1630],[1648,1648],[1750,1764],[1767,1768],[1770,1773],[1807,1807],[1809,1809],[1840,1866],[1958,1968],[2027,2035],[2305,2306],[2364,2364],[2369,2376],[2381,2381],[2385,2388],[2402,2403],[2433,2433],[2492,2492],[2497,2500],[2509,2509],[2530,2531],[2561,2562],[2620,2620],[2625,2626],[2631,2632],[2635,2637],[2672,2673],[2689,2690],[2748,2748],[2753,2757],[2759,2760],[2765,2765],[2786,2787],[2817,2817],[2876,2876],[2879,2879],[2881,2883],[2893,2893],[2902,2902],[2946,2946],[3008,3008],[3021,3021],[3134,3136],[3142,3144],[3146,3149],[3157,3158],[3260,3260],[3263,3263],[3270,3270],[3276,3277],[3298,3299],[3393,3395],[3405,3405],[3530,3530],[3538,3540],[3542,3542],[3633,3633],[3636,3642],[3655,3662],[3761,3761],[3764,3769],[3771,3772],[3784,3789],[3864,3865],[3893,3893],[3895,3895],[3897,3897],[3953,3966],[3968,3972],[3974,3975],[3984,3991],[3993,4028],[4038,4038],[4141,4144],[4146,4146],[4150,4151],[4153,4153],[4184,4185],[4448,4607],[4959,4959],[5906,5908],[5938,5940],[5970,5971],[6002,6003],[6068,6069],[6071,6077],[6086,6086],[6089,6099],[6109,6109],[6155,6157],[6313,6313],[6432,6434],[6439,6440],[6450,6450],[6457,6459],[6679,6680],[6912,6915],[6964,6964],[6966,6970],[6972,6972],[6978,6978],[7019,7027],[7616,7626],[7678,7679],[8203,8207],[8234,8238],[8288,8291],[8298,8303],[8400,8431],[12330,12335],[12441,12442],[43014,43014],[43019,43019],[43045,43046],[64286,64286],[65024,65039],[65056,65059],[65279,65279],[65529,65531]],qe=[[68097,68099],[68101,68102],[68108,68111],[68152,68154],[68159,68159],[119143,119145],[119155,119170],[119173,119179],[119210,119213],[119362,119364],[917505,917505],[917536,917631],[917760,917999]],A;function He(r,e){let t=0,n=e.length-1,o;if(r<e[0][0]||r>e[n][1])return!1;for(;n>=t;)if(o=t+n>>1,r>e[o][1])t=o+1;else if(r<e[o][0])n=o-1;else return!0;return!1}var H=class{constructor(){this.version="6";if(!A){A=new Uint8Array(65536),A.fill(1),A[0]=0,A.fill(0,1,32),A.fill(0,127,160),A.fill(2,4352,4448),A[9001]=2,A[9002]=2,A.fill(2,11904,42192),A[12351]=1,A.fill(2,44032,55204),A.fill(2,63744,64256),A.fill(2,65040,65050),A.fill(2,65072,65136),A.fill(2,65280,65377),A.fill(2,65504,65511);for(let e=0;e<ue.length;++e)A.fill(0,ue[e][0],ue[e][1]+1)}}wcwidth(e){return e<32?0:e<127?1:e<65536?A[e]:He(e,qe)?0:e>=131072&&e<=196605||e>=196608&&e<=262141?2:1}charProperties(e,t){let n=this.wcwidth(e),o=n===0&&t!==0;if(o){let d=w.extractWidth(t);d===0?o=!1:d>n&&(n=d)}return w.createPropertyValue(0,n,o)}};var de=class{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout(()=>{throw e.stack?J.isErrorNoTelemetry(e)?new J(e.message+`

`+e.stack):new Error(e.message+`

`+e.stack):e},0)}}addListener(e){return this.listeners.push(e),()=>{this._removeListener(e)}}emit(e){this.listeners.forEach(t=>{t(e)})}_removeListener(e){this.listeners.splice(this.listeners.indexOf(e),1)}setUnexpectedErrorHandler(e){this.unexpectedErrorHandler=e}getUnexpectedErrorHandler(){return this.unexpectedErrorHandler}onUnexpectedError(e){this.unexpectedErrorHandler(e),this.emit(e)}onUnexpectedExternalError(e){this.unexpectedErrorHandler(e)}},Ge=new de;function Y(r){Je(r)||Ge.onUnexpectedError(r)}var ce="Canceled";function Je(r){return r instanceof G?!0:r instanceof Error&&r.name===ce&&r.message===ce}var G=class extends Error{constructor(){super(ce),this.name=this.message}};var J=class r extends Error{constructor(e){super(e),this.name="CodeExpectedError"}static fromError(e){if(e instanceof r)return e;let t=new r;return t.message=e.message,t.stack=e.stack,t}static isErrorNoTelemetry(e){return e.name==="CodeExpectedError"}};function pe(r,e){let t=this,n=!1,o;return function(){if(n)return o;if(n=!0,e)try{o=r.apply(t,arguments)}finally{e()}else o=r.apply(t,arguments);return o}}function Ye(r,e,t=0,n=r.length){let o=t,d=n;for(;o<d;){let v=Math.floor((o+d)/2);e(r[v])?o=v+1:d=v}return o-1}var X=class X{constructor(e){this._array=e;this._findLastMonotonousLastIdx=0}findLastMonotonous(e){if(X.assertInvariants){if(this._prevFindLastPredicate){for(let n of this._array)if(this._prevFindLastPredicate(n)&&!e(n))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this._prevFindLastPredicate=e}let t=Ye(this._array,e,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=t+1,t===-1?void 0:this._array[t]}};X.assertInvariants=!1;var _e=X;var Be;(E=>{function r(p){return p<0}E.isLessThan=r;function e(p){return p<=0}E.isLessThanOrEqual=e;function t(p){return p>0}E.isGreaterThan=t;function n(p){return p===0}E.isNeitherLessOrGreaterThan=n,E.greaterThan=1,E.lessThan=-1,E.neitherLessOrGreaterThan=0})(Be||={});function we(r,e){return(t,n)=>e(r(t),r(n))}var ke=(r,e)=>r-e;var R=class R{constructor(e){this.iterate=e}forEach(e){this.iterate(t=>(e(t),!0))}toArray(){let e=[];return this.iterate(t=>(e.push(t),!0)),e}filter(e){return new R(t=>this.iterate(n=>e(n)?t(n):!0))}map(e){return new R(t=>this.iterate(n=>t(e(n))))}some(e){let t=!1;return this.iterate(n=>(t=e(n),!t)),t}findFirst(e){let t;return this.iterate(n=>e(n)?(t=n,!1):!0),t}findLast(e){let t;return this.iterate(n=>(e(n)&&(t=n),!0)),t}findLastMaxBy(e){let t,n=!0;return this.iterate(o=>((n||Be.isGreaterThan(e(o,t)))&&(n=!1,t=o),!0)),t}};R.empty=new R(e=>{});var Ie=R;function Oe(r,e){let t=Object.create(null);for(let n of r){let o=e(n),d=t[o];d||(d=t[o]=[]),d.push(n)}return t}var Se,Re,Le=class{constructor(e,t){this.toKey=t;this._map=new Map;this[Se]="SetWithKey";for(let n of e)this.add(n)}get size(){return this._map.size}add(e){let t=this.toKey(e);return this._map.set(t,e),this}delete(e){return this._map.delete(this.toKey(e))}has(e){return this._map.has(this.toKey(e))}*entries(){for(let e of this._map.values())yield[e,e]}keys(){return this.values()}*values(){for(let e of this._map.values())yield e}clear(){this._map.clear()}forEach(e,t){this._map.forEach(n=>e.call(t,n,n,this))}[(Re=Symbol.iterator,Se=Symbol.toStringTag,Re)](){return this.values()}};var Z=class{constructor(){this.map=new Map}add(e,t){let n=this.map.get(e);n||(n=new Set,this.map.set(e,n)),n.add(t)}delete(e,t){let n=this.map.get(e);n&&(n.delete(t),n.size===0&&this.map.delete(e))}forEach(e,t){let n=this.map.get(e);n&&n.forEach(t)}get(e){let t=this.map.get(e);return t||new Set}};var fe;(le=>{function r(u){return u&&typeof u=="object"&&typeof u[Symbol.iterator]=="function"}le.is=r;let e=Object.freeze([]);function t(){return e}le.empty=t;function*n(u){yield u}le.single=n;function o(u){return r(u)?u:n(u)}le.wrap=o;function d(u){return u||e}le.from=d;function*v(u){for(let f=u.length-1;f>=0;f--)yield u[f]}le.reverse=v;function E(u){return!u||u[Symbol.iterator]().next().done===!0}le.isEmpty=E;function p(u){return u[Symbol.iterator]().next().value}le.first=p;function b(u,f){let m=0;for(let g of u)if(f(g,m++))return!0;return!1}le.some=b;function D(u,f){for(let m of u)if(f(m))return m}le.find=D;function*T(u,f){for(let m of u)f(m)&&(yield m)}le.filter=T;function*B(u,f){let m=0;for(let g of u)yield f(g,m++)}le.map=B;function*L(u,f){let m=0;for(let g of u)yield*f(g,m++)}le.flatMap=L;function*oe(...u){for(let f of u)yield*f}le.concat=oe;function z(u,f,m){let g=m;for(let W of u)g=f(g,W);return g}le.reduce=z;function*k(u,f,m=u.length){for(f<0&&(f+=u.length),m<0?m+=u.length:m>u.length&&(m=u.length);f<m;f++)yield u[f]}le.slice=k;function ae(u,f=Number.POSITIVE_INFINITY){let m=[];if(f===0)return[m,u];let g=u[Symbol.iterator]();for(let W=0;W<f;W++){let xe=g.next();if(xe.done)return[m,le.empty()];m.push(xe.value)}return[m,{[Symbol.iterator](){return g}}]}le.consume=ae;async function V(u){let f=[];for await(let m of u)f.push(m);return Promise.resolve(f)}le.asyncToArray=V})(fe||={});var Xe=!1,O=null,ee=class ee{constructor(){this.livingDisposables=new Map}getDisposableData(e){let t=this.livingDisposables.get(e);return t||(t={parent:null,source:null,isSingleton:!1,value:e,idx:ee.idx++},this.livingDisposables.set(e,t)),t}trackDisposable(e){let t=this.getDisposableData(e);t.source||(t.source=new Error().stack)}setParent(e,t){let n=this.getDisposableData(e);n.parent=t}markAsDisposed(e){this.livingDisposables.delete(e)}markAsSingleton(e){this.getDisposableData(e).isSingleton=!0}getRootParent(e,t){let n=t.get(e);if(n)return n;let o=e.parent?this.getRootParent(this.getDisposableData(e.parent),t):e;return t.set(e,o),o}getTrackedDisposables(){let e=new Map;return[...this.livingDisposables.entries()].filter(([,n])=>n.source!==null&&!this.getRootParent(n,e).isSingleton).flatMap(([n])=>n)}computeLeakingDisposables(e=10,t){let n;if(t)n=t;else{let p=new Map,b=[...this.livingDisposables.values()].filter(T=>T.source!==null&&!this.getRootParent(T,p).isSingleton);if(b.length===0)return;let D=new Set(b.map(T=>T.value));if(n=b.filter(T=>!(T.parent&&D.has(T.parent))),n.length===0)throw new Error("There are cyclic diposable chains!")}if(!n)return;function o(p){function b(T,B){for(;T.length>0&&B.some(L=>typeof L=="string"?L===T[0]:T[0].match(L));)T.shift()}let D=p.source.split(`
`).map(T=>T.trim().replace("at ","")).filter(T=>T!=="");return b(D,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),D.reverse()}let d=new Z;for(let p of n){let b=o(p);for(let D=0;D<=b.length;D++)d.add(b.slice(0,D).join(`
`),p)}n.sort(we(p=>p.idx,ke));let v="",E=0;for(let p of n.slice(0,e)){E++;let b=o(p),D=[];for(let T=0;T<b.length;T++){let B=b[T];B=`(shared with ${d.get(b.slice(0,T+1).join(`
`)).size}/${n.length} leaks) at ${B}`;let oe=d.get(b.slice(0,T).join(`
`)),z=Oe([...oe].map(k=>o(k)[T]),k=>k);delete z[b[T]];for(let[k,ae]of Object.entries(z))D.unshift(`    - stacktraces of ${ae.length} other leaks continue with ${k}`);D.unshift(B)}v+=`


==================== Leaking disposable ${E}/${n.length}: ${p.value.constructor.name} ====================
${D.join(`
`)}
============================================================

`}return n.length>e&&(v+=`


... and ${n.length-e} more leaking disposables

`),{leaks:n,details:v}}};ee.idx=0;var Ue=ee;function Ze(r){O=r}if(Xe){let r="__is_disposable_tracked__";Ze(new class{trackDisposable(e){let t=new Error("Potentially leaked disposable").stack;setTimeout(()=>{e[r]||console.log(t)},3e3)}setParent(e,t){if(e&&e!==_.None)try{e[r]=!0}catch{}}markAsDisposed(e){if(e&&e!==_.None)try{e[r]=!0}catch{}}markAsSingleton(e){}})}function Te(r){return O?.trackDisposable(r),r}function ve(r){O?.markAsDisposed(r)}function he(r,e){O?.setParent(r,e)}function et(r,e){if(O)for(let t of r)O.setParent(t,e)}function Pe(r){if(fe.is(r)){let e=[];for(let t of r)if(t)try{t.dispose()}catch(n){e.push(n)}if(e.length===1)throw e[0];if(e.length>1)throw new AggregateError(e,"Encountered errors while disposing of store");return Array.isArray(r)?[]:r}else if(r)return r.dispose(),r}function Me(...r){let e=me(()=>Pe(r));return et(r,e),e}function me(r){let e=Te({dispose:pe(()=>{ve(e),r()})});return e}var te=class te{constructor(){this._toDispose=new Set;this._isDisposed=!1;Te(this)}dispose(){this._isDisposed||(ve(this),this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(this._toDispose.size!==0)try{Pe(this._toDispose)}finally{this._toDispose.clear()}}add(e){if(!e)return e;if(e===this)throw new Error("Cannot register a disposable on itself!");return he(e,this),this._isDisposed?te.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(e),e}delete(e){if(e){if(e===this)throw new Error("Cannot dispose a disposable on itself!");this._toDispose.delete(e),e.dispose()}}deleteAndLeak(e){e&&this._toDispose.has(e)&&(this._toDispose.delete(e),he(e,null))}};te.DISABLE_DISPOSED_WARNING=!1;var U=te,_=class{constructor(){this._store=new U;Te(this),he(this._store,this)}dispose(){ve(this),this._store.dispose()}_register(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(e)}};_.None=Object.freeze({dispose(){}});var P=class P{constructor(e){this.element=e,this.next=P.Undefined,this.prev=P.Undefined}};P.Undefined=new P(void 0);var Ne=P;var tt=globalThis.performance&&typeof globalThis.performance.now=="function",ne=class r{static create(e){return new r(e)}constructor(e){this._now=tt&&e===!1?Date.now:globalThis.performance.now.bind(globalThis.performance),this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}reset(){this._startTime=this._now(),this._stopTime=-1}elapsed(){return this._stopTime!==-1?this._stopTime-this._startTime:this._now()-this._startTime}};var nt=!1,Ve=!1,rt=!1,it;(Q=>{Q.None=()=>_.None;function e(l){if(rt){let{onDidAddListener:i}=l,a=K.create(),s=0;l.onDidAddListener=()=>{++s===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),a.print()),i?.()}}}function t(l,i){return B(l,()=>{},0,void 0,!0,void 0,i)}Q.defer=t;function n(l){return(i,a=null,s)=>{let x=!1,c;return c=l(h=>{if(!x)return c?c.dispose():x=!0,i.call(a,h)},null,s),x&&c.dispose(),c}}Q.once=n;function o(l,i,a){return D((s,x=null,c)=>l(h=>s.call(x,i(h)),null,c),a)}Q.map=o;function d(l,i,a){return D((s,x=null,c)=>l(h=>{i(h),s.call(x,h)},null,c),a)}Q.forEach=d;function v(l,i,a){return D((s,x=null,c)=>l(h=>i(h)&&s.call(x,h),null,c),a)}Q.filter=v;function E(l){return l}Q.signal=E;function p(...l){return(i,a=null,s)=>{let x=Me(...l.map(c=>c(h=>i.call(a,h))));return T(x,s)}}Q.any=p;function b(l,i,a,s){let x=a;return o(l,c=>(x=i(x,c),x),s)}Q.reduce=b;function D(l,i){let a,s={onWillAddFirstListener(){a=l(x.fire,x)},onDidRemoveLastListener(){a?.dispose()}};i||e(s);let x=new C(s);return i?.add(x),x.event}function T(l,i){return i instanceof Array?i.push(l):i&&i.add(l),l}function B(l,i,a=100,s=!1,x=!1,c,h){let F,y,S,$=0,j,Ce={leakWarningThreshold:c,onWillAddFirstListener(){F=l(Qe=>{$++,y=i(y,Qe),s&&!S&&(q.fire(y),y=void 0),j=()=>{let $e=y;y=void 0,S=void 0,(!s||$>1)&&q.fire($e),$=0},typeof a=="number"?(clearTimeout(S),S=setTimeout(j,a)):S===void 0&&(S=0,queueMicrotask(j))})},onWillRemoveListener(){x&&$>0&&j?.()},onDidRemoveLastListener(){j=void 0,F.dispose()}};h||e(Ce);let q=new C(Ce);return h?.add(q),q.event}Q.debounce=B;function L(l,i=0,a){return Q.debounce(l,(s,x)=>s?(s.push(x),s):[x],i,void 0,!0,void 0,a)}Q.accumulate=L;function oe(l,i=(s,x)=>s===x,a){let s=!0,x;return v(l,c=>{let h=s||!i(c,x);return s=!1,x=c,h},a)}Q.latch=oe;function z(l,i,a){return[Q.filter(l,i,a),Q.filter(l,s=>!i(s),a)]}Q.split=z;function k(l,i=!1,a=[],s){let x=a.slice(),c=l(y=>{x?x.push(y):F.fire(y)});s&&s.add(c);let h=()=>{x?.forEach(y=>F.fire(y)),x=null},F=new C({onWillAddFirstListener(){c||(c=l(y=>F.fire(y)),s&&s.add(c))},onDidAddFirstListener(){x&&(i?setTimeout(h):h())},onDidRemoveLastListener(){c&&c.dispose(),c=null}});return s&&s.add(F),F.event}Q.buffer=k;function ae(l,i){return(s,x,c)=>{let h=i(new le);return l(function(F){let y=h.evaluate(F);y!==V&&s.call(x,y)},void 0,c)}}Q.chain=ae;let V=Symbol("HaltChainable");class le{constructor(){this.steps=[]}map(i){return this.steps.push(i),this}forEach(i){return this.steps.push(a=>(i(a),a)),this}filter(i){return this.steps.push(a=>i(a)?a:V),this}reduce(i,a){let s=a;return this.steps.push(x=>(s=i(s,x),s)),this}latch(i=(a,s)=>a===s){let a=!0,s;return this.steps.push(x=>{let c=a||!i(x,s);return a=!1,s=x,c?x:V}),this}evaluate(i){for(let a of this.steps)if(i=a(i),i===V)break;return i}}function u(l,i,a=s=>s){let s=(...F)=>h.fire(a(...F)),x=()=>l.on(i,s),c=()=>l.removeListener(i,s),h=new C({onWillAddFirstListener:x,onDidRemoveLastListener:c});return h.event}Q.fromNodeEventEmitter=u;function f(l,i,a=s=>s){let s=(...F)=>h.fire(a(...F)),x=()=>l.addEventListener(i,s),c=()=>l.removeEventListener(i,s),h=new C({onWillAddFirstListener:x,onDidRemoveLastListener:c});return h.event}Q.fromDOMEventEmitter=f;function m(l){return new Promise(i=>n(l)(i))}Q.toPromise=m;function g(l){let i=new C;return l.then(a=>{i.fire(a)},()=>{i.fire(void 0)}).finally(()=>{i.dispose()}),i.event}Q.fromPromise=g;function W(l,i){return l(a=>i.fire(a))}Q.forward=W;function xe(l,i,a){return i(a),l(s=>i(s))}Q.runAndSubscribe=xe;class ze{constructor(i,a){this._observable=i;this._counter=0;this._hasChanged=!1;let s={onWillAddFirstListener:()=>{i.addObserver(this)},onDidRemoveLastListener:()=>{i.removeObserver(this)}};a||e(s),this.emitter=new C(s),a&&a.add(this.emitter)}beginUpdate(i){this._counter++}handlePossibleChange(i){}handleChange(i,a){this._hasChanged=!0}endUpdate(i){this._counter--,this._counter===0&&(this._observable.reportChanges(),this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this._observable.get())))}}function ut(l,i){return new ze(l,i).emitter.event}Q.fromObservable=ut;function dt(l){return(i,a,s)=>{let x=0,c=!1,h={beginUpdate(){x++},endUpdate(){x--,x===0&&(l.reportChanges(),c&&(c=!1,i.call(a)))},handlePossibleChange(){},handleChange(){c=!0}};l.addObserver(h),l.reportChanges();let F={dispose(){l.removeObserver(h)}};return s instanceof U?s.add(F):Array.isArray(s)&&s.push(F),F}}Q.fromObservableLight=dt})(it||={});var M=class M{constructor(e){this.listenerCount=0;this.invocationCount=0;this.elapsedOverall=0;this.durations=[];this.name=`${e}_${M._idPool++}`,M.all.add(this)}start(e){this._stopWatch=new ne,this.listenerCount=e}stop(){if(this._stopWatch){let e=this._stopWatch.elapsed();this.durations.push(e),this.elapsedOverall+=e,this.invocationCount+=1,this._stopWatch=void 0}}};M.all=new Set,M._idPool=0;var be=M,We=-1;var ie=class ie{constructor(e,t,n=(ie._idPool++).toString(16).padStart(3,"0")){this._errorHandler=e;this.threshold=t;this.name=n;this._warnCountdown=0}dispose(){this._stacks?.clear()}check(e,t){let n=this.threshold;if(n<=0||t<n)return;this._stacks||(this._stacks=new Map);let o=this._stacks.get(e.value)||0;if(this._stacks.set(e.value,o+1),this._warnCountdown-=1,this._warnCountdown<=0){this._warnCountdown=n*.5;let[d,v]=this.getMostFrequentStack(),E=`[${this.name}] potential listener LEAK detected, having ${t} listeners already. MOST frequent listener (${v}):`;console.warn(E),console.warn(d);let p=new De(E,d);this._errorHandler(p)}return()=>{let d=this._stacks.get(e.value)||0;this._stacks.set(e.value,d-1)}}getMostFrequentStack(){if(!this._stacks)return;let e,t=0;for(let[n,o]of this._stacks)(!e||t<o)&&(e=[n,o],t=o);return e}};ie._idPool=1;var Ee=ie,K=class r{constructor(e){this.value=e}static create(){let e=new Error;return new r(e.stack??"")}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},De=class extends Error{constructor(e,t){super(e),this.name="ListenerLeakError",this.stack=t}},Ae=class extends Error{constructor(e,t){super(e),this.name="ListenerRefusalError",this.stack=t}},st=0,N=class{constructor(e){this.value=e;this.id=st++}},ot=2,at=(r,e)=>{if(r instanceof N)e(r);else for(let t=0;t<r.length;t++){let n=r[t];n&&e(n)}},re;if(nt){let r=[];setInterval(()=>{r.length!==0&&(console.warn("[LEAKING LISTENERS] GC'ed these listeners that were NOT yet disposed:"),console.warn(r.join(`
`)),r.length=0)},3e3),re=new FinalizationRegistry(e=>{typeof e=="string"&&r.push(e)})}var C=class{constructor(e){this._size=0;this._options=e,this._leakageMon=We>0||this._options?.leakWarningThreshold?new Ee(e?.onListenerError??Y,this._options?.leakWarningThreshold??We):void 0,this._perfMon=this._options?._profName?new be(this._options._profName):void 0,this._deliveryQueue=this._options?.deliveryQueue}dispose(){if(!this._disposed){if(this._disposed=!0,this._deliveryQueue?.current===this&&this._deliveryQueue.reset(),this._listeners){if(Ve){let e=this._listeners;queueMicrotask(()=>{at(e,t=>t.stack?.print())})}this._listeners=void 0,this._size=0}this._options?.onDidRemoveLastListener?.(),this._leakageMon?.dispose()}}get event(){return this._event??=(e,t,n)=>{if(this._leakageMon&&this._size>this._leakageMon.threshold**2){let p=`[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this._size} vs ${this._leakageMon.threshold})`;console.warn(p);let b=this._leakageMon.getMostFrequentStack()??["UNKNOWN stack",-1],D=new Ae(`${p}. HINT: Stack shows most frequent listener (${b[1]}-times)`,b[0]);return(this._options?.onListenerError||Y)(D),_.None}if(this._disposed)return _.None;t&&(e=e.bind(t));let o=new N(e),d,v;this._leakageMon&&this._size>=Math.ceil(this._leakageMon.threshold*.2)&&(o.stack=K.create(),d=this._leakageMon.check(o.stack,this._size+1)),Ve&&(o.stack=v??K.create()),this._listeners?this._listeners instanceof N?(this._deliveryQueue??=new Fe,this._listeners=[this._listeners,o]):this._listeners.push(o):(this._options?.onWillAddFirstListener?.(this),this._listeners=o,this._options?.onDidAddFirstListener?.(this)),this._size++;let E=me(()=>{re?.unregister(E),d?.(),this._removeListener(o)});if(n instanceof U?n.add(E):Array.isArray(n)&&n.push(E),re){let p=new Error().stack.split(`
`).slice(2,3).join(`
`).trim(),b=/(file:|vscode-file:\/\/vscode-app)?(\/[^:]*:\d+:\d+)/.exec(p);re.register(E,b?.[2]??p,E)}return E},this._event}_removeListener(e){if(this._options?.onWillRemoveListener?.(this),!this._listeners)return;if(this._size===1){this._listeners=void 0,this._options?.onDidRemoveLastListener?.(this),this._size=0;return}let t=this._listeners,n=t.indexOf(e);if(n===-1)throw console.log("disposed?",this._disposed),console.log("size?",this._size),console.log("arr?",JSON.stringify(this._listeners)),new Error("Attempted to dispose unknown listener");this._size--,t[n]=void 0;let o=this._deliveryQueue.current===this;if(this._size*ot<=t.length){let d=0;for(let v=0;v<t.length;v++)t[v]?t[d++]=t[v]:o&&(this._deliveryQueue.end--,d<this._deliveryQueue.i&&this._deliveryQueue.i--);t.length=d}}_deliver(e,t){if(!e)return;let n=this._options?.onListenerError||Y;if(!n){e.value(t);return}try{e.value(t)}catch(o){n(o)}}_deliverQueue(e){let t=e.current._listeners;for(;e.i<e.end;)this._deliver(t[e.i++],e.value);e.reset()}fire(e){if(this._deliveryQueue?.current&&(this._deliverQueue(this._deliveryQueue),this._perfMon?.stop()),this._perfMon?.start(this._size),this._listeners)if(this._listeners instanceof N)this._deliver(this._listeners,e);else{let t=this._deliveryQueue;t.enqueue(this,e,this._listeners.length),this._deliverQueue(t)}this._perfMon?.stop()}hasListeners(){return this._size>0}};var Fe=class{constructor(){this.i=-1;this.end=0}enqueue(e,t,n){this.i=0,this.end=n,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}};var w=class r{constructor(){this._providers=Object.create(null);this._active="";this._onChange=new C;this.onChange=this._onChange.event;let e=new H;this.register(e),this._active=e.version,this._activeProvider=e}static extractShouldJoin(e){return(e&1)!==0}static extractWidth(e){return e>>1&3}static extractCharKind(e){return e>>3}static createPropertyValue(e,t,n=!1){return(e&16777215)<<3|(t&3)<<1|(n?1:0)}dispose(){this._onChange.dispose()}get versions(){return Object.keys(this._providers)}get activeVersion(){return this._active}set activeVersion(e){if(!this._providers[e])throw new Error(`unknown Unicode version "${e}"`);this._active=e,this._activeProvider=this._providers[e],this._onChange.fire(e)}register(e){this._providers[e.version]=e}wcwidth(e){return this._activeProvider.wcwidth(e)}getStringCellWidth(e){let t=0,n=0,o=e.length;for(let d=0;d<o;++d){let v=e.charCodeAt(d);if(55296<=v&&v<=56319){if(++d>=o)return t+this.wcwidth(v);let b=e.charCodeAt(d);56320<=b&&b<=57343?v=(v-55296)*1024+b-56320+65536:t+=this.wcwidth(b)}let E=this.charProperties(v,n),p=r.extractWidth(E);r.extractShouldJoin(E)&&(p-=r.extractWidth(n)),t+=p,n=E}return t}charProperties(e,t){return this._activeProvider.charProperties(e,t)}};var ye=[[768,879],[1155,1161],[1425,1469],[1471,1471],[1473,1474],[1476,1477],[1479,1479],[1536,1541],[1552,1562],[1564,1564],[1611,1631],[1648,1648],[1750,1757],[1759,1764],[1767,1768],[1770,1773],[1807,1807],[1809,1809],[1840,1866],[1958,1968],[2027,2035],[2045,2045],[2070,2073],[2075,2083],[2085,2087],[2089,2093],[2137,2139],[2259,2306],[2362,2362],[2364,2364],[2369,2376],[2381,2381],[2385,2391],[2402,2403],[2433,2433],[2492,2492],[2497,2500],[2509,2509],[2530,2531],[2558,2558],[2561,2562],[2620,2620],[2625,2626],[2631,2632],[2635,2637],[2641,2641],[2672,2673],[2677,2677],[2689,2690],[2748,2748],[2753,2757],[2759,2760],[2765,2765],[2786,2787],[2810,2815],[2817,2817],[2876,2876],[2879,2879],[2881,2884],[2893,2893],[2902,2902],[2914,2915],[2946,2946],[3008,3008],[3021,3021],[3072,3072],[3076,3076],[3134,3136],[3142,3144],[3146,3149],[3157,3158],[3170,3171],[3201,3201],[3260,3260],[3263,3263],[3270,3270],[3276,3277],[3298,3299],[3328,3329],[3387,3388],[3393,3396],[3405,3405],[3426,3427],[3530,3530],[3538,3540],[3542,3542],[3633,3633],[3636,3642],[3655,3662],[3761,3761],[3764,3772],[3784,3789],[3864,3865],[3893,3893],[3895,3895],[3897,3897],[3953,3966],[3968,3972],[3974,3975],[3981,3991],[3993,4028],[4038,4038],[4141,4144],[4146,4151],[4153,4154],[4157,4158],[4184,4185],[4190,4192],[4209,4212],[4226,4226],[4229,4230],[4237,4237],[4253,4253],[4448,4607],[4957,4959],[5906,5908],[5938,5940],[5970,5971],[6002,6003],[6068,6069],[6071,6077],[6086,6086],[6089,6099],[6109,6109],[6155,6158],[6277,6278],[6313,6313],[6432,6434],[6439,6440],[6450,6450],[6457,6459],[6679,6680],[6683,6683],[6742,6742],[6744,6750],[6752,6752],[6754,6754],[6757,6764],[6771,6780],[6783,6783],[6832,6846],[6912,6915],[6964,6964],[6966,6970],[6972,6972],[6978,6978],[7019,7027],[7040,7041],[7074,7077],[7080,7081],[7083,7085],[7142,7142],[7144,7145],[7149,7149],[7151,7153],[7212,7219],[7222,7223],[7376,7378],[7380,7392],[7394,7400],[7405,7405],[7412,7412],[7416,7417],[7616,7673],[7675,7679],[8203,8207],[8234,8238],[8288,8292],[8294,8303],[8400,8432],[11503,11505],[11647,11647],[11744,11775],[12330,12333],[12441,12442],[42607,42610],[42612,42621],[42654,42655],[42736,42737],[43010,43010],[43014,43014],[43019,43019],[43045,43046],[43204,43205],[43232,43249],[43263,43263],[43302,43309],[43335,43345],[43392,43394],[43443,43443],[43446,43449],[43452,43453],[43493,43493],[43561,43566],[43569,43570],[43573,43574],[43587,43587],[43596,43596],[43644,43644],[43696,43696],[43698,43700],[43703,43704],[43710,43711],[43713,43713],[43756,43757],[43766,43766],[44005,44005],[44008,44008],[44013,44013],[64286,64286],[65024,65039],[65056,65071],[65279,65279],[65529,65531]],lt=[[66045,66045],[66272,66272],[66422,66426],[68097,68099],[68101,68102],[68108,68111],[68152,68154],[68159,68159],[68325,68326],[68900,68903],[69446,69456],[69633,69633],[69688,69702],[69759,69761],[69811,69814],[69817,69818],[69821,69821],[69837,69837],[69888,69890],[69927,69931],[69933,69940],[70003,70003],[70016,70017],[70070,70078],[70089,70092],[70191,70193],[70196,70196],[70198,70199],[70206,70206],[70367,70367],[70371,70378],[70400,70401],[70459,70460],[70464,70464],[70502,70508],[70512,70516],[70712,70719],[70722,70724],[70726,70726],[70750,70750],[70835,70840],[70842,70842],[70847,70848],[70850,70851],[71090,71093],[71100,71101],[71103,71104],[71132,71133],[71219,71226],[71229,71229],[71231,71232],[71339,71339],[71341,71341],[71344,71349],[71351,71351],[71453,71455],[71458,71461],[71463,71467],[71727,71735],[71737,71738],[72148,72151],[72154,72155],[72160,72160],[72193,72202],[72243,72248],[72251,72254],[72263,72263],[72273,72278],[72281,72283],[72330,72342],[72344,72345],[72752,72758],[72760,72765],[72767,72767],[72850,72871],[72874,72880],[72882,72883],[72885,72886],[73009,73014],[73018,73018],[73020,73021],[73023,73029],[73031,73031],[73104,73105],[73109,73109],[73111,73111],[73459,73460],[78896,78904],[92912,92916],[92976,92982],[94031,94031],[94095,94098],[113821,113822],[113824,113827],[119143,119145],[119155,119170],[119173,119179],[119210,119213],[119362,119364],[121344,121398],[121403,121452],[121461,121461],[121476,121476],[121499,121503],[121505,121519],[122880,122886],[122888,122904],[122907,122913],[122915,122916],[122918,122922],[123184,123190],[123628,123631],[125136,125142],[125252,125258],[917505,917505],[917536,917631],[917760,917999]],ge=[[4352,4447],[8986,8987],[9001,9002],[9193,9196],[9200,9200],[9203,9203],[9725,9726],[9748,9749],[9800,9811],[9855,9855],[9875,9875],[9889,9889],[9898,9899],[9917,9918],[9924,9925],[9934,9934],[9940,9940],[9962,9962],[9970,9971],[9973,9973],[9978,9978],[9981,9981],[9989,9989],[9994,9995],[10024,10024],[10060,10060],[10062,10062],[10067,10069],[10071,10071],[10133,10135],[10160,10160],[10175,10175],[11035,11036],[11088,11088],[11093,11093],[11904,11929],[11931,12019],[12032,12245],[12272,12283],[12288,12329],[12334,12350],[12353,12438],[12443,12543],[12549,12591],[12593,12686],[12688,12730],[12736,12771],[12784,12830],[12832,12871],[12880,19903],[19968,42124],[42128,42182],[43360,43388],[44032,55203],[63744,64255],[65040,65049],[65072,65106],[65108,65126],[65128,65131],[65281,65376],[65504,65510]],xt=[[94176,94179],[94208,100343],[100352,101106],[110592,110878],[110928,110930],[110948,110951],[110960,111355],[126980,126980],[127183,127183],[127374,127374],[127377,127386],[127488,127490],[127504,127547],[127552,127560],[127568,127569],[127584,127589],[127744,127776],[127789,127797],[127799,127868],[127870,127891],[127904,127946],[127951,127955],[127968,127984],[127988,127988],[127992,128062],[128064,128064],[128066,128252],[128255,128317],[128331,128334],[128336,128359],[128378,128378],[128405,128406],[128420,128420],[128507,128591],[128640,128709],[128716,128716],[128720,128722],[128725,128725],[128747,128748],[128756,128762],[128992,129003],[129293,129393],[129395,129398],[129402,129442],[129445,129450],[129454,129482],[129485,129535],[129648,129651],[129656,129658],[129664,129666],[129680,129685],[131072,196605],[196608,262141]],I;function je(r,e){let t=0,n=e.length-1,o;if(r<e[0][0]||r>e[n][1])return!1;for(;n>=t;)if(o=t+n>>1,r>e[o][1])t=o+1;else if(r<e[o][0])n=o-1;else return!0;return!1}var se=class{constructor(){this.version="11";if(!I){I=new Uint8Array(65536),I.fill(1),I[0]=0,I.fill(0,1,32),I.fill(0,127,160);for(let e=0;e<ye.length;++e)I.fill(0,ye[e][0],ye[e][1]+1);for(let e=0;e<ge.length;++e)I.fill(2,ge[e][0],ge[e][1]+1)}}wcwidth(e){return e<32?0:e<127?1:e<65536?I[e]:je(e,lt)?0:je(e,xt)?2:1}charProperties(e,t){let n=this.wcwidth(e),o=n===0&&t!==0;if(o){let d=w.extractWidth(t);d===0?o=!1:d>n&&(n=d)}return w.createPropertyValue(0,n,o)}};var Ke=class{activate(e){e.unicode.register(new se)}dispose(){}};export{Ke as Unicode11Addon};
//# sourceMappingURL=addon-unicode11.mjs.map
