{"name": "@vscode/sqlite3", "description": "Asynchronous, non-blocking SQLite3 bindings", "version": "5.1.8-vscode", "homepage": "https://github.com/microsoft/vscode-node-sqlite3", "author": {"name": "Mapbox", "url": "https://mapbox.com/"}, "binary": {"module_name": "node_sqlite3", "module_path": "./lib/binding/napi-v{napi_build_version}-{platform}-{libc}-{arch}", "host": "https://github.com/TryGhost/node-sqlite3/releases/download/", "remote_path": "v{version}", "package_name": "napi-v{napi_build_version}-{platform}-{libc}-{arch}.tar.gz", "napi_versions": [3, 6]}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <e<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <mrjj<PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON>", "<PERSON> <<EMAIL>>"], "files": ["binding.gyp", "deps/", "lib/*.js", "lib/*.d.ts", "src/"], "repository": {"type": "git", "url": "https://github.com/microsoft/vscode-node-sqlite3.git"}, "dependencies": {"node-addon-api": "^8.2.0", "tar": "^6.1.11"}, "devDependencies": {"electron": "32.1.2", "eslint": "^7.32.0", "mocha": "7.2.0"}, "scripts": {"pretest": "node test/support/createdb.js", "test": "mocha -R spec --timeout 480000", "pack": "node-pre-gyp package"}, "license": "BSD-3-<PERSON><PERSON>", "keywords": ["sql", "sqlite", "sqlite3", "database"], "main": "./lib/sqlite3", "types": "./lib/sqlite3.d.ts", "renovate": {"extends": ["@tryghost:base"]}}