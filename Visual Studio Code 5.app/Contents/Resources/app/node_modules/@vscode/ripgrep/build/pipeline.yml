name: $(Date:yyyyMMdd)$(Rev:.r)

trigger:
  branches:
    include:
      - main
pr: none

resources:
  repositories:
    - repository: templates
      type: github
      name: microsoft/vscode-engineering
      ref: main
      endpoint: Monaco

parameters:
  - name: publishPackage
    displayName: 🚀 Publish @vscode/ripgrep
    type: boolean
    default: false

extends:
  template: azure-pipelines/npm-package/pipeline.yml@templates
  parameters:
    npmPackages:
      - name: ripgrep

        buildPlatforms:
          - name: Linux
            nodeVersions:
              - 18.x
          - name: MacOS
            nodeVersions:
              - 18.x
          - name: Windows
            nodeVersions:
              - 18.x

        buildSteps:
          - task: AzureKeyVault@2
            displayName: "Azure Key Vault: Get Secrets"
            inputs:
              azureSubscription: "vscode-oss-build-secrets"
              KeyVaultName: "vscode-oss-build-secrets"
              SecretsFilter: "github-token-code-oss"
          - script: npm i
            displayName: Install dependencies
            env:
              GITHUB_TOKEN: $(github-token-code-oss)

        publishPackage: ${{ parameters.publishPackage }}