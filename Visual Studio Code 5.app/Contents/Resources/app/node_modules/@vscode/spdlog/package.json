{"name": "@vscode/spdlog", "version": "0.15.2", "description": "Node bindings for spdlog", "main": "index.js", "types": "index.d.ts", "mocha": {"ui": "tdd"}, "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/node-spdlog.git"}, "keywords": ["node", "spdlog", "logging", "logger", "log"], "author": "Microsoft", "license": "MIT", "bugs": {"url": "https://github.com/microsoft/node-spdlog/issues"}, "homepage": "https://github.com/microsoft/node-spdlog#readme", "dependencies": {"bindings": "^1.5.0", "mkdirp": "^1.0.4", "node-addon-api": "7.1.0"}, "devDependencies": {"@types/mocha": "^10.0.0", "@types/node": "^16.0.0", "mocha": "^10.1.0"}}