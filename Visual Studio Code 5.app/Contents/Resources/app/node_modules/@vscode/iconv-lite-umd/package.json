{"name": "@vscode/iconv-lite-umd", "version": "0.7.0", "description": "Iconv-lite as UMD module", "main": "lib/iconv-lite-umd.js", "typings": "iconv-lite-umd.d.ts", "scripts": {"build": "webpack", "prepublish": "webpack", "test": "mocha --reporter spec --grep ."}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-iconv-lite-umd.git"}, "keywords": ["iconv-lite", "umd"], "author": "Microsoft", "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-iconv-lite-umd/issues"}, "homepage": "https://github.com/Microsoft/vscode-iconv-lite-umd#readme", "devDependencies": {"async": "^3.2.0", "c8": "^7.2.0", "errto": "^0.2.1", "iconv": "^2.3.5", "iconv-lite": "0.6.3", "mocha": "^3.5.3", "request": "^2.88.2", "semver": "^6.3.0", "unorm": "^1.6.0", "webpack": "^4.43.0", "webpack-cli": "^3.3.11"}}