{"name": "@vscode/proxy-agent", "version": "0.32.0", "description": "NodeJS http(s) agent implementation for VS Code", "main": "out/index.js", "types": "out/index.d.ts", "repository": {"type": "git", "url": "git://github.com/microsoft/vscode-proxy-agent.git"}, "keywords": ["proxy", "agent", "http", "https", "socks", "request", "access"], "authors": ["<PERSON> <<EMAIL>> (http://n8.io/)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "Microsoft Corporation"], "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-proxy-agent/issues"}, "homepage": "https://github.com/microsoft/vscode-proxy-agent", "dependencies": {"@tootallnate/once": "^3.0.0", "agent-base": "^7.0.1", "debug": "^4.3.4", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.2", "socks-proxy-agent": "^8.0.1", "undici": "^7.2.0"}, "devDependencies": {"@types/debug": "^4.1.9", "@types/node": "^20.8.4", "typescript": "^5.2.2"}, "scripts": {"compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "optionalDependencies": {"@vscode/windows-ca-certs": "^0.3.1"}}