{"information_for_contributors": ["This file has been converted from https://github.com/RedCMD/YAML-Syntax-Highlighter/blob/master/syntaxes/yaml-1.3.tmLanguage.json", "If you want to provide a fix or improvement, please create a pull request against the original repository.", "Once accepted there, we are happy to receive an update request."], "version": "https://github.com/RedCMD/YAML-Syntax-Highlighter/commit/274009903e20ac6dc37ba5763fb853744e28c9b2", "name": "YAML 1.3", "scopeName": "source.yaml.1.3", "comment": "https://spec.yaml.io/main/spec/1.3.0/", "patterns": [{"include": "source.yaml"}], "repository": {"directive-YAML": {"comment": "https://yaml.org/spec/1.2.2/#681-yaml-directives", "begin": "(?=%YAML[\t ]+1\\.3(?=[\r\n\t ]))", "end": "\\G(?=(?>\\.{3}|---)[\r\n\t ])", "name": "meta.1.3.yaml", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#681-yaml-directives", "begin": "\\G(%)(YAML)([\t ]+)(1\\.3)", "end": "\\G(?=---[\r\n\t ])", "beginCaptures": {"1": {"name": "punctuation.definition.directive.begin.yaml"}, "2": {"name": "keyword.other.directive.yaml.yaml"}, "3": {"name": "punctuation.whitespace.separator.yaml"}, "4": {"name": "constant.numeric.yaml-version.yaml"}}, "name": "meta.directives.yaml", "patterns": [{"include": "source.yaml.1.2#directive-invalid"}, {"include": "source.yaml.1.2#directives"}, {"include": "source.yaml.1.2#presentation-detail"}]}, {"include": "source.yaml.1.2#document"}]}}}