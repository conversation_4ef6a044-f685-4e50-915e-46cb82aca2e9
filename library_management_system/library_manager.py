"""
Library Management System
This module provides a simple command-line library management system
that stores book data in JSON format.
"""

import json


class Book:
    """Represents a book in the library system"""
    def __init__(self, id, title, author, year):
        """Initialize a book with given attributes"""
        self.id = id          # Unique identifier for the book
        self.title = title    # Title of the book
        self.author = author  # Author of the book
        self.year = year      # Publication year of the book

    def __str__(self):
        """String representation of the book for display purposes"""
        return f"ID: {self.id}, Title: {self.title}, Author: {self.author}, Publication Year: {self.year}"


class Library:
    """Main library class that manages a collection of books"""
    def __init__(self, file_path):
        """Initialize library with data from given JSON file path"""
        self.file_path = file_path  # Path to JSON data file
        self.books = self.load_books()  # Load existing books from file

    def load_books(self):
        """Load books from JSON file into memory"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
                # Convert JSON data to Book objects
                return [Book(book['id'], book['title'], book['author'], book['year']) for book in data]
        except (FileNotFoundError, json.JSONDecodeError):
            # Return empty list if file doesn't exist or is invalid
            return []

    def save_books(self):
        """Save current book collection to JSON file"""
        # Convert Book objects to dictionary format
        data = [{'id': book.id, 'title': book.title, 'author': book.author, 'year': book.year} 
                for book in self.books]
        with open(self.file_path, 'w', encoding='utf-8') as file:
            # Write data to file with pretty formatting
            json.dump(data, file, ensure_ascii=False, indent=4)

    def add_book(self, title, author, year):
        """Add a new book to the library"""
        # Generate new ID (max existing ID + 1)
        new_id = max([book.id for book in self.books], default=0) + 1
        new_book = Book(new_id, title, author, year)
        self.books.append(new_book)
        self.save_books()  # Persist changes
        print(f"The book '{title}' has been successfully added.")

    def remove_book(self, book_id):
        """Remove a book by its ID"""
        for book in self.books:
            if book.id == book_id:
                self.books.remove(book)
                self.save_books()  # Persist changes
                print(f"The book with ID {book_id} has been successfully removed.")
                return
        print(f"No book with ID {book_id} was found.")

    def search_book(self, keyword):
        """Search books by title or author keyword (case-insensitive)"""
        found_books = []
        for book in self.books:
            if keyword.lower() in book.title.lower() or keyword.lower() in book.author.lower():
                found_books.append(book)
        if found_books:
            for book in found_books:
                print(book)
        else:
            print(f"No books containing the keyword '{keyword}' were found.")

    def display_all_books(self):
        """Display all books in the library"""
        if self.books:
            for book in self.books:
                print(book)
        else:
            print("There are no books in the library.")


def main():
    """Main function that runs the library management system"""
    # Initialize library with data file
    library = Library('books.json')
    
    # Main program loop
    while True:
        print("\nLibrary Management System Menu:")
        print("1. Add a book")
        print("2. Remove a book")
        print("3. Search for a book")
        print("4. Display all books")
        print("5. Exit the system")
        
        # Get user input
        choice = input("Please enter your choice (1 - 5): ")

        # Process user choice
        if choice == '1':
            title = input("Please enter the book title: ")
            author = input("Please enter the book author: ")
            year = input("Please enter the publication year: ")
            library.add_book(title, author, year)
        elif choice == '2':
            book_id = int(input("Please enter the ID of the book to remove: "))
            library.remove_book(book_id)
        elif choice == '3':
            keyword = input("Please enter the search keyword: ")
            library.search_book(keyword)
        elif choice == '4':
            library.display_all_books()
        elif choice == '5':
            print("Thank you for using the library management system. Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")


if __name__ == "__main__":
    main()