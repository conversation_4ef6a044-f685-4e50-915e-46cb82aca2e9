#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图书管理系统

这是一个简单的图书管理系统，允许用户添加、删除、搜索和显示图书。
图书信息存储在JSON文件中，实现数据持久化。

主要功能:
- 添加新书籍
- 删除现有书籍
- 按标题或作者搜索书籍
- 显示所有书籍
- 数据持久化到JSON文件

作者: [您的姓名]
日期: [创建日期]
版本: 1.0
"""

import json


class Book:
    """
    书籍类，表示图书馆中的一本书

    这个类封装了书籍的基本信息，包括ID、标题、作者和出版年份。
    提供了字符串表示方法，便于显示书籍信息。

    属性:
        id (int): 书籍的唯一标识符
        title (str): 书籍标题
        author (str): 书籍作者
        year (int): 出版年份
    """

    def __init__(self, id, title, author, year):
        """
        初始化书籍对象

        参数:
            id (int): 书籍的唯一标识符
            title (str): 书籍标题
            author (str): 书籍作者
            year (int): 出版年份

        示例:
            >>> book = Book(1, "Python编程", "张三", 2023)
            >>> print(book.title)
            Python编程
        """
        self.id = id
        self.title = title
        self.author = author
        self.year = year

    def __str__(self):
        """
        返回书籍的字符串表示

        返回:
            str: 包含书籍所有信息的格式化字符串

        示例:
            >>> book = Book(1, "Python编程", "张三", 2023)
            >>> str(book)
            'ID: 1, Title: Python编程, Author: 张三, Publication Year: 2023'
        """
        return f"ID: {self.id}, Title: {self.title}, Author: {self.author}, Publication Year: {self.year}"
class Library:
    """
    图书馆类，管理书籍集合和相关操作

    这个类提供了完整的图书管理功能，包括添加、删除、搜索书籍，
    以及数据的持久化存储。所有书籍信息都存储在JSON文件中。

    属性:
        file_path (str): JSON文件的路径，用于数据持久化
        books (list): 存储Book对象的列表
    """

    def __init__(self, file_path):
        """
        初始化图书馆对象

        参数:
            file_path (str): JSON文件的路径，用于存储图书数据

        初始化过程:
            1. 设置文件路径
            2. 从文件加载现有的图书数据

        示例:
            >>> library = Library('books.json')
            >>> len(library.books)  # 显示当前图书数量
        """
        self.file_path = file_path
        self.books = self.load_books()  # 从文件加载现有数据

    def load_books(self):
        """
        从JSON文件加载图书数据

        返回:
            list: Book对象的列表

        异常处理:
            - FileNotFoundError: 如果文件不存在，返回空列表
            - json.JSONDecodeError: 如果JSON格式无效，返回空列表

        注意:
            这个方法在初始化时自动调用，也可以手动调用来重新加载数据
        """
        try:
            # 尝试打开并读取JSON文件
            with open(self.file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
                # 将JSON数据转换为Book对象列表
                return [Book(book['id'], book['title'], book['author'], book['year']) for book in data]
        except (FileNotFoundError, json.JSONDecodeError):
            # 如果文件不存在或JSON格式错误，返回空列表
            return []

    def save_books(self):
        """
        将当前的图书数据保存到JSON文件

        这个方法将内存中的Book对象列表转换为JSON格式并写入文件。
        每次添加或删除书籍后都会自动调用此方法。

        文件格式:
            JSON数组，每个元素包含书籍的id、title、author、year字段

        异常:
            可能抛出IOError如果文件写入失败
        """
        # 将Book对象列表转换为字典列表
        data = [{'id': book.id, 'title': book.title, 'author': book.author, 'year': book.year} for book in self.books]
        # 写入JSON文件，使用UTF-8编码和4空格缩进
        with open(self.file_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, ensure_ascii=False, indent=4)

    def add_book(self, title, author, year):
        """
        添加新书籍到图书馆

        参数:
            title (str): 书籍标题
            author (str): 书籍作者
            year (int): 出版年份

        功能:
            1. 自动生成新的唯一ID（比现有最大ID大1）
            2. 创建新的Book对象
            3. 添加到书籍列表
            4. 保存到文件
            5. 显示成功消息

        示例:
            >>> library.add_book("Python编程", "张三", 2023)
            The book 'Python编程' has been successfully added.
        """
        # 生成新的唯一ID：找到当前最大ID并加1，如果没有书籍则从1开始
        new_id = max([book.id for book in self.books], default=0) + 1
        # 创建新的Book对象
        new_book = Book(new_id, title, author, year)
        # 添加到书籍列表
        self.books.append(new_book)
        # 保存到文件
        self.save_books()
        # 显示成功消息
        print(f"The book '{title}' has been successfully added.")

    def remove_book(self, book_id):
        """
        根据ID删除书籍

        参数:
            book_id (int): 要删除的书籍ID

        功能:
            1. 在书籍列表中查找指定ID的书籍
            2. 如果找到，从列表中删除并保存到文件
            3. 显示相应的成功或失败消息

        返回:
            None: 找到并删除书籍时提前返回

        示例:
            >>> library.remove_book(1)
            The book with ID 1 has been successfully removed.
            >>> library.remove_book(999)
            No book with ID 999 was found.
        """
        # 遍历书籍列表查找指定ID的书籍
        for book in self.books:
            if book.id == book_id:
                # 找到书籍，从列表中删除
                self.books.remove(book)
                # 保存更新后的数据到文件
                self.save_books()
                # 显示成功消息
                print(f"The book with ID {book_id} has been successfully removed.")
                return  # 提前返回，避免执行后面的"未找到"消息

        # 如果循环结束都没有找到，显示未找到消息
        print(f"No book with ID {book_id} was found.")

    def search_book(self, keyword):
        """
        根据关键词搜索书籍

        参数:
            keyword (str): 搜索关键词

        功能:
            1. 在书籍标题和作者中搜索关键词（不区分大小写）
            2. 收集所有匹配的书籍
            3. 显示找到的书籍或"未找到"消息

        搜索范围:
            - 书籍标题
            - 书籍作者

        注意:
            搜索是不区分大小写的，支持部分匹配

        示例:
            >>> library.search_book("Python")
            ID: 1, Title: Python编程, Author: 张三, Publication Year: 2023
            >>> library.search_book("不存在")
            No books containing the keyword '不存在' were found.
        """
        found_books = []  # 存储找到的书籍

        # 遍历所有书籍进行搜索
        for book in self.books:
            # 在标题和作者中搜索关键词（转换为小写进行不区分大小写的比较）
            if keyword.lower() in book.title.lower() or keyword.lower() in book.author.lower():
                found_books.append(book)

        # 显示搜索结果
        if found_books:
            # 如果找到书籍，逐一显示
            for book in found_books:
                print(book)
        else:
            # 如果没有找到，显示未找到消息
            print(f"No books containing the keyword '{keyword}' were found.")

    def display_all_books(self):
        """
        显示图书馆中的所有书籍

        功能:
            1. 检查图书馆是否有书籍
            2. 如果有书籍，逐一显示所有书籍信息
            3. 如果没有书籍，显示空图书馆消息

        示例:
            >>> library.display_all_books()
            ID: 1, Title: Python编程, Author: 张三, Publication Year: 2023
            ID: 2, Title: Java编程, Author: 李四, Publication Year: 2022

            # 或者如果图书馆为空：
            There are no books in the library.
        """
        if self.books:
            # 如果图书馆有书籍，显示所有书籍
            for book in self.books:
                print(book)
        else:
            # 如果图书馆为空，显示相应消息
            print("There are no books in the library.")


def main():
    library = Library('books.json')
    while True:
        print("\nLibrary Management System Menu:")
        print("1. Add a book")
        print("2. Remove a book")
        print("3. Search for a book")
        print("4. Display all books")
        print("5. Exit the system")
        choice = input("Please enter your choice (1 - 5): ")

        if choice == '1':
            title = input("Please enter the book title: ")
            author = input("Please enter the book author: ")
            year = input("Please enter the publication year: ")
            library.add_book(title, author, year)
        elif choice == '2':
            book_id = int(input("Please enter the ID of the book to remove: "))
            library.remove_book(book_id)
        elif choice == '3':
            keyword = input("Please enter the search keyword: ")
            library.search_book(keyword)
        elif choice == '4':
            library.display_all_books()
        elif choice == '5':
            print("Thank you for using the library management system. Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")


if __name__ == "__main__":
    main()