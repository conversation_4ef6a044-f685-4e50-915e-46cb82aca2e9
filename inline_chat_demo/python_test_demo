import json


class Book:
    def __init__(self, id, title, author, year):
        self.id = id
        self.title = title
        self.author = author
        self.year = year

    def __str__(self):
        return f"ID: {self.id}, Title: {self.title}, Author: {self.author}, Publication Year: {self.year}"
class Library:
    def __init__(self, file_path):
        self.file_path = file_path
        self.books = self.load_books()

    def load_books(self):
        try:
            with open(self.file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
                return [Book(book['id'], book['title'], book['author'], book['year']) for book in data]
        except (FileNotFoundError, json.JSONDecodeError):
            return []

    def save_books(self):
        data = [{'id': book.id, 'title': book.title, 'author': book.author, 'year': book.year} for book in self.books]
        with open(self.file_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, ensure_ascii=False, indent=4)

    def add_book(self, title, author, year):
        new_id = max([book.id for book in self.books], default=0) + 1
        new_book = Book(new_id, title, author, year)
        self.books.append(new_book)
        self.save_books()
        print(f"The book '{title}' has been successfully added.")

    def remove_book(self, book_id):
        for book in self.books:
            if book.id == book_id:
                self.books.remove(book)
                self.save_books()
                print(f"The book with ID {book_id} has been successfully removed.")
                return
        print(f"No book with ID {book_id} was found.")

    def search_book(self, keyword):
        found_books = []
        for book in self.books:
            if keyword.lower() in book.title.lower() or keyword.lower() in book.author.lower():
                found_books.append(book)
        if found_books:
            for book in found_books:
                print(book)
        else:
            print(f"No books containing the keyword '{keyword}' were found.")

    def display_all_books(self):
        if self.books:
            for book in self.books:
                print(book)
        else:
            print("There are no books in the library.")


def main():
    library = Library('books.json')
    while True:
        print("\nLibrary Management System Menu:")
        print("1. Add a book")
        print("2. Remove a book")
        print("3. Search for a book")
        print("4. Display all books")
        print("5. Exit the system")
        choice = input("Please enter your choice (1 - 5): ")

        if choice == '1':
            title = input("Please enter the book title: ")
            author = input("Please enter the book author: ")
            year = input("Please enter the publication year: ")
            library.add_book(title, author, year)
        elif choice == '2':
            book_id = int(input("Please enter the ID of the book to remove: "))
            library.remove_book(book_id)
        elif choice == '3':
            keyword = input("Please enter the search keyword: ")
            library.search_book(keyword)
        elif choice == '4':
            library.display_all_books()
        elif choice == '5':
            print("Thank you for using the library management system. Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")


if __name__ == "__main__":
    main()