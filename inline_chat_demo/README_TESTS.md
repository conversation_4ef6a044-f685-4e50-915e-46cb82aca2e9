# 测试用例说明

本目录包含了为两个Python项目生成的测试用例：

## 项目结构

```
inline_chat_demo/
├── python_test2.py                 # 计算器程序
├── python_test_demo.py             # 图书管理系统
├── test_python_test2.py            # 计算器测试用例
├── test_python_test_demo.py        # 图书管理系统测试用例
├── run_all_tests.py                # 运行所有测试的脚本
└── README_TESTS.md                 # 本说明文件
```

## 测试覆盖范围

### 1. 计算器程序测试 (test_python_test2.py)

**测试的功能模块：**
- `TestCalculatorFunctions`: 测试基本数学运算函数
  - `test_add()`: 测试加法函数，包括正数、负数、零和小数
  - `test_subtract()`: 测试减法函数
  - `test_multiply()`: 测试乘法函数
  - `test_divide()`: 测试除法函数，包括除零错误处理

- `TestCalculatorInterface`: 测试用户界面交互
  - `test_calculator_addition()`: 测试加法操作的完整流程
  - `test_calculator_subtraction()`: 测试减法操作
  - `test_calculator_multiplication()`: 测试乘法操作
  - `test_calculator_division()`: 测试除法操作
  - `test_calculator_division_by_zero()`: 测试除零错误处理
  - `test_calculator_invalid_choice()`: 测试无效输入处理
  - `test_calculator_multiple_operations()`: 测试多次计算操作

### 2. 图书管理系统测试 (test_python_test_demo.py)

**测试的功能模块：**
- `TestBook`: 测试Book类
  - `test_book_creation()`: 测试书籍对象创建
  - `test_book_str_representation()`: 测试书籍字符串表示

- `TestLibrary`: 测试Library类
  - `test_library_creation_with_empty_file()`: 测试创建空图书馆
  - `test_library_creation_with_existing_data()`: 测试从现有数据创建图书馆
  - `test_add_book()`: 测试添加书籍功能
  - `test_add_multiple_books()`: 测试添加多本书籍
  - `test_remove_book_existing()`: 测试删除存在的书籍
  - `test_remove_book_nonexistent()`: 测试删除不存在的书籍
  - `test_search_book_by_title()`: 测试按标题搜索
  - `test_search_book_by_author()`: 测试按作者搜索
  - `test_search_book_not_found()`: 测试搜索不存在的书籍
  - `test_display_all_books_empty()`: 测试显示空图书馆
  - `test_display_all_books_with_data()`: 测试显示有书籍的图书馆
  - `test_save_and_load_books()`: 测试数据持久化
  - `test_load_books_file_not_found()`: 测试文件不存在的情况
  - `test_load_books_invalid_json()`: 测试无效JSON文件处理

## 运行测试

### 方法1：运行所有测试
```bash
cd inline_chat_demo
python run_all_tests.py
```

### 方法2：单独运行计算器测试
```bash
cd inline_chat_demo
python -m unittest test_python_test2.py -v
```

### 方法3：单独运行图书管理系统测试
```bash
cd inline_chat_demo
python -m unittest test_python_test_demo.py -v
```

### 方法4：运行特定测试类
```bash
cd inline_chat_demo
python -m unittest test_python_test2.TestCalculatorFunctions -v
python -m unittest test_python_test_demo.TestLibrary -v
```

### 方法5：运行特定测试方法
```bash
cd inline_chat_demo
python -m unittest test_python_test2.TestCalculatorFunctions.test_add -v
python -m unittest test_python_test_demo.TestLibrary.test_add_book -v
```

## 测试特点

### 使用的测试技术：
1. **单元测试框架**: 使用Python标准库的`unittest`
2. **模拟技术**: 使用`unittest.mock`来模拟用户输入和输出
3. **临时文件**: 为图书管理系统创建临时文件进行测试
4. **异常处理测试**: 测试各种边界条件和错误情况
5. **输出验证**: 验证程序的输出是否符合预期

### 测试覆盖的场景：
- ✅ 正常功能测试
- ✅ 边界条件测试
- ✅ 错误处理测试
- ✅ 用户交互测试
- ✅ 数据持久化测试
- ✅ 文件操作异常测试

## 预期结果

运行测试后，您应该看到类似以下的输出：

```
✓ 已加载计算器测试用例
✓ 已加载图书管理系统测试用例

test_add (test_python_test2.TestCalculatorFunctions) ... ok
test_divide (test_python_test2.TestCalculatorFunctions) ... ok
test_multiply (test_python_test2.TestCalculatorFunctions) ... ok
test_subtract (test_python_test2.TestCalculatorFunctions) ... ok
...

==================================================
测试结果摘要:
运行的测试数量: XX
失败的测试数量: 0
错误的测试数量: 0

🎉 所有测试都通过了！
```

## 注意事项

1. 确保Python环境已正确安装
2. 测试文件需要与被测试的源文件在同一目录下
3. 图书管理系统的测试会创建临时文件，测试完成后会自动清理
4. 如果测试失败，请检查源代码是否有语法错误或逻辑问题
