import unittest
import sys
import os
from io import StringIO
from unittest.mock import patch

# 添加当前目录到Python路径，以便导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入要测试的函数
from python_test2 import add, subtract, multiply, divide, calculator


class TestCalculatorFunctions(unittest.TestCase):
    """测试计算器的基本数学运算函数"""
    
    def test_add(self):
        """测试加法函数"""
        self.assertEqual(add(2, 3), 5)
        self.assertEqual(add(-1, 1), 0)
        self.assertEqual(add(0, 0), 0)
        self.assertEqual(add(-5, -3), -8)
        self.assertAlmostEqual(add(0.1, 0.2), 0.3, places=7)
    
    def test_subtract(self):
        """测试减法函数"""
        self.assertEqual(subtract(5, 3), 2)
        self.assertEqual(subtract(1, 1), 0)
        self.assertEqual(subtract(-1, -1), 0)
        self.assertEqual(subtract(0, 5), -5)
        self.assertAlmostEqual(subtract(0.3, 0.1), 0.2, places=7)
    
    def test_multiply(self):
        """测试乘法函数"""
        self.assertEqual(multiply(3, 4), 12)
        self.assertEqual(multiply(-2, 3), -6)
        self.assertEqual(multiply(0, 100), 0)
        self.assertEqual(multiply(-1, -1), 1)
        self.assertAlmostEqual(multiply(0.1, 0.2), 0.02, places=7)
    
    def test_divide(self):
        """测试除法函数"""
        self.assertEqual(divide(10, 2), 5)
        self.assertEqual(divide(-6, 3), -2)
        self.assertEqual(divide(0, 5), 0)
        self.assertAlmostEqual(divide(1, 3), 0.3333333333333333, places=7)
        
        # 测试除零错误
        self.assertEqual(divide(5, 0), "错误：除数不能为零")
        self.assertEqual(divide(-5, 0), "错误：除数不能为零")
        self.assertEqual(divide(0, 0), "错误：除数不能为零")


class TestCalculatorInterface(unittest.TestCase):
    """测试计算器的用户界面"""
    
    @patch('builtins.input')
    @patch('sys.stdout', new_callable=StringIO)
    def test_calculator_addition(self, mock_stdout, mock_input):
        """测试计算器加法操作"""
        # 模拟用户输入：选择1（加法），输入5和3，然后选择no退出
        mock_input.side_effect = ['1', '5', '3', 'no']
        
        calculator()
        
        output = mock_stdout.getvalue()
        self.assertIn("5.0 + 3.0 = 8.0", output)
    
    @patch('builtins.input')
    @patch('sys.stdout', new_callable=StringIO)
    def test_calculator_subtraction(self, mock_stdout, mock_input):
        """测试计算器减法操作"""
        mock_input.side_effect = ['2', '10', '4', 'no']
        
        calculator()
        
        output = mock_stdout.getvalue()
        self.assertIn("10.0 - 4.0 = 6.0", output)
    
    @patch('builtins.input')
    @patch('sys.stdout', new_callable=StringIO)
    def test_calculator_multiplication(self, mock_stdout, mock_input):
        """测试计算器乘法操作"""
        mock_input.side_effect = ['3', '7', '8', 'no']
        
        calculator()
        
        output = mock_stdout.getvalue()
        self.assertIn("7.0 * 8.0 = 56.0", output)
    
    @patch('builtins.input')
    @patch('sys.stdout', new_callable=StringIO)
    def test_calculator_division(self, mock_stdout, mock_input):
        """测试计算器除法操作"""
        mock_input.side_effect = ['4', '15', '3', 'no']
        
        calculator()
        
        output = mock_stdout.getvalue()
        self.assertIn("15.0 / 3.0 = 5.0", output)
    
    @patch('builtins.input')
    @patch('sys.stdout', new_callable=StringIO)
    def test_calculator_division_by_zero(self, mock_stdout, mock_input):
        """测试计算器除零操作"""
        mock_input.side_effect = ['4', '10', '0', 'no']
        
        calculator()
        
        output = mock_stdout.getvalue()
        self.assertIn("错误：除数不能为零", output)
    
    @patch('builtins.input')
    @patch('sys.stdout', new_callable=StringIO)
    def test_calculator_invalid_choice(self, mock_stdout, mock_input):
        """测试计算器无效选择"""
        mock_input.side_effect = ['5', '1', '2', '3', 'no']
        
        calculator()
        
        output = mock_stdout.getvalue()
        self.assertIn("无效输入，请重新选择。", output)
    
    @patch('builtins.input')
    @patch('sys.stdout', new_callable=StringIO)
    def test_calculator_multiple_operations(self, mock_stdout, mock_input):
        """测试计算器多次操作"""
        mock_input.side_effect = ['1', '2', '3', 'yes', '2', '10', '5', 'no']
        
        calculator()
        
        output = mock_stdout.getvalue()
        self.assertIn("2.0 + 3.0 = 5.0", output)
        self.assertIn("10.0 - 5.0 = 5.0", output)


if __name__ == '__main__':
    unittest.main()
