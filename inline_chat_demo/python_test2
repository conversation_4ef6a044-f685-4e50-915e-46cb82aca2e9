#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单计算器程序

这个程序提供了基本的数学运算功能，包括加法、减法、乘法和除法。
用户可以通过交互式菜单选择运算类型并输入数字进行计算。

作者: [您的姓名]
日期: [创建日期]
版本: 1.0
"""


def add(x, y):
    """
    执行两个数的加法运算

    参数:
        x (float): 第一个加数
        y (float): 第二个加数

    返回:
        float: 两数之和

    示例:
        >>> add(3, 5)
        8
        >>> add(-2, 7)
        5
    """
    return x + y


def subtract(x, y):
    """
    执行两个数的减法运算

    参数:
        x (float): 被减数
        y (float): 减数

    返回:
        float: 两数之差

    示例:
        >>> subtract(10, 3)
        7
        >>> subtract(5, 8)
        -3
    """
    return x - y


def multiply(x, y):
    """
    执行两个数的乘法运算

    参数:
        x (float): 第一个乘数
        y (float): 第二个乘数

    返回:
        float: 两数之积

    示例:
        >>> multiply(4, 6)
        24
        >>> multiply(-3, 5)
        -15
    """
    return x * y


def divide(x, y):
    """
    执行两个数的除法运算

    参数:
        x (float): 被除数
        y (float): 除数

    返回:
        float or str: 如果除数不为零，返回商；否则返回错误信息

    示例:
        >>> divide(12, 3)
        4.0
        >>> divide(7, 2)
        3.5
        >>> divide(5, 0)
        '错误：除数不能为零'
    """
    # 检查除数是否为零，避免除零错误
    if y == 0:
        return "错误：除数不能为零"
    return x / y

def calculator():
    """
    主计算器函数，提供交互式用户界面

    这个函数显示菜单选项，接收用户输入，并执行相应的数学运算。
    用户可以连续进行多次计算，直到选择退出。

    功能:
        - 显示运算选项菜单
        - 接收用户的运算选择
        - 获取两个操作数
        - 调用相应的运算函数
        - 显示计算结果
        - 询问是否继续计算

    异常处理:
        - 处理无效的菜单选择
        - 通过divide函数处理除零错误
        - 可能的ValueError（如果用户输入非数字）会导致程序崩溃
    """
    # 显示计算器菜单
    print("选择运算:")
    print("1. 加")
    print("2. 减")
    print("3. 乘")
    print("4. 除")

    # 主循环，持续接收用户输入直到用户选择退出
    while True:
        # 获取用户的运算选择
        choice = input("输入你的选择 (1/2/3/4): ")

        # 验证用户输入是否为有效选项
        if choice in ['1', '2', '3', '4']:
            # 获取两个操作数
            # 注意：这里可能会抛出ValueError如果用户输入非数字
            num1 = float(input("输入第一个数字: "))
            num2 = float(input("输入第二个数字: "))

            # 根据用户选择执行相应的运算
            if choice == '1':
                # 执行加法运算并显示结果
                print(f"{num1} + {num2} = {add(num1, num2)}")
            elif choice == '2':
                # 执行减法运算并显示结果
                print(f"{num1} - {num2} = {subtract(num1, num2)}")
            elif choice == '3':
                # 执行乘法运算并显示结果
                print(f"{num1} * {num2} = {multiply(num1, num2)}")
            elif choice == '4':
                # 执行除法运算并显示结果（包含除零检查）
                print(f"{num1} / {num2} = {divide(num1, num2)}")
        else:
            # 处理无效的菜单选择
            print("无效输入，请重新选择。")

        # 询问用户是否继续进行计算
        next_calculation = input("是否进行下一次计算？(yes/no): ")
        # 如果用户输入不是'yes'（不区分大小写），则退出循环
        if next_calculation.lower() != 'yes':
            break


# 程序入口点
if __name__ == "__main__":
    """
    程序的主入口点

    当脚本直接运行时（而不是被导入时），执行calculator函数
    这是Python的标准做法，允许模块既可以作为脚本运行，也可以被其他模块导入
    """
    calculator()
