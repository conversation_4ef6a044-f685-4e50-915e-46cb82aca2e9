#!/usr/bin/env python3
"""
运行所有测试用例的脚本
"""

import unittest
import sys
import os

def run_all_tests():
    """运行所有测试用例"""
    # 添加当前目录到Python路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 加载测试模块
    try:
        # 加载计算器测试
        from test_python_test2 import TestCalculatorFunctions, TestCalculatorInterface
        suite.addTests(loader.loadTestsFromTestCase(TestCalculatorFunctions))
        suite.addTests(loader.loadTestsFromTestCase(TestCalculatorInterface))
        print("✓ 已加载计算器测试用例")
    except ImportError as e:
        print(f"✗ 无法加载计算器测试用例: {e}")
    
    try:
        # 加载图书管理系统测试
        from test_python_test_demo import TestBook, TestLibrary
        suite.addTests(loader.loadTestsFromTestCase(TestBook))
        suite.addTests(loader.loadTestsFromTestCase(TestLibrary))
        print("✓ 已加载图书管理系统测试用例")
    except ImportError as e:
        print(f"✗ 无法加载图书管理系统测试用例: {e}")
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "="*50)
    print("测试结果摘要:")
    print(f"运行的测试数量: {result.testsRun}")
    print(f"失败的测试数量: {len(result.failures)}")
    print(f"错误的测试数量: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    if result.wasSuccessful():
        print("\n🎉 所有测试都通过了！")
        return 0
    else:
        print("\n❌ 有测试失败或出错")
        return 1

if __name__ == '__main__':
    exit_code = run_all_tests()
    sys.exit(exit_code)
