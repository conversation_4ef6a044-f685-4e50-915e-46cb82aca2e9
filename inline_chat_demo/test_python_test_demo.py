import unittest
import json
import os
import tempfile
import sys
from io import StringIO
from unittest.mock import patch, mock_open

# 添加当前目录到Python路径，以便导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入要测试的类
from python_test_demo import Book, Library


class TestBook(unittest.TestCase):
    """测试Book类"""
    
    def setUp(self):
        """设置测试数据"""
        self.book = Book(1, "Python编程", "张三", 2023)
    
    def test_book_creation(self):
        """测试书籍对象创建"""
        self.assertEqual(self.book.id, 1)
        self.assertEqual(self.book.title, "Python编程")
        self.assertEqual(self.book.author, "张三")
        self.assertEqual(self.book.year, 2023)
    
    def test_book_str_representation(self):
        """测试书籍字符串表示"""
        expected = "ID: 1, Title: Python编程, Author: 张三, Publication Year: 2023"
        self.assertEqual(str(self.book), expected)


class TestLibrary(unittest.TestCase):
    """测试Library类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建临时文件用于测试
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json')
        self.temp_file.close()
        self.library = Library(self.temp_file.name)
    
    def tearDown(self):
        """清理测试环境"""
        # 删除临时文件
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_library_creation_with_empty_file(self):
        """测试创建空图书馆"""
        self.assertEqual(len(self.library.books), 0)
    
    def test_library_creation_with_existing_data(self):
        """测试从现有数据创建图书馆"""
        # 准备测试数据
        test_data = [
            {"id": 1, "title": "Python编程", "author": "张三", "year": 2023},
            {"id": 2, "title": "Java编程", "author": "李四", "year": 2022}
        ]
        
        # 写入测试数据
        with open(self.temp_file.name, 'w', encoding='utf-8') as f:
            json.dump(test_data, f)
        
        # 重新创建图书馆对象
        library = Library(self.temp_file.name)
        
        self.assertEqual(len(library.books), 2)
        self.assertEqual(library.books[0].title, "Python编程")
        self.assertEqual(library.books[1].title, "Java编程")
    
    def test_add_book(self):
        """测试添加书籍"""
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            self.library.add_book("测试书籍", "测试作者", 2024)
        
        self.assertEqual(len(self.library.books), 1)
        self.assertEqual(self.library.books[0].title, "测试书籍")
        self.assertEqual(self.library.books[0].author, "测试作者")
        self.assertEqual(self.library.books[0].year, 2024)
        self.assertEqual(self.library.books[0].id, 1)
        
        output = mock_stdout.getvalue()
        self.assertIn("The book '测试书籍' has been successfully added.", output)
    
    def test_add_multiple_books(self):
        """测试添加多本书籍"""
        with patch('sys.stdout', new_callable=StringIO):
            self.library.add_book("书籍1", "作者1", 2023)
            self.library.add_book("书籍2", "作者2", 2024)
        
        self.assertEqual(len(self.library.books), 2)
        self.assertEqual(self.library.books[0].id, 1)
        self.assertEqual(self.library.books[1].id, 2)
    
    def test_remove_book_existing(self):
        """测试删除存在的书籍"""
        # 先添加一本书
        with patch('sys.stdout', new_callable=StringIO):
            self.library.add_book("待删除书籍", "作者", 2023)
        
        # 删除书籍
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            self.library.remove_book(1)
        
        self.assertEqual(len(self.library.books), 0)
        output = mock_stdout.getvalue()
        self.assertIn("The book with ID 1 has been successfully removed.", output)
    
    def test_remove_book_nonexistent(self):
        """测试删除不存在的书籍"""
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            self.library.remove_book(999)
        
        output = mock_stdout.getvalue()
        self.assertIn("No book with ID 999 was found.", output)
    
    def test_search_book_by_title(self):
        """测试按标题搜索书籍"""
        # 添加测试书籍
        with patch('sys.stdout', new_callable=StringIO):
            self.library.add_book("Python编程入门", "张三", 2023)
            self.library.add_book("Java高级编程", "李四", 2022)
        
        # 搜索书籍
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            self.library.search_book("Python")
        
        output = mock_stdout.getvalue()
        self.assertIn("Python编程入门", output)
        self.assertNotIn("Java高级编程", output)
    
    def test_search_book_by_author(self):
        """测试按作者搜索书籍"""
        # 添加测试书籍
        with patch('sys.stdout', new_callable=StringIO):
            self.library.add_book("Python编程", "张三", 2023)
            self.library.add_book("数据结构", "张三", 2022)
            self.library.add_book("Java编程", "李四", 2021)
        
        # 搜索书籍
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            self.library.search_book("张三")
        
        output = mock_stdout.getvalue()
        self.assertIn("Python编程", output)
        self.assertIn("数据结构", output)
        self.assertNotIn("Java编程", output)
    
    def test_search_book_not_found(self):
        """测试搜索不存在的书籍"""
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            self.library.search_book("不存在的书")
        
        output = mock_stdout.getvalue()
        self.assertIn("No books containing the keyword '不存在的书' were found.", output)
    
    def test_display_all_books_empty(self):
        """测试显示空图书馆的所有书籍"""
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            self.library.display_all_books()
        
        output = mock_stdout.getvalue()
        self.assertIn("There are no books in the library.", output)
    
    def test_display_all_books_with_data(self):
        """测试显示有书籍的图书馆"""
        # 添加测试书籍
        with patch('sys.stdout', new_callable=StringIO):
            self.library.add_book("书籍1", "作者1", 2023)
            self.library.add_book("书籍2", "作者2", 2024)
        
        # 显示所有书籍
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            self.library.display_all_books()
        
        output = mock_stdout.getvalue()
        self.assertIn("书籍1", output)
        self.assertIn("书籍2", output)
        self.assertIn("作者1", output)
        self.assertIn("作者2", output)
    
    def test_save_and_load_books(self):
        """测试保存和加载书籍数据"""
        # 添加书籍
        with patch('sys.stdout', new_callable=StringIO):
            self.library.add_book("测试书籍", "测试作者", 2023)
        
        # 创建新的图书馆实例来测试加载
        new_library = Library(self.temp_file.name)
        
        self.assertEqual(len(new_library.books), 1)
        self.assertEqual(new_library.books[0].title, "测试书籍")
        self.assertEqual(new_library.books[0].author, "测试作者")
        self.assertEqual(new_library.books[0].year, 2023)
    
    def test_load_books_file_not_found(self):
        """测试加载不存在的文件"""
        library = Library("nonexistent_file.json")
        self.assertEqual(len(library.books), 0)
    
    def test_load_books_invalid_json(self):
        """测试加载无效的JSON文件"""
        # 写入无效的JSON数据
        with open(self.temp_file.name, 'w') as f:
            f.write("invalid json content")
        
        library = Library(self.temp_file.name)
        self.assertEqual(len(library.books), 0)


if __name__ == '__main__':
    unittest.main()
